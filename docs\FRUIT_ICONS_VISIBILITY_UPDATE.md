# تحديث رؤية أيقونات الفاكهة

## 🎯 التغيير المطلوب

إخفاء أيقونات الفاكهة وجعلها تظهر بشكل شفاف عند النقر عليها في فترة الرهان.

## ✅ التغييرات المطبقة

### **1. إخفاء الأيقونات افتراضياً:**
- **الملف**: `src/components/BettingControlsSimple.tsx`
- **التغيير**: `opacity: 1` → `opacity: 0`
- **النتيجة**: الأيقونات مخفية تماماً

### **2. منطق الإظهار الشفاف:**
```typescript
const hasBet = betsOnTypes[symbol as keyof BetsOnTypes] > 0;
const shouldShow = isPressed || hasBet; // إظهار عند النقر أو عند وجود رهان
```

### **3. إظهار شفاف عند التفاعل:**
- **عند النقر**: `opacity: 0.7` (شفاف)
- **عند وجود رهان**: `opacity: 0.7` (شفاف)
- **في الحالات الأخرى**: `opacity: 0` (مخفي تماماً)

## 🎨 التفاصيل التقنية

### **المنطق الجديد:**
```typescript
// إظهار الأيقونة عند:
// 1. النقر عليها (isPressed)
// 2. وجود رهان عليها (hasBet)
const shouldShow = isPressed || hasBet;

// مستوى الشفافية:
opacity: shouldShow ? 0.7 : 0
```

### **العناصر المتأثرة:**
1. **الحاوية الرئيسية**: `opacity: shouldShow ? 0.7 : 0`
2. **صورة BAR**: `opacity: shouldShow ? 0.7 : 0`
3. **صور الفواكه الأخرى**: `opacity: shouldShow ? 0.7 : 0`

## 🎮 تجربة المستخدم

### **الحالة الافتراضية:**
- **الأيقونات**: مخفية تماماً
- **الوظيفة**: قابلة للنقر
- **المظهر**: نظيف ومرتب

### **عند النقر:**
- **الأيقونات**: تظهر بشكل شفاف (70%)
- **الانيميشن**: توهج ذهبي
- **التفاعل**: فوري وواضح

### **عند وجود رهان:**
- **الأيقونات**: تبقى شفافة (70%)
- **المؤشر**: يوضح وجود رهان
- **الوضوح**: يمكن رؤية الرهان

## 🔧 الفوائد

### **1. واجهة أنظف:**
- **تقليل الفوضى البصرية**
- **تركيز على العناصر المهمة**
- **تجربة مستخدم محسنة**

### **2. تفاعل أفضل:**
- **وضوح عند النقر**
- **مؤشرات بصرية واضحة**
- **استجابة فورية**

### **3. أداء محسن:**
- **تقليل العناصر المرئية**
- **تحسين الأداء**
- **استهلاك أقل للموارد**

## 📱 التوافق

### **جميع الأجهزة:**
- ✅ **iPhone 14**: متوافق
- ✅ **Samsung Galaxy**: متوافق
- ✅ **Desktop**: متوافق
- ✅ **Tablet**: متوافق

### **جميع مراحل اللعبة:**
- ✅ **فترة الرهان**: الأيقونات مخفية، تظهر عند النقر
- ✅ **دوران الإضاءة**: الأيقونات مخفية
- ✅ **عرض النتائج**: الأيقونات مخفية

## 🎯 النتيجة النهائية

- **الأيقونات مخفية افتراضياً**
- **تظهر بشكل شفاف عند النقر**
- **تبقى شفافة عند وجود رهان**
- **واجهة أنظف وأكثر تركيزاً**
- **تجربة مستخدم محسنة** 