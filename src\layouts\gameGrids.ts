// تعريف الأنواع
interface DeviceConfig {
  name: string;
  width: number;
  height: number;
  backgroundImage: string;
}

interface Position {
  left: string;
  top: string;
  zIndex?: number;
}

interface LayerConfig {
  background: number;
  grid: number;
  symbols: number;
  buttons: number;
  overlay: number;
}

interface GridPosition extends Position {
  width: string;
  height: string;
}

interface GridSquare {
  gridIndex: number;
  symbol: string;
  type: 'normal' | 'halfFruit' | 'stackedBar' | 'inner';
}

interface GameGridLayout {
  device: DeviceConfig;
  layers: LayerConfig;
  gameGrid: {
    position: GridPosition;
    squares: GridSquare[];
  };
  elements: {
    symbolButtons: {
      [key: string]: Position & { name: string };
    };
    amountButtons: {
      [key: string]: Position & { value: number };
    };
    actionButtons: {
      [key: string]: Position & { action: string };
    };
    displayElements: {
      [key: string]: Position & { type: string };
    };
    betRectangles: {
      [key: string]: Position & { symbol: string };
    };
  };
}

// استيراد التخطيطات من مجلد New
// @ts-ignore
import desktopLayout from '../../New/desktop-layout.js';
// @ts-ignore
import ipadLayout from '../../New/ipad-layout.js';
// @ts-ignore
import iphone14promaxLayout from '../../New/iphone-14-pro-max-layout.js';
// @ts-ignore
import galaxyLayout from '../../New/galaxy-layout.js';
// @ts-ignore
import galaxy360x740Layout from '../../New/galaxy-360x740-layout.js';
// @ts-ignore
import iphone12Layout from '../../New/iphone-12-layout.js';
// @ts-ignore
import iphoneseLayout from '../../New/iphone-se-layout.js';

// تعريف التخطيطات لكل الأجهزة (من ملفات New)
const deviceLayouts: { [key: string]: any } = {
  'desktop': desktopLayout,
  'ipad': ipadLayout,
  'iphone-14-pro-max': iphone14promaxLayout,
  'galaxy': galaxyLayout,
  'galaxy-360x740': galaxy360x740Layout,
  'iphone-12': iphone12Layout,
  'iphone-se': iphoneseLayout,
};

// دالة للحصول على أقرب تخطيط للشاشة الحالية
export function getLayoutForScreen(width: number, height: number): any {
  // للاختبار - إجبار استخدام Galaxy 360x740
  if (width === 360 && height === 740) {
    console.log('Using Galaxy 360x740 layout');
    return deviceLayouts['galaxy-360x740'];
  }
  console.log('Using default Galaxy layout');
  return deviceLayouts['galaxy'];
  
  // الكود الأصلي (معلق مؤقتاً)
  /*
  const ratio = width / height;
  if (ratio >= 16/9) {
    return deviceLayouts['desktop'];
  }
  if (width >= 768) {
    return deviceLayouts['ipad'];
  }
  if (width >= 428) {
    return deviceLayouts['iphone-14-pro-max'];
  } else if (width >= 412) {
    return deviceLayouts['galaxy'];
  } else if (width >= 390) {
    return deviceLayouts['iphone-12'];
  }
  return deviceLayouts['iphone-se'];
  */
}

// Hook لاستخدام التخطيط التفاعلي
import { useState, useEffect } from 'react';

export function useResponsiveLayout() {
  const [layout, setLayout] = useState<GameGridLayout>(() => 
    getLayoutForScreen(window.innerWidth, window.innerHeight)
  );

  useEffect(() => {
    const handleResize = () => {
      setLayout(getLayoutForScreen(window.innerWidth, window.innerHeight));
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return layout;
}

// مثال على الاستخدام في المكون:
/**
 * كيفية استخدام التخطيطات في المكون:
 * 
 * import { useResponsiveLayout } from './layouts/gameGrids';
 * 
 * function GameComponent() {
 *   const layout = useResponsiveLayout();
 *   // ... استخدم layout كما تريد
 * }
 */
