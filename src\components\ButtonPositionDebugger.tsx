import React, { useState, useEffect } from 'react';
import { BUTTON_POSITIONS } from '../utils/buttonPositions';

interface ButtonPositionDebuggerProps {
  isVisible: boolean;
  onToggle: () => void;
}

const ButtonPositionDebugger: React.FC<ButtonPositionDebuggerProps> = ({
  isVisible,
  onToggle
}) => {
  const [deviceInfo, setDeviceInfo] = useState('');
  const [currentConfig, setCurrentConfig] = useState<any>(null);

  useEffect(() => {
    const updateDeviceInfo = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      
      // البحث عن التكوين المناسب
      let deviceConfig = BUTTON_POSITIONS.find(d => 
        Math.abs(d.device.width - width) < 50 && Math.abs(d.device.height - height) < 50
      );
      
      if (!deviceConfig) {
        if (width <= 768) {
          deviceConfig = BUTTON_POSITIONS.find(d => d.device.width <= 768) || BUTTON_POSITIONS[0];
        } else {
          deviceConfig = BUTTON_POSITIONS.find(d => d.device.width > 768) || BUTTON_POSITIONS[1] || BUTTON_POSITIONS[0];
        }
      }

      setDeviceInfo(`${width}×${height} → ${deviceConfig.device.name}`);
      setCurrentConfig(deviceConfig);
    };

    updateDeviceInfo();
    window.addEventListener('resize', updateDeviceInfo);
    return () => window.removeEventListener('resize', updateDeviceInfo);
  }, []);

  if (!isVisible) {
    return (
      <button
        onClick={onToggle}
        style={{
          position: 'fixed',
          top: '10px',
          right: '10px',
          zIndex: 9999,
          background: 'rgba(0, 0, 0, 0.8)',
          color: '#00ff88',
          border: '1px solid #00ff88',
          borderRadius: '4px',
          padding: '8px 12px',
          cursor: 'pointer',
          fontSize: '12px',
        }}
      >
        🎯 Debug Positions
      </button>
    );
  }

  return (
    <div style={{
      position: 'fixed',
      top: '10px',
      right: '10px',
      width: '300px',
      maxHeight: '80vh',
      background: 'rgba(0, 0, 0, 0.9)',
      color: '#fff',
      border: '1px solid #00ff88',
      borderRadius: '8px',
      padding: '16px',
      zIndex: 9999,
      overflow: 'auto',
      fontSize: '12px',
    }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
        <h3 style={{ margin: 0, color: '#00ff88' }}>Button Positions Debug</h3>
        <button
          onClick={onToggle}
          style={{
            background: 'transparent',
            color: '#ff4444',
            border: 'none',
            cursor: 'pointer',
            fontSize: '16px',
          }}
        >
          ✕
        </button>
      </div>

      <div style={{ marginBottom: '16px' }}>
        <strong>Device:</strong> {deviceInfo}
      </div>

      {currentConfig && (
        <>
          <div style={{ marginBottom: '12px' }}>
            <strong style={{ color: '#FFD700' }}>Symbol Buttons:</strong>
            {Object.entries(currentConfig.symbolButtons).map(([key, pos]: [string, any]) => (
              <div key={key} style={{ marginLeft: '8px', fontSize: '11px' }}>
                {key}: {pos.left}, {pos.top}
              </div>
            ))}
          </div>

          <div style={{ marginBottom: '12px' }}>
            <strong style={{ color: '#FFD700' }}>Amount Buttons:</strong>
            {Object.entries(currentConfig.amountButtons).map(([key, pos]: [string, any]) => (
              <div key={key} style={{ marginLeft: '8px', fontSize: '11px' }}>
                {key}: {pos.left}, {pos.top}
              </div>
            ))}
          </div>

          <div style={{ marginBottom: '12px' }}>
            <strong style={{ color: '#FFD700' }}>Bet Rectangles:</strong>
            {Object.entries(currentConfig.betRectangles).map(([key, pos]: [string, any]) => (
              <div key={key} style={{ marginLeft: '8px', fontSize: '11px' }}>
                {key}: {pos.left}, {pos.top}
              </div>
            ))}
          </div>

          <div style={{ marginBottom: '12px' }}>
            <strong style={{ color: '#FFD700' }}>Top Displays:</strong>
            {Object.entries(currentConfig.topDisplays).map(([key, pos]: [string, any]) => (
              <div key={key} style={{ marginLeft: '8px', fontSize: '11px' }}>
                {key}: {pos.left}, {pos.top}
              </div>
            ))}
          </div>
        </>
      )}

      {/* عرض نقاط المرجع البصرية */}
      {currentConfig && Object.entries(currentConfig.symbolButtons).map(([key, pos]: [string, any]) => (
        <div
          key={`marker-${key}`}
          style={{
            position: 'fixed',
            left: pos.left,
            top: pos.top,
            width: '8px',
            height: '8px',
            background: '#ff0000',
            borderRadius: '50%',
            transform: 'translate(-50%, -50%)',
            zIndex: 9998,
            pointerEvents: 'none',
          }}
        />
      ))}

      {currentConfig && Object.entries(currentConfig.amountButtons).map(([key, pos]: [string, any]) => (
        <div
          key={`marker-amount-${key}`}
          style={{
            position: 'fixed',
            left: pos.left,
            top: pos.top,
            width: '6px',
            height: '6px',
            background: '#00ff00',
            borderRadius: '50%',
            transform: 'translate(-50%, -50%)',
            zIndex: 9998,
            pointerEvents: 'none',
          }}
        />
      ))}

      {currentConfig && Object.entries(currentConfig.betRectangles).map(([key, pos]: [string, any]) => (
        <div
          key={`marker-bet-${key}`}
          style={{
            position: 'fixed',
            left: pos.left,
            top: pos.top,
            width: '4px',
            height: '4px',
            background: '#ffff00',
            borderRadius: '50%',
            transform: 'translate(-50%, -50%)',
            zIndex: 9998,
            pointerEvents: 'none',
          }}
        />
      ))}

      <div style={{ 
        marginTop: '16px', 
        padding: '8px', 
        background: 'rgba(255, 255, 255, 0.1)', 
        borderRadius: '4px',
        fontSize: '10px'
      }}>
        <div><span style={{color: '#ff0000'}}>●</span> Symbol Buttons</div>
        <div><span style={{color: '#00ff00'}}>●</span> Amount Buttons</div>
        <div><span style={{color: '#ffff00'}}>●</span> Bet Rectangles</div>
      </div>
    </div>
  );
};

export default ButtonPositionDebugger;
