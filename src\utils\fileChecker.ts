// أداة فحص وتشخيص ملفات الخلفية
// Background Files Checker and Diagnostic Tool

export interface FileStatus {
  path: string;
  exists: boolean;
  actualPath?: string;
  error?: string;
}

// قائمة الملفات المطلوبة
const REQUIRED_FILES = [
  '/images/bg-375x667.webp',    // iPhone SE
  '/images/bg-390x844.webp',    // iPhone 12/13/14
  '/images/bg-428x926.webp',    // iPhone 14 Pro Max
  '/images/bg-412x915.webp',    // Samsung Galaxy
  '/images/bg-768x1024.webp',   // iPad Portrait
  '/images/bg-1920x1080.webp',  // Desktop
];

// قائمة المسارات البديلة المحتملة
const ALTERNATIVE_PATHS = [
  '/images/bg-{size}.webp.webp',  // امتداد مضاعف
  '/bg-{size}.webp',              // في الجذر
  '/images/{size}.webp',          // بدون bg-
  '/backgrounds/bg-{size}.webp',  // في مجلد backgrounds
];

// دالة فحص ملف واحد
async function checkFile(path: string): Promise<FileStatus> {
  try {
    const response = await fetch(path, { method: 'HEAD' });
    return {
      path,
      exists: response.ok,
      error: response.ok ? undefined : `HTTP ${response.status}`
    };
  } catch (error) {
    return {
      path,
      exists: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

// دالة البحث عن مسارات بديلة
async function findAlternativePath(size: string): Promise<string | null> {
  for (const pattern of ALTERNATIVE_PATHS) {
    const path = pattern.replace('{size}', size);
    const status = await checkFile(path);
    if (status.exists) {
      return path;
    }
  }
  return null;
}

// دالة فحص جميع الملفات
export async function checkAllBackgroundFiles(): Promise<FileStatus[]> {
  console.log('🔍 فحص ملفات الخلفية...');
  
  const results: FileStatus[] = [];
  
  for (const filePath of REQUIRED_FILES) {
    const status = await checkFile(filePath);
    
    if (!status.exists) {
      // البحث عن مسارات بديلة
      const size = filePath.match(/bg-(\d+x\d+)\.webp$/)?.[1];
      if (size) {
        const alternativePath = await findAlternativePath(size);
        if (alternativePath) {
          status.actualPath = alternativePath;
          console.log(`⚠️ الملف ${filePath} غير موجود، لكن وُجد في: ${alternativePath}`);
        }
      }
    }
    
    results.push(status);
  }
  
  return results;
}

// دالة عرض تقرير مفصل
export async function generateFileReport(): Promise<void> {
  console.log('📋 تقرير ملفات الخلفية:');
  console.log('================================');
  
  const results = await checkAllBackgroundFiles();
  
  let foundFiles = 0;
  let missingFiles = 0;
  let alternativeFiles = 0;
  
  results.forEach(result => {
    if (result.exists) {
      console.log(`✅ ${result.path}`);
      foundFiles++;
    } else if (result.actualPath) {
      console.log(`⚠️ ${result.path} → ${result.actualPath}`);
      alternativeFiles++;
    } else {
      console.log(`❌ ${result.path} (${result.error})`);
      missingFiles++;
    }
  });
  
  console.log('================================');
  console.log(`📊 الملخص:`);
  console.log(`  ✅ ملفات موجودة: ${foundFiles}`);
  console.log(`  ⚠️ ملفات في مسارات بديلة: ${alternativeFiles}`);
  console.log(`  ❌ ملفات مفقودة: ${missingFiles}`);
  
  if (alternativeFiles > 0) {
    console.log('');
    console.log('🔧 اقتراحات الإصلاح:');
    console.log('1. انقل الملفات للمسارات الصحيحة');
    console.log('2. أو استخدم updateFilePaths() لتحديث المسارات');
  }
  
  if (missingFiles > 0) {
    console.log('');
    console.log('📁 الملفات المطلوبة:');
    results.forEach(result => {
      if (!result.exists && !result.actualPath) {
        console.log(`  - ${result.path}`);
      }
    });
  }
}

// دالة تحديث المسارات تلقائياً
export async function updateFilePaths(): Promise<void> {
  console.log('🔄 تحديث مسارات الملفات...');
  
  const results = await checkAllBackgroundFiles();
  const updates: { [key: string]: string } = {};
  
  results.forEach(result => {
    if (!result.exists && result.actualPath) {
      updates[result.path] = result.actualPath;
    }
  });
  
  if (Object.keys(updates).length === 0) {
    console.log('✅ جميع المسارات صحيحة');
    return;
  }
  
  console.log('📝 التحديثات المطلوبة:');
  Object.entries(updates).forEach(([oldPath, newPath]) => {
    console.log(`  ${oldPath} → ${newPath}`);
  });
  
  console.log('');
  console.log('⚠️ يجب تحديث الملف يدوياً:');
  console.log('src/utils/multiDeviceBackground.ts');
}

// دالة اختبار تحميل الصور
export async function testImageLoading(): Promise<void> {
  console.log('🖼️ اختبار تحميل الصور...');
  
  const results = await checkAllBackgroundFiles();
  
  for (const result of results) {
    const pathToTest = result.actualPath || result.path;
    
    if (result.exists || result.actualPath) {
      try {
        const img = new Image();
        await new Promise((resolve, reject) => {
          img.onload = resolve;
          img.onerror = reject;
          img.src = pathToTest;
        });
        console.log(`✅ تم تحميل: ${pathToTest}`);
      } catch (error) {
        console.log(`❌ فشل تحميل: ${pathToTest}`);
      }
    }
  }
}

// دالة إنشاء ملفات وهمية للاختبار
export function createTestFiles(): void {
  console.log('🧪 إنشاء ملفات اختبار وهمية...');
  console.log('');
  console.log('📁 أنشئ هذه المجلدات والملفات:');
  console.log('public/images/');
  
  REQUIRED_FILES.forEach(filePath => {
    const fileName = filePath.split('/').pop();
    console.log(`  - ${fileName}`);
  });
  
  console.log('');
  console.log('💡 يمكنك نسخ الصورة الأصلية (222.jpg) وإعادة تسميتها');
  console.log('أو استخدام صور مخصصة لكل جهاز');
}

// دالة تشخيص شاملة
export async function runFullDiagnostic(): Promise<void> {
  console.log('🔍 تشخيص شامل لنظام الخلفيات');
  console.log('=====================================');
  
  // 1. فحص الملفات
  await generateFileReport();
  
  console.log('');
  
  // 2. اختبار التحميل
  await testImageLoading();
  
  console.log('');
  
  // 3. فحص الجهاز الحالي
  if (typeof window !== 'undefined' && (window as any).detectDevice) {
    const device = (window as any).detectDevice();
    const background = (window as any).getDeviceBackground();
    
    console.log('📱 الجهاز الحالي:');
    console.log(`  الاسم: ${device.name}`);
    console.log(`  الأبعاد: ${device.width}×${device.height}`);
    console.log(`  الصورة المطلوبة: ${background.image}`);
    
    const fileExists = await checkFile(background.image);
    console.log(`  حالة الملف: ${fileExists.exists ? '✅ موجود' : '❌ مفقود'}`);
  }
  
  console.log('');
  console.log('🎯 انتهى التشخيص');
}

// إضافة الأوامر لوحدة التحكم
if (typeof window !== 'undefined') {
  (window as any).checkBackgroundFiles = checkAllBackgroundFiles;
  (window as any).generateFileReport = generateFileReport;
  (window as any).updateFilePaths = updateFilePaths;
  (window as any).testImageLoading = testImageLoading;
  (window as any).createTestFiles = createTestFiles;
  (window as any).runFullDiagnostic = runFullDiagnostic;
}
