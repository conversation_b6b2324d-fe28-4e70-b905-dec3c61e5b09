// نظام إدارة الأخطاء المحسن
export interface GameError {
  id: string;
  type: 'GAME_LOGIC' | 'RENDERING' | 'NETWORK' | 'USER_INPUT' | 'PERFORMANCE';
  message: string;
  details?: any;
  timestamp: number;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  context?: {
    gamePhase?: string;
    userAction?: string;
    componentName?: string;
    additionalData?: any;
  };
}

class ErrorHandler {
  private static instance: ErrorHandler;
  private errors: GameError[] = [];
  private maxErrors = 100;
  private listeners: ((error: GameError) => void)[] = [];

  private constructor() {
    this.setupGlobalErrorHandlers();
  }

  public static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }

  // إعداد معالجات الأخطاء العامة
  private setupGlobalErrorHandlers() {
    // معالج الأخطاء العام
    window.addEventListener('error', (event) => {
      this.logError({
        type: 'RENDERING',
        message: event.message,
        details: {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          error: event.error
        },
        severity: 'HIGH'
      });
    });

    // معالج أخطاء Promise
    window.addEventListener('unhandledrejection', (event) => {
      this.logError({
        type: 'NETWORK',
        message: 'Unhandled Promise Rejection',
        details: event.reason,
        severity: 'MEDIUM'
      });
    });

    // معالج أخطاء React (إذا كان متاحاً)
    if (typeof window !== 'undefined' && (window as any).React) {
      const originalConsoleError = console.error;
      console.error = (...args) => {
        if (args[0] && args[0].includes && args[0].includes('React')) {
          this.logError({
            type: 'RENDERING',
            message: 'React Error',
            details: args,
            severity: 'HIGH'
          });
        }
        originalConsoleError.apply(console, args);
      };
    }
  }

  // تسجيل خطأ جديد
  public logError(errorData: Partial<GameError>) {
    const error: GameError = {
      id: this.generateErrorId(),
      type: errorData.type || 'GAME_LOGIC',
      message: errorData.message || 'Unknown error',
      details: errorData.details,
      timestamp: Date.now(),
      severity: errorData.severity || 'MEDIUM',
      context: errorData.context
    };

    this.errors.unshift(error);

    // الحفاظ على الحد الأقصى للأخطاء
    if (this.errors.length > this.maxErrors) {
      this.errors = this.errors.slice(0, this.maxErrors);
    }

    // إشعار المستمعين
    this.listeners.forEach(listener => listener(error));

    // طباعة في وحدة التحكم حسب الخطورة
    this.logToConsole(error);

    // حفظ في localStorage للأخطاء الحرجة
    if (error.severity === 'CRITICAL') {
      this.saveToStorage(error);
    }
  }

  // طباعة الخطأ في وحدة التحكم
  private logToConsole(error: GameError) {
    const prefix = this.getSeverityEmoji(error.severity);
    const message = `${prefix} [${error.type}] ${error.message}`;

    switch (error.severity) {
      case 'LOW':
        console.info(message, error.details);
        break;
      case 'MEDIUM':
        console.warn(message, error.details);
        break;
      case 'HIGH':
      case 'CRITICAL':
        console.error(message, error.details);
        break;
    }
  }

  // الحصول على رمز تعبيري للخطورة
  private getSeverityEmoji(severity: GameError['severity']): string {
    switch (severity) {
      case 'LOW': return '💡';
      case 'MEDIUM': return '⚠️';
      case 'HIGH': return '🚨';
      case 'CRITICAL': return '💥';
    }
  }

  // حفظ الأخطاء الحرجة في التخزين المحلي
  private saveToStorage(error: GameError) {
    try {
      const criticalErrors = JSON.parse(localStorage.getItem('game_critical_errors') || '[]');
      criticalErrors.unshift(error);
      
      // الاحتفاظ بآخر 10 أخطاء حرجة فقط
      const limitedErrors = criticalErrors.slice(0, 10);
      localStorage.setItem('game_critical_errors', JSON.stringify(limitedErrors));
    } catch (e) {
      console.error('Failed to save critical error to storage:', e);
    }
  }

  // توليد معرف فريد للخطأ
  private generateErrorId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // إضافة مستمع للأخطاء
  public addErrorListener(listener: (error: GameError) => void) {
    this.listeners.push(listener);
  }

  // إزالة مستمع للأخطاء
  public removeErrorListener(listener: (error: GameError) => void) {
    this.listeners = this.listeners.filter(l => l !== listener);
  }

  // الحصول على جميع الأخطاء
  public getAllErrors(): GameError[] {
    return [...this.errors];
  }

  // الحصول على الأخطاء حسب النوع
  public getErrorsByType(type: GameError['type']): GameError[] {
    return this.errors.filter(error => error.type === type);
  }

  // الحصول على الأخطاء حسب الخطورة
  public getErrorsBySeverity(severity: GameError['severity']): GameError[] {
    return this.errors.filter(error => error.severity === severity);
  }

  // مسح جميع الأخطاء
  public clearErrors() {
    this.errors = [];
  }

  // الحصول على إحصائيات الأخطاء
  public getErrorStats() {
    const stats = {
      total: this.errors.length,
      byType: {} as Record<GameError['type'], number>,
      bySeverity: {} as Record<GameError['severity'], number>,
      recent: this.errors.slice(0, 5)
    };

    this.errors.forEach(error => {
      stats.byType[error.type] = (stats.byType[error.type] || 0) + 1;
      stats.bySeverity[error.severity] = (stats.bySeverity[error.severity] || 0) + 1;
    });

    return stats;
  }
}

// إنشاء مثيل واحد
export const errorHandler = ErrorHandler.getInstance();

// دوال مساعدة للاستخدام السهل
export const logGameError = (message: string, details?: any, context?: GameError['context']) => {
  errorHandler.logError({
    type: 'GAME_LOGIC',
    message,
    details,
    context,
    severity: 'MEDIUM'
  });
};

export const logRenderError = (message: string, componentName?: string, details?: any) => {
  errorHandler.logError({
    type: 'RENDERING',
    message,
    details,
    context: { componentName },
    severity: 'HIGH'
  });
};

export const logPerformanceError = (message: string, details?: any) => {
  errorHandler.logError({
    type: 'PERFORMANCE',
    message,
    details,
    severity: 'LOW'
  });
};

export const logCriticalError = (message: string, details?: any, context?: GameError['context']) => {
  errorHandler.logError({
    type: 'GAME_LOGIC',
    message,
    details,
    context,
    severity: 'CRITICAL'
  });
};

// إتاحة معالج الأخطاء من وحدة التحكم للتشخيص
declare global {
  interface Window {
    gameErrorHandler: ErrorHandler;
    showGameErrors: () => void;
    clearGameErrors: () => void;
  }
}

if (typeof window !== 'undefined') {
  window.gameErrorHandler = errorHandler;
  window.showGameErrors = () => {
    console.table(errorHandler.getAllErrors());
    console.log('📊 إحصائيات الأخطاء:', errorHandler.getErrorStats());
  };
  window.clearGameErrors = () => {
    errorHandler.clearErrors();
    console.log('🧹 تم مسح جميع الأخطاء');
  };
}
