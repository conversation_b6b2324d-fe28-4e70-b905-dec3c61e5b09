import React, { useState, useEffect } from 'react';
import { BUTTON_POSITIONS } from '../utils/buttonPositions';

interface NewLayoutPreviewProps {
  isVisible: boolean;
  onToggle: () => void;
}

const NewLayoutPreview: React.FC<NewLayoutPreviewProps> = ({
  isVisible,
  onToggle
}) => {
  const [currentDevice, setCurrentDevice] = useState<any>(null);

  useEffect(() => {
    const updateDevice = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      
      let deviceConfig = BUTTON_POSITIONS.find(d => 
        Math.abs(d.device.width - width) < 50 && Math.abs(d.device.height - height) < 50
      );
      
      if (!deviceConfig) {
        if (width <= 768) {
          deviceConfig = BUTTON_POSITIONS.find(d => d.device.width <= 768) || BUTTON_POSITIONS[0];
        } else {
          deviceConfig = BUTTON_POSITIONS.find(d => d.device.width > 768) || BUTTON_POSITIONS[1] || BUTTON_POSITIONS[0];
        }
      }
      
      setCurrentDevice(deviceConfig);
    };

    updateDevice();
    window.addEventListener('resize', updateDevice);
    return () => window.removeEventListener('resize', updateDevice);
  }, []);

  if (!isVisible) {
    return (
      <button
        onClick={onToggle}
        style={{
          position: 'fixed',
          top: '210px',
          right: '10px',
          zIndex: 9999,
          background: 'rgba(0, 200, 100, 0.8)',
          color: '#fff',
          border: '1px solid #00cc66',
          borderRadius: '4px',
          padding: '8px 12px',
          cursor: 'pointer',
          fontSize: '12px',
        }}
      >
        ✨ New Layout
      </button>
    );
  }

  if (!currentDevice) return null;

  return (
    <>
      {/* أزرار الفواكه */}
      {Object.entries(currentDevice.symbolButtons).map(([key, pos]: [string, any]) => (
        <div
          key={`new-symbol-${key}`}
          style={{
            position: 'absolute',
            left: pos.left,
            top: pos.top,
            width: '60px',
            height: '60px',
            background: 'rgba(0, 255, 136, 0.9)',
            border: '3px solid #00ff88',
            borderRadius: '12px',
            transform: 'translate(-50%, -50%)',
            zIndex: 9997,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '10px',
            fontWeight: 'bold',
            color: '#000',
            cursor: 'pointer',
            transition: 'all 0.3s ease',
            boxShadow: '0 4px 12px rgba(0, 255, 136, 0.4)',
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'translate(-50%, -50%) scale(1.1)';
            e.currentTarget.style.boxShadow = '0 6px 20px rgba(0, 255, 136, 0.6)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'translate(-50%, -50%) scale(1)';
            e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 255, 136, 0.4)';
          }}
        >
          {key.substring(0, 3).toUpperCase()}
        </div>
      ))}

      {/* مربعات الرهانات - بين الفواكه والمبالغ */}
      {Object.entries(currentDevice.betRectangles).map(([key, pos]: [string, any]) => (
        <div
          key={`new-bet-${key}`}
          style={{
            position: 'absolute',
            left: pos.left,
            top: pos.top,
            padding: '6px 10px',
            background: 'rgba(255, 215, 0, 0.9)',
            border: '2px solid #FFD700',
            borderRadius: '8px',
            transform: 'translate(-50%, -50%)',
            zIndex: 9997,
            fontSize: '12px',
            fontWeight: 'bold',
            color: '#000',
            minWidth: '40px',
            textAlign: 'center',
            cursor: 'pointer',
            boxShadow: '0 3px 10px rgba(255, 215, 0, 0.4)',
            transition: 'all 0.3s ease',
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'translate(-50%, -50%) scale(1.05)';
            e.currentTarget.style.boxShadow = '0 4px 15px rgba(255, 215, 0, 0.6)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'translate(-50%, -50%) scale(1)';
            e.currentTarget.style.boxShadow = '0 3px 10px rgba(255, 215, 0, 0.4)';
          }}
        >
          1.5k
        </div>
      ))}

      {/* أزرار المبالغ - أصغر حجماً */}
      {Object.entries(currentDevice.amountButtons).map(([key, pos]: [string, any]) => (
        <div
          key={`new-amount-${key}`}
          style={{
            position: 'absolute',
            left: pos.left,
            top: pos.top,
            width: '40px',
            height: '40px',
            background: 'linear-gradient(135deg, #FFD700, #FFA500)',
            border: '2px solid #FFD700',
            borderRadius: '50%',
            transform: 'translate(-50%, -50%)',
            zIndex: 9997,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '10px',
            fontWeight: 'bold',
            color: '#000',
            cursor: 'pointer',
            transition: 'all 0.3s ease',
            boxShadow: '0 3px 10px rgba(255, 215, 0, 0.4)',
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'translate(-50%, -50%) scale(1.1)';
            e.currentTarget.style.boxShadow = '0 4px 15px rgba(255, 215, 0, 0.6)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'translate(-50%, -50%) scale(1)';
            e.currentTarget.style.boxShadow = '0 3px 10px rgba(255, 215, 0, 0.4)';
          }}
        >
          {pos.value >= 1000 ? `${pos.value/1000}k` : pos.value}
        </div>
      ))}

      {/* لوحة المعلومات */}
      <div style={{
        position: 'fixed',
        bottom: '80px',
        left: '20px',
        width: '250px',
        background: 'rgba(0, 0, 0, 0.9)',
        border: '1px solid #00cc66',
        borderRadius: '8px',
        padding: '16px',
        zIndex: 9999,
        color: '#fff',
        fontSize: '12px',
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '12px' }}>
          <h3 style={{ margin: 0, fontSize: '14px', color: '#00cc66' }}>✨ New Layout</h3>
          <button onClick={onToggle} style={{ background: 'none', border: 'none', color: '#ff4444', cursor: 'pointer', fontSize: '16px' }}>✕</button>
        </div>

        <div style={{ marginBottom: '12px' }}>
          <strong>الجهاز:</strong> {currentDevice.device.name}<br/>
          <strong>الترتيب الجديد:</strong>
        </div>

        <div style={{ fontSize: '10px', lineHeight: '1.6' }}>
          <div>🟢 <strong>أزرار الفواكه</strong> (65%)</div>
          <div>🟡 <strong>مربعات الرهانات</strong> (71%)</div>
          <div>🟠 <strong>أزرار المبالغ</strong> (78%) - أصغر</div>
        </div>

        <div style={{ 
          marginTop: '12px', 
          padding: '8px', 
          background: 'rgba(0, 204, 102, 0.2)', 
          borderRadius: '4px',
          fontSize: '10px'
        }}>
          <strong>✅ التحسينات:</strong><br/>
          • أزرار المبالغ أصغر (40px)<br/>
          • مربعات الرهان بين الأزرار<br/>
          • ترتيب منطقي ومرئي
        </div>
      </div>
    </>
  );
};

export default NewLayoutPreview;
