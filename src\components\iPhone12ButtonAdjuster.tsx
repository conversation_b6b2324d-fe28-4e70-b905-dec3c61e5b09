import React, { useState, useEffect } from 'react';
import { BUTTON_POSITIONS } from '../utils/buttonPositions';

interface iPhone12ButtonAdjusterProps {
  isVisible: boolean;
  onToggle: () => void;
}

const iPhone12ButtonAdjuster: React.FC<iPhone12ButtonAdjusterProps> = ({
  isVisible,
  onToggle
}) => {
  const [selectedButton, setSelectedButton] = useState<string>('');
  const [positions, setPositions] = useState<any>({});
  const [isDragging, setIsDragging] = useState(false);

  // البحث عن تكوين iPhone 12
  const iPhone12Config = BUTTON_POSITIONS.find(d => d.device.name === 'iPhone 12/13/14');

  useEffect(() => {
    if (iPhone12Config) {
      setPositions({
        ...iPhone12Config.symbolButtons,
        ...iPhone12Config.amountButtons,
        ...iPhone12Config.betRectangles,
        ...iPhone12Config.topDisplays
      });
    }
  }, [iPhone12Config]);

  const handleMouseDown = (buttonKey: string, e: React.MouseEvent) => {
    e.preventDefault();
    setSelectedButton(buttonKey);
    setIsDragging(true);
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging || !selectedButton) return;

    const rect = e.currentTarget.getBoundingClientRect();
    const x = ((e.clientX - rect.left) / rect.width) * 100;
    const y = ((e.clientY - rect.top) / rect.height) * 100;

    setPositions(prev => ({
      ...prev,
      [selectedButton]: {
        ...prev[selectedButton],
        left: `${Math.max(0, Math.min(100, x)).toFixed(1)}%`,
        top: `${Math.max(0, Math.min(100, y)).toFixed(1)}%`
      }
    }));
  };

  const handleMouseUp = () => {
    setIsDragging(false);
    setSelectedButton('');
  };

  const exportPositions = () => {
    const symbolButtons = {};
    const amountButtons = {};
    const betRectangles = {};
    const topDisplays = {};

    Object.entries(positions).forEach(([key, pos]: [string, any]) => {
      if (key.includes('amount')) {
        amountButtons[key] = pos;
      } else if (key.includes('Display')) {
        topDisplays[key] = pos;
      } else if (['bar', 'watermelon', 'lemon', 'banana', 'apple'].includes(key)) {
        symbolButtons[key] = pos;
        betRectangles[key] = { ...pos, symbol: pos.name };
      }
    });

    const configCode = `
// تكوين iPhone 12/13/14 المحدث
{
  device: {
    name: 'iPhone 12/13/14',
    width: 390,
    height: 844,
  },
  backgroundImage: '/images/bg-390x844.webp',
  symbolButtons: ${JSON.stringify(symbolButtons, null, 4)},
  amountButtons: ${JSON.stringify(amountButtons, null, 4)},
  betRectangles: ${JSON.stringify(betRectangles, null, 4)},
  topDisplays: ${JSON.stringify(topDisplays, null, 4)},
  gameBoard: {
    left: '50%',
    top: '32%',
    width: 'min(360px, 85vw)',
    height: 'min(280px, 48vh)',
    transform: 'translateX(-50%)',
    position: 'absolute',
    zIndex: 10,
  },
}`;

    navigator.clipboard.writeText(configCode);
    alert('تم نسخ التكوين الجديد! الصقه في ملف buttonPositions.ts');
  };

  if (!isVisible) {
    return (
      <button
        onClick={onToggle}
        style={{
          position: 'fixed',
          top: '50px',
          right: '10px',
          zIndex: 9999,
          background: 'rgba(0, 100, 200, 0.8)',
          color: '#fff',
          border: '1px solid #0066cc',
          borderRadius: '4px',
          padding: '8px 12px',
          cursor: 'pointer',
          fontSize: '12px',
        }}
      >
        📱 iPhone 12 Adjuster
      </button>
    );
  }

  return (
    <div style={{
      position: 'fixed',
      top: '0',
      left: '0',
      width: '100vw',
      height: '100vh',
      background: 'rgba(0, 0, 0, 0.9)',
      zIndex: 9999,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
    }}>
      {/* محاكي iPhone 12 */}
      <div style={{
        width: '390px',
        height: '844px',
        background: 'linear-gradient(135deg, #1a1a2e, #16213e)',
        border: '8px solid #333',
        borderRadius: '40px',
        position: 'relative',
        overflow: 'hidden',
      }}>
        {/* الخلفية */}
        <div style={{
          width: '100%',
          height: '100%',
          background: 'linear-gradient(45deg, #0f3460, #0e4b99)',
          position: 'relative',
        }}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
        >
          {/* لوحة اللعبة */}
          <div style={{
            position: 'absolute',
            left: '50%',
            top: '32%',
            width: '300px',
            height: '200px',
            transform: 'translateX(-50%)',
            background: 'rgba(255, 255, 255, 0.1)',
            border: '2px dashed rgba(255, 255, 255, 0.3)',
            borderRadius: '12px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'rgba(255, 255, 255, 0.7)',
            fontSize: '14px',
          }}>
            Game Board Area
          </div>

          {/* أزرار الفواكه */}
          {['bar', 'watermelon', 'lemon', 'banana', 'apple'].map(symbol => {
            const pos = positions[symbol];
            if (!pos) return null;
            
            return (
              <div
                key={symbol}
                style={{
                  position: 'absolute',
                  left: pos.left,
                  top: pos.top,
                  width: '50px',
                  height: '50px',
                  background: selectedButton === symbol ? '#ff6b6b' : '#4ecdc4',
                  border: '2px solid #fff',
                  borderRadius: '12px',
                  transform: 'translate(-50%, -50%)',
                  cursor: 'move',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '10px',
                  fontWeight: 'bold',
                  color: '#fff',
                  userSelect: 'none',
                  zIndex: selectedButton === symbol ? 1000 : 100,
                }}
                onMouseDown={(e) => handleMouseDown(symbol, e)}
              >
                {symbol.toUpperCase()}
              </div>
            );
          })}

          {/* أزرار المبالغ */}
          {[1, 2, 3, 4, 5].map(num => {
            const key = `amount${num}`;
            const pos = positions[key];
            if (!pos) return null;
            
            return (
              <div
                key={key}
                style={{
                  position: 'absolute',
                  left: pos.left,
                  top: pos.top,
                  width: '35px',
                  height: '35px',
                  background: selectedButton === key ? '#ffa726' : '#66bb6a',
                  border: '2px solid #fff',
                  borderRadius: '50%',
                  transform: 'translate(-50%, -50%)',
                  cursor: 'move',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '8px',
                  fontWeight: 'bold',
                  color: '#fff',
                  userSelect: 'none',
                  zIndex: selectedButton === key ? 1000 : 100,
                }}
                onMouseDown={(e) => handleMouseDown(key, e)}
              >
                {pos.value ? (pos.value >= 1000 ? `${pos.value/1000}k` : pos.value) : num}
              </div>
            );
          })}

          {/* عروض الرصيد */}
          {['balanceDisplay', 'totalBetDisplay'].map(key => {
            const pos = positions[key];
            if (!pos) return null;
            
            return (
              <div
                key={key}
                style={{
                  position: 'absolute',
                  left: pos.left,
                  top: pos.top,
                  padding: '8px 12px',
                  background: selectedButton === key ? '#9c27b0' : '#2196f3',
                  border: '2px solid #fff',
                  borderRadius: '8px',
                  transform: 'translate(-50%, -50%)',
                  cursor: 'move',
                  fontSize: '10px',
                  fontWeight: 'bold',
                  color: '#fff',
                  userSelect: 'none',
                  zIndex: selectedButton === key ? 1000 : 100,
                }}
                onMouseDown={(e) => handleMouseDown(key, e)}
              >
                {key === 'balanceDisplay' ? 'BALANCE' : 'TOTAL BET'}
              </div>
            );
          })}
        </div>
      </div>

      {/* لوحة التحكم */}
      <div style={{
        position: 'absolute',
        top: '20px',
        right: '20px',
        width: '300px',
        background: 'rgba(0, 0, 0, 0.9)',
        border: '1px solid #333',
        borderRadius: '8px',
        padding: '16px',
        color: '#fff',
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '16px' }}>
          <h3 style={{ margin: 0, fontSize: '16px' }}>iPhone 12 Button Adjuster</h3>
          <button onClick={onToggle} style={{ background: 'none', border: 'none', color: '#ff4444', cursor: 'pointer', fontSize: '18px' }}>✕</button>
        </div>

        <div style={{ marginBottom: '16px', fontSize: '12px' }}>
          <strong>التعليمات:</strong>
          <ul style={{ margin: '8px 0', paddingLeft: '16px' }}>
            <li>اسحب الأزرار لتحريكها</li>
            <li>🔵 أزرار الفواكه</li>
            <li>🟢 أزرار المبالغ</li>
            <li>🟣 عروض الرصيد</li>
          </ul>
        </div>

        {selectedButton && (
          <div style={{ marginBottom: '16px', padding: '8px', background: 'rgba(255, 255, 255, 0.1)', borderRadius: '4px' }}>
            <strong>المحدد:</strong> {selectedButton}<br/>
            <strong>الموقع:</strong> {positions[selectedButton]?.left}, {positions[selectedButton]?.top}
          </div>
        )}

        <button
          onClick={exportPositions}
          style={{
            width: '100%',
            padding: '12px',
            background: '#4caf50',
            color: '#fff',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            fontWeight: 'bold',
          }}
        >
          📋 نسخ التكوين الجديد
        </button>
      </div>
    </div>
  );
};

export default iPhone12ButtonAdjuster;
