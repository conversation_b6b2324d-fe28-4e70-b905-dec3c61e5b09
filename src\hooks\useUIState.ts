import { useState, useEffect } from 'react';

/**
 * Custom hook لإدارة حالات واجهة المستخدم
 * يفصل منطق UI عن منطق اللعبة لتحسين الأداء
 */
export const useUIState = (gamePhase: string, messages: string[], betsOnTypes: any) => {
  // حالات عرض البطاقات والنوافذ
  const [showResultCard, setShowResultCard] = useState(false);
  const [didParticipate, setDidParticipate] = useState(false);
  const [lastResultMessages, setLastResultMessages] = useState<string[]>([]);
  
  // حالات رسائل التنبيه
  const [showPhaseAlert, setShowPhaseAlert] = useState(false);
  const [phaseAlertMessage, setPhaseAlertMessage] = useState('');
  
  // حالات النوافذ المنبثقة
  const [showHelp, setShowHelp] = useState(false);
  const [showPlayerHistory, setShowPlayerHistory] = useState(false);
  const [showGameHistory, setShowGameHistory] = useState(false);
  const [showMedals, setShowMedals] = useState(false);

  // إظهار بطاقة النتائج عند انتهاء الجولة
  useEffect(() => {
    if (gamePhase === 'result_display') {
      const didPlayerParticipate = Object.values(betsOnTypes).some((bet: number) => bet > 0);
      setDidParticipate(didPlayerParticipate);
      setLastResultMessages(messages);
      setShowResultCard(true);
    }
  }, [gamePhase, betsOnTypes, messages]);

  // إخفاء بطاقة النتائج عند بدء جولة جديدة
  useEffect(() => {
    if (gamePhase === 'betting') {
      setShowResultCard(false);
    }
  }, [gamePhase]);

  // إظهار تنبيه المرحلة
  const showPhaseAlertWithMessage = (message: string) => {
    setPhaseAlertMessage(message);
    setShowPhaseAlert(true);
    setTimeout(() => setShowPhaseAlert(false), 2000);
  };

  // إغلاق جميع النوافذ المنبثقة
  const closeAllPopups = () => {
    setShowHelp(false);
    setShowPlayerHistory(false);
    setShowGameHistory(false);
    setShowMedals(false);
  };

  // تبديل نافذة المساعدة
  const toggleHelp = () => {
    closeAllPopups();
    setShowHelp(prev => !prev);
  };

  // تبديل نافذة الميداليات
  const toggleMedals = () => {
    closeAllPopups();
    setShowMedals(prev => !prev);
  };

  // تبديل نافذة تاريخ اللاعب
  const togglePlayerHistory = () => {
    closeAllPopups();
    setShowPlayerHistory(prev => !prev);
  };

  // تبديل نافذة تاريخ اللعبة
  const toggleGameHistory = () => {
    closeAllPopups();
    setShowGameHistory(prev => !prev);
  };

  return {
    // حالات البطاقات
    showResultCard,
    setShowResultCard,
    didParticipate,
    lastResultMessages,
    
    // حالات التنبيهات
    showPhaseAlert,
    phaseAlertMessage,
    showPhaseAlertWithMessage,
    
    // حالات النوافذ المنبثقة
    showHelp,
    showPlayerHistory,
    showGameHistory,
    showMedals,
    
    // دوال التحكم
    toggleHelp,
    toggleMedals,
    togglePlayerHistory,
    toggleGameHistory,
    closeAllPopups
  };
};
