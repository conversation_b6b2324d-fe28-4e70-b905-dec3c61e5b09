<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎮 مدير الأجهزة والأزرار - Lucky Ocean Game</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 350px 1fr;
            gap: 20px;
            height: calc(100vh - 40px);
        }

        .sidebar {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            overflow-y: auto;
        }

        .main-area {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 20px;
            position: relative;
            overflow: hidden;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #00ff88;
            margin-bottom: 10px;
            font-size: 2em;
        }

        .section {
            margin-bottom: 25px;
            padding: 15px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            border-left: 4px solid #00ff88;
        }

        .section h3 {
            color: #00ffff;
            margin-bottom: 15px;
            font-size: 1.2em;
        }

        .device-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }

        .device-btn {
            padding: 12px;
            background: linear-gradient(45deg, #333, #555);
            color: white;
            border: 2px solid #666;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
            text-align: center;
        }

        .device-btn:hover {
            border-color: #00ff88;
            transform: translateY(-2px);
        }

        .device-btn.active {
            background: linear-gradient(45deg, #00ff88, #00ffff);
            color: #000;
            border-color: #00ffff;
        }

        .control-group {
            margin-bottom: 15px;
        }

        .control-group label {
            display: block;
            margin-bottom: 8px;
            color: #00ff88;
            font-weight: bold;
        }

        .control-group input, .control-group select {
            width: 100%;
            padding: 10px;
            border: 2px solid #00ff88;
            border-radius: 6px;
            background: rgba(0, 0, 0, 0.5);
            color: white;
            font-size: 14px;
        }

        .btn {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(45deg, #00ff88, #00ffff);
            color: #000;
        }

        .btn-secondary {
            background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(45deg, #ffaa00, #ffcc00);
            color: #000;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .preview-area {
            position: relative;
            width: 100%;
            height: 100%;
            background: #000;
            border-radius: 10px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .device-frame {
            position: relative;
            background: #222;
            border-radius: 20px;
            padding: 20px;
            box-shadow: 0 0 30px rgba(0, 0, 0, 0.8);
        }

        .device-screen {
            position: relative;
            background-image: url('http://localhost:5173/222.jpg');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            border-radius: 10px;
            overflow: hidden;
        }

        .game-button {
            position: absolute;
            width: 35px;
            height: 35px;
            background: rgba(0, 255, 136, 0.8);
            border: 2px solid #00ff88;
            border-radius: 50%;
            color: white;
            font-size: 8px;
            font-weight: bold;
            cursor: move;
            display: flex;
            align-items: center;
            justify-content: center;
            transform: translate(-50%, -50%);
            transition: all 0.3s ease;
            z-index: 10;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        }

        /* أزرار الرموز (الفواكه + BAR) */
        .symbol-button {
            background: rgba(0, 255, 136, 0.8);
            border-color: #00ff88;
        }

        /* أزرار المبالغ */
        .amount-button {
            background: rgba(255, 165, 0, 0.8);
            border-color: #ffa500;
            border-radius: 8px;
            width: 40px;
            height: 25px;
            font-size: 7px;
        }

        /* زر الرهان الرئيسي */
        .spin-button {
            background: rgba(255, 69, 0, 0.8);
            border-color: #ff4500;
            width: 50px;
            height: 30px;
            font-size: 9px;
            font-weight: bold;
        }

        /* عناصر العرض */
        .balance-display, .countdown-display {
            background: rgba(0, 191, 255, 0.8);
            border-color: #00bfff;
            border-radius: 8px;
            width: 60px;
            height: 20px;
            font-size: 6px;
        }

        /* مستطيلات الرهان */
        .bet-rectangle {
            background: rgba(255, 255, 255, 0.9);
            border-color: #333;
            border-radius: 4px;
            width: 45px;
            height: 15px;
            font-size: 5px;
            color: #000;
            font-weight: bold;
        }

        /* أرقام الرهان */
        .bet-number {
            background: rgba(255, 215, 0, 0.8);
            border-color: #ffd700;
            border-radius: 6px;
            width: 50px;
            height: 18px;
            font-size: 5px;
            color: #000;
            font-weight: bold;
        }

        .game-button:hover {
            background: rgba(255, 0, 102, 0.8);
            border-color: #ff0066;
            transform: translate(-50%, -50%) scale(1.1);
        }

        .game-button.dragging {
            background: rgba(255, 0, 102, 0.9);
            border-color: #ff0066;
            transform: translate(-50%, -50%) scale(1.2);
            z-index: 100;
        }

        .grid-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                linear-gradient(rgba(0, 255, 136, 0.2) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 255, 136, 0.2) 1px, transparent 1px);
            background-size: 10% 10%;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .grid-overlay.visible {
            opacity: 1;
        }

        .status-bar {
            position: fixed;
            bottom: 20px;
            left: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.9);
            padding: 10px 20px;
            border-radius: 10px;
            border: 2px solid #00ff88;
            color: #00ff88;
            font-family: monospace;
            font-size: 12px;
            z-index: 1000;
        }

        .code-output {
            background: #000;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            color: #00ff88;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #00ff88;
            margin-top: 10px;
        }

        .device-info {
            background: rgba(0, 0, 0, 0.5);
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            font-size: 12px;
        }

        @media (max-width: 1200px) {
            .container {
                grid-template-columns: 1fr;
                grid-template-rows: auto 1fr;
            }
            
            .sidebar {
                max-height: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- الشريط الجانبي -->
        <div class="sidebar">
            <div class="header">
                <h1>🎮 مدير الأجهزة</h1>
                <p>Lucky Ocean Game</p>
            </div>

            <!-- اختيار الجهاز -->
            <div class="section">
                <h3>📱 اختيار الجهاز</h3>
                <div class="device-grid">
                    <div class="device-btn" data-device="iphone-se" data-width="375" data-height="667">
                        iPhone SE<br>375×667
                    </div>
                    <div class="device-btn active" data-device="iphone-12" data-width="390" data-height="844">
                        iPhone 12<br>390×844
                    </div>
                    <div class="device-btn" data-device="iphone-14-pro-max" data-width="428" data-height="926">
                        iPhone 14 Pro Max<br>428×926
                    </div>
                    <div class="device-btn" data-device="galaxy" data-width="412" data-height="915">
                        Samsung Galaxy<br>412×915
                    </div>
                    <div class="device-btn" data-device="ipad" data-width="768" data-height="1024">
                        iPad Portrait<br>768×1024
                    </div>
                    <div class="device-btn" data-device="desktop" data-width="1920" data-height="1080">
                        Desktop<br>1920×1080
                    </div>
                </div>
                
                <div class="control-group">
                    <label>🎨 صورة الخلفية:</label>
                    <input type="file" id="backgroundUpload" accept="image/*">
                </div>
            </div>

            <!-- معلومات الجهاز -->
            <div class="section">
                <h3>📊 معلومات الجهاز</h3>
                <div class="device-info" id="deviceInfo">
                    <strong>الجهاز:</strong> iPhone 12/13/14<br>
                    <strong>الأبعاد:</strong> 390×844<br>
                    <strong>النسبة:</strong> 9:19.5<br>
                    <strong>الصورة:</strong> bg-390x844.webp
                </div>
            </div>

            <!-- إدارة أنواع الأزرار -->
            <div class="section">
                <h3>🎯 أنواع الأزرار</h3>
                <div class="control-group">
                    <label>
                        <input type="checkbox" id="showSymbols" checked onchange="toggleButtonType('symbol')">
                        🍎 أزرار الرموز (5)
                    </label>
                </div>
                <div class="control-group">
                    <label>
                        <input type="checkbox" id="showAmounts" checked onchange="toggleButtonType('amount')">
                        💰 أزرار المبالغ (5)
                    </label>
                </div>
                <div class="control-group">
                    <label>
                        <input type="checkbox" id="showActions" checked onchange="toggleButtonType('action')">
                        🎮 أزرار الإجراءات (1)
                    </label>
                </div>
                <div class="control-group">
                    <label>
                        <input type="checkbox" id="showDisplays" checked onchange="toggleButtonType('display')">
                        📊 عناصر العرض (2)
                    </label>
                </div>
                <div class="control-group">
                    <label>
                        <input type="checkbox" id="showBetDisplays" checked onchange="toggleButtonType('bet-display')">
                        📋 مستطيلات الرهان (5)
                    </label>
                </div>
                <div class="control-group">
                    <label>
                        <input type="checkbox" id="showBetInfos" checked onchange="toggleButtonType('bet-info')">
                        🔢 أرقام الرهان (3)
                    </label>
                </div>
            </div>

            <!-- أدوات التحكم -->
            <div class="section">
                <h3>🛠️ أدوات التحكم</h3>

                <div class="control-group">
                    <label>📏 حجم الأزرار:</label>
                    <select id="buttonSize" onchange="changeButtonSize()">
                        <option value="25">صغير جداً (25px)</option>
                        <option value="30">صغير (30px)</option>
                        <option value="35" selected>متوسط (35px)</option>
                        <option value="40">كبير (40px)</option>
                        <option value="50">كبير جداً (50px)</option>
                    </select>
                </div>

                <div class="control-group">
                    <label>🎨 شفافية الأزرار:</label>
                    <select id="buttonOpacity" onchange="changeButtonOpacity()">
                        <option value="0.5">شفاف جداً (50%)</option>
                        <option value="0.7">شفاف (70%)</option>
                        <option value="0.8" selected>متوسط (80%)</option>
                        <option value="0.9">معتم (90%)</option>
                        <option value="1.0">معتم تماماً (100%)</option>
                    </select>
                </div>

                <button class="btn btn-primary" onclick="toggleGrid()">
                    📐 تفعيل/إلغاء الشبكة
                </button>
                <button class="btn btn-primary" onclick="toggleAllButtons()">
                    👁️ إخفاء/إظهار جميع الأزرار
                </button>
                <button class="btn btn-secondary" onclick="resetPositions()">
                    🔄 إعادة تعيين المواقع
                </button>
                <button class="btn btn-warning" onclick="exportCode()">
                    💾 تصدير الكود
                </button>
            </div>

            <!-- إضافة أزرار جديدة -->
            <div class="section">
                <h3>➕ إضافة أزرار جديدة</h3>

                <div class="control-group">
                    <label>🏷️ اسم الزر:</label>
                    <input type="text" id="newButtonName" placeholder="مثال: MENU">
                </div>

                <div class="control-group">
                    <label>🎯 نوع الزر:</label>
                    <select id="newButtonType">
                        <option value="symbol">رمز</option>
                        <option value="amount">مبلغ</option>
                        <option value="action">إجراء</option>
                        <option value="display">عرض</option>
                        <option value="bet-display">مستطيل رهان</option>
                        <option value="bet-info">رقم رهان</option>
                    </select>
                </div>

                <button class="btn btn-primary" onclick="addNewButton()">
                    ➕ إضافة زر جديد
                </button>

                <button class="btn btn-secondary" onclick="removeLastButton()">
                    🗑️ حذف آخر زر
                </button>
            </div>

            <!-- الكود المُولد -->
            <div class="section">
                <h3>💻 الكود المُولد</h3>
                <div class="code-output" id="codeOutput">
// سيظهر الكود هنا بعد ترتيب الأزرار
                </div>
                <button class="btn btn-primary" onclick="copyCode()" style="margin-top: 10px;">
                    📋 نسخ الكود
                </button>
            </div>
        </div>

        <!-- المنطقة الرئيسية -->
        <div class="main-area">
            <div class="preview-area">
                <div class="device-frame" id="deviceFrame">
                    <div class="device-screen" id="deviceScreen">
                        <div class="grid-overlay" id="gridOverlay"></div>
                        
                        <!-- أزرار الرهان على الرموز -->
                        <div class="game-button symbol-button" data-symbol="BAR" data-type="symbol" style="left: 5.12%; top: 75.23%;">BAR</div>
                        <div class="game-button symbol-button" data-symbol="WATERMELON" data-type="symbol" style="left: 22.05%; top: 73.10%;">WATERMELON</div>
                        <div class="game-button symbol-button" data-symbol="LEMON" data-type="symbol" style="left: 50.00%; top: 73.50%;">LEMON</div>
                        <div class="game-button symbol-button" data-symbol="BANANA" data-type="symbol" style="left: 77.95%; top: 73.10%;">BANANA</div>
                        <div class="game-button symbol-button" data-symbol="APPLE" data-type="symbol" style="left: 94.88%; top: 75.23%;">APPLE</div>

                        <!-- أزرار مبالغ الرهان -->
                        <div class="game-button amount-button" data-symbol="5000" data-type="amount" style="left: 10%; top: 85%;">5K</div>
                        <div class="game-button amount-button" data-symbol="3000" data-type="amount" style="left: 25%; top: 85%;">3K</div>
                        <div class="game-button amount-button" data-symbol="1000" data-type="amount" style="left: 40%; top: 85%;">1K</div>
                        <div class="game-button amount-button" data-symbol="500" data-type="amount" style="left: 55%; top: 85%;">500</div>
                        <div class="game-button amount-button" data-symbol="100" data-type="amount" style="left: 70%; top: 85%;">100</div>

                        <!-- زر الرهان الرئيسي -->
                        <div class="game-button spin-button" data-symbol="SPIN" data-type="action" style="left: 85%; top: 85%;">SPIN</div>

                        <!-- عرض الرصيد -->
                        <div class="game-button balance-display" data-symbol="BALANCE" data-type="display" style="left: 15%; top: 10%;">BALANCE</div>

                        <!-- عرض العد التنازلي -->
                        <div class="game-button countdown-display" data-symbol="COUNTDOWN" data-type="display" style="left: 85%; top: 10%;">TIMER</div>

                        <!-- مستطيلات الرهان (تظهر مبالغ الرهان على كل رمز) -->
                        <div class="game-button bet-rectangle" data-symbol="BAR_BET" data-type="bet-display" style="left: 5.12%; top: 65%;">BAR: 0</div>
                        <div class="game-button bet-rectangle" data-symbol="WATERMELON_BET" data-type="bet-display" style="left: 22.05%; top: 63%;">🍉: 0</div>
                        <div class="game-button bet-rectangle" data-symbol="LEMON_BET" data-type="bet-display" style="left: 50.00%; top: 63.5%;">🍋: 0</div>
                        <div class="game-button bet-rectangle" data-symbol="BANANA_BET" data-type="bet-display" style="left: 77.95%; top: 63%;">🍌: 0</div>
                        <div class="game-button bet-rectangle" data-symbol="APPLE_BET" data-type="bet-display" style="left: 94.88%; top: 65%;">🍎: 0</div>

                        <!-- أرقام الرهان (تظهر في الأعلى بجانب الرصيد) -->
                        <div class="game-button bet-number" data-symbol="TOTAL_BETS" data-type="bet-info" style="left: 35%; top: 10%;">إجمالي الرهانات</div>
                        <div class="game-button bet-number" data-symbol="CURRENT_BET" data-type="bet-info" style="left: 55%; top: 10%;">الرهان الحالي</div>
                        <div class="game-button bet-number" data-symbol="LAST_WIN" data-type="bet-info" style="left: 75%; top: 10%;">آخر ربح</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- شريط الحالة -->
    <div class="status-bar" id="statusBar">
        🎯 جاهز للاستخدام | اسحب الأزرار لترتيبها | الجهاز: iPhone 12/13/14 (390×844)
    </div>

    <script>
        // البيانات الأساسية
        const devices = {
            'iphone-se': { name: 'iPhone SE', width: 375, height: 667, image: 'bg-375x667.webp' },
            'iphone-12': { name: 'iPhone 12/13/14', width: 390, height: 844, image: 'bg-390x844.webp' },
            'iphone-14-pro-max': { name: 'iPhone 14 Pro Max', width: 428, height: 926, image: 'bg-428x926.webp' },
            'galaxy': { name: 'Samsung Galaxy', width: 412, height: 915, image: 'bg-412x915.webp' },
            'ipad': { name: 'iPad Portrait', width: 768, height: 1024, image: 'bg-768x1024.webp' },
            'desktop': { name: 'Desktop', width: 1920, height: 1080, image: 'bg-1920x1080.webp' }
        };

        let currentDevice = 'iphone-12';
        let isDragging = false;
        let dragElement = null;
        let dragOffset = { x: 0, y: 0 };

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            initializeDeviceSelection();
            initializeDragAndDrop();
            updateDeviceDisplay();
            updateCode();
        });

        // تهيئة اختيار الأجهزة
        function initializeDeviceSelection() {
            const deviceButtons = document.querySelectorAll('.device-btn');
            
            deviceButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    // إزالة التحديد السابق
                    deviceButtons.forEach(b => b.classList.remove('active'));
                    
                    // تحديد الجهاز الجديد
                    this.classList.add('active');
                    currentDevice = this.dataset.device;
                    
                    updateDeviceDisplay();
                    updateCode();
                    updateStatusBar();
                });
            });
        }

        // تحديث عرض الجهاز
        function updateDeviceDisplay() {
            const device = devices[currentDevice];
            const frame = document.getElementById('deviceFrame');
            const screen = document.getElementById('deviceScreen');
            const info = document.getElementById('deviceInfo');
            
            // حساب الحجم المناسب للعرض
            const maxWidth = 800;
            const maxHeight = 600;
            const scale = Math.min(maxWidth / device.width, maxHeight / device.height, 1);
            
            const displayWidth = device.width * scale;
            const displayHeight = device.height * scale;
            
            // تطبيق الأبعاد
            screen.style.width = displayWidth + 'px';
            screen.style.height = displayHeight + 'px';
            
            // تحديث معلومات الجهاز
            const ratio = (device.width / device.height).toFixed(2);
            info.innerHTML = `
                <strong>الجهاز:</strong> ${device.name}<br>
                <strong>الأبعاد:</strong> ${device.width}×${device.height}<br>
                <strong>النسبة:</strong> ${ratio}<br>
                <strong>الصورة:</strong> ${device.image}
            `;
        }

        // تهيئة السحب والإفلات
        function initializeDragAndDrop() {
            const buttons = document.querySelectorAll('.game-button');

            buttons.forEach(button => {
                makeButtonDraggable(button);
            });

            // أحداث المستند
            document.addEventListener('mousemove', drag);
            document.addEventListener('mouseup', endDrag);
            document.addEventListener('touchmove', drag, { passive: false });
            document.addEventListener('touchend', endDrag);
        }

        // جعل زر قابل للسحب
        function makeButtonDraggable(button) {
            // أحداث الماوس
            button.addEventListener('mousedown', startDrag);

            // أحداث اللمس
            button.addEventListener('touchstart', startDrag, { passive: false });
        }

        // بداية السحب
        function startDrag(e) {
            e.preventDefault();
            
            isDragging = true;
            dragElement = e.target;
            dragElement.classList.add('dragging');
            
            const clientX = e.clientX || e.touches[0].clientX;
            const clientY = e.clientY || e.touches[0].clientY;
            
            const rect = dragElement.getBoundingClientRect();
            dragOffset.x = clientX - rect.left - rect.width / 2;
            dragOffset.y = clientY - rect.top - rect.height / 2;
            
            updateStatusBar(`🚀 سحب ${dragElement.dataset.symbol}...`);
        }

        // السحب
        function drag(e) {
            if (!isDragging || !dragElement) return;
            
            e.preventDefault();
            
            const clientX = e.clientX || e.touches[0].clientX;
            const clientY = e.clientY || e.touches[0].clientY;
            
            const screen = document.getElementById('deviceScreen');
            const screenRect = screen.getBoundingClientRect();
            
            const x = clientX - screenRect.left - dragOffset.x;
            const y = clientY - screenRect.top - dragOffset.y;
            
            const leftPercent = Math.max(5, Math.min(95, (x / screenRect.width) * 100));
            const topPercent = Math.max(5, Math.min(95, (y / screenRect.height) * 100));
            
            dragElement.style.left = leftPercent + '%';
            dragElement.style.top = topPercent + '%';
            
            updateStatusBar(`📍 ${dragElement.dataset.symbol}: ${leftPercent.toFixed(1)}%, ${topPercent.toFixed(1)}%`);
        }

        // إنهاء السحب
        function endDrag() {
            if (!isDragging) return;
            
            isDragging = false;
            
            if (dragElement) {
                dragElement.classList.remove('dragging');
                updateStatusBar(`✅ تم وضع ${dragElement.dataset.symbol} في موقع جديد`);
                dragElement = null;
            }
            
            updateCode();
            
            setTimeout(() => {
                updateStatusBar();
            }, 2000);
        }

        // تفعيل/إلغاء الشبكة
        function toggleGrid() {
            const grid = document.getElementById('gridOverlay');
            grid.classList.toggle('visible');

            const isVisible = grid.classList.contains('visible');
            updateStatusBar(isVisible ? '📐 تم عرض الشبكة' : '📐 تم إخفاء الشبكة');

            setTimeout(() => {
                updateStatusBar();
            }, 2000);
        }

        // إخفاء/إظهار جميع الأزرار
        function toggleAllButtons() {
            const buttons = document.querySelectorAll('.game-button');
            const firstButton = buttons[0];
            const isVisible = firstButton.style.display !== 'none';

            buttons.forEach(button => {
                button.style.display = isVisible ? 'none' : 'flex';
            });

            updateStatusBar(isVisible ? '👁️ تم إخفاء جميع الأزرار' : '👁️ تم إظهار جميع الأزرار');

            setTimeout(() => {
                updateStatusBar();
            }, 2000);
        }

        // إخفاء/إظهار نوع معين من الأزرار
        function toggleButtonType(type) {
            const buttons = document.querySelectorAll(`[data-type="${type}"]`);

            // تحديد معرف الـ checkbox المناسب
            let checkboxId;
            if (type === 'bet-display') {
                checkboxId = 'showBetDisplays';
            } else if (type === 'bet-info') {
                checkboxId = 'showBetInfos';
            } else {
                checkboxId = `show${type.charAt(0).toUpperCase() + type.slice(1)}s`;
            }

            const checkbox = document.getElementById(checkboxId);
            const isVisible = checkbox.checked;

            buttons.forEach(button => {
                button.style.display = isVisible ? 'flex' : 'none';
            });

            const typeNames = {
                'symbol': 'أزرار الرموز',
                'amount': 'أزرار المبالغ',
                'action': 'أزرار الإجراءات',
                'display': 'عناصر العرض',
                'bet-display': 'مستطيلات الرهان',
                'bet-info': 'أرقام الرهان'
            };

            updateStatusBar(isVisible ? `👁️ تم إظهار ${typeNames[type]}` : `👁️ تم إخفاء ${typeNames[type]}`);

            setTimeout(() => {
                updateStatusBar();
            }, 2000);
        }

        // إعادة تعيين المواقع
        function resetPositions() {
            // المواقع الافتراضية لجميع العناصر
            const defaultPositions = {
                // أزرار الرموز
                'BAR': { left: '5.12%', top: '75.23%' },
                'WATERMELON': { left: '22.05%', top: '73.10%' },
                'LEMON': { left: '50.00%', top: '73.50%' },
                'BANANA': { left: '77.95%', top: '73.10%' },
                'APPLE': { left: '94.88%', top: '75.23%' },

                // أزرار المبالغ (القيم الجديدة)
                '5000': { left: '10%', top: '85%' },
                '3000': { left: '25%', top: '85%' },
                '1000': { left: '40%', top: '85%' },
                '500': { left: '55%', top: '85%' },
                '100': { left: '70%', top: '85%' },

                // أزرار الإجراءات
                'SPIN': { left: '85%', top: '85%' },

                // عناصر العرض
                'BALANCE': { left: '15%', top: '10%' },
                'COUNTDOWN': { left: '85%', top: '10%' },

                // مستطيلات الرهان
                'BAR_BET': { left: '5.12%', top: '65%' },
                'WATERMELON_BET': { left: '22.05%', top: '63%' },
                'LEMON_BET': { left: '50.00%', top: '63.5%' },
                'BANANA_BET': { left: '77.95%', top: '63%' },
                'APPLE_BET': { left: '94.88%', top: '65%' },

                // أرقام الرهان
                'TOTAL_BETS': { left: '35%', top: '10%' },
                'CURRENT_BET': { left: '55%', top: '10%' },
                'LAST_WIN': { left: '75%', top: '10%' }
            };

            const buttons = document.querySelectorAll('.game-button');

            buttons.forEach(button => {
                const symbol = button.dataset.symbol;
                if (defaultPositions[symbol]) {
                    button.style.left = defaultPositions[symbol].left;
                    button.style.top = defaultPositions[symbol].top;
                }
            });

            updateCode();
            updateStatusBar('🔄 تم إعادة تعيين جميع المواقع للقيم الافتراضية');

            setTimeout(() => {
                updateStatusBar();
            }, 2000);
        }

        // تحديث الكود
        function updateCode() {
            const device = devices[currentDevice];
            const buttons = document.querySelectorAll('.game-button');

            let code = `// Layout for ${device.name} (${device.width}x${device.height})\n`;
            code += `const ${currentDevice.replace(/-/g, '')}Layout = {\n`;
            code += `  device: {\n`;
            code += `    name: "${device.name}",\n`;
            code += `    width: ${device.width},\n`;
            code += `    height: ${device.height}\n`;
            code += `  },\n`;
            code += `  backgroundImage: '/images/${device.image}',\n\n`;

            // تجميع الأزرار حسب النوع
            const buttonsByType = {
                symbol: [],
                amount: [],
                action: [],
                display: [],
                'bet-display': [],
                'bet-info': []
            };

            buttons.forEach(button => {
                const type = button.dataset.type;
                if (type && buttonsByType[type]) {
                    buttonsByType[type].push(button);
                }
            });

            // أزرار الرموز
            if (buttonsByType.symbol.length > 0) {
                code += `  // أزرار الرموز (الفواكه + BAR)\n`;
                code += `  symbolButtons: {\n`;
                buttonsByType.symbol.forEach((button, index) => {
                    const symbol = button.dataset.symbol;
                    const key = symbol.toLowerCase();
                    const left = button.style.left;
                    const top = button.style.top;
                    code += `    ${key}: { left: '${left}', top: '${top}', name: '${symbol}' }${index < buttonsByType.symbol.length - 1 ? ',' : ''}\n`;
                });
                code += `  },\n\n`;
            }

            // أزرار المبالغ
            if (buttonsByType.amount.length > 0) {
                code += `  // أزرار مبالغ الرهان\n`;
                code += `  amountButtons: {\n`;
                buttonsByType.amount.forEach((button, index) => {
                    const amount = button.dataset.symbol;
                    const left = button.style.left;
                    const top = button.style.top;
                    code += `    amount${amount}: { left: '${left}', top: '${top}', value: ${amount} }${index < buttonsByType.amount.length - 1 ? ',' : ''}\n`;
                });
                code += `  },\n\n`;
            }

            // أزرار الإجراءات
            if (buttonsByType.action.length > 0) {
                code += `  // أزرار الإجراءات\n`;
                code += `  actionButtons: {\n`;
                buttonsByType.action.forEach((button, index) => {
                    const action = button.dataset.symbol;
                    const key = action.toLowerCase();
                    const left = button.style.left;
                    const top = button.style.top;
                    code += `    ${key}: { left: '${left}', top: '${top}', action: '${action}' }${index < buttonsByType.action.length - 1 ? ',' : ''}\n`;
                });
                code += `  },\n\n`;
            }

            // عناصر العرض
            if (buttonsByType.display.length > 0) {
                code += `  // عناصر العرض\n`;
                code += `  displayElements: {\n`;
                buttonsByType.display.forEach((button, index) => {
                    const element = button.dataset.symbol;
                    const key = element.toLowerCase();
                    const left = button.style.left;
                    const top = button.style.top;
                    code += `    ${key}: { left: '${left}', top: '${top}', type: '${element}' }${index < buttonsByType.display.length - 1 ? ',' : ''}\n`;
                });
                code += `  },\n\n`;
            }

            // مستطيلات الرهان
            if (buttonsByType['bet-display'].length > 0) {
                code += `  // مستطيلات الرهان (تظهر مبالغ الرهان على كل رمز)\n`;
                code += `  betRectangles: {\n`;
                buttonsByType['bet-display'].forEach((button, index) => {
                    const element = button.dataset.symbol;
                    const key = element.toLowerCase();
                    const left = button.style.left;
                    const top = button.style.top;
                    code += `    ${key}: { left: '${left}', top: '${top}', symbol: '${element}' }${index < buttonsByType['bet-display'].length - 1 ? ',' : ''}\n`;
                });
                code += `  },\n\n`;
            }

            // أرقام الرهان
            if (buttonsByType['bet-info'].length > 0) {
                code += `  // أرقام الرهان (تظهر في الأعلى)\n`;
                code += `  betNumbers: {\n`;
                buttonsByType['bet-info'].forEach((button, index) => {
                    const element = button.dataset.symbol;
                    const key = element.toLowerCase();
                    const left = button.style.left;
                    const top = button.style.top;
                    code += `    ${key}: { left: '${left}', top: '${top}', info: '${element}' }${index < buttonsByType['bet-info'].length - 1 ? ',' : ''}\n`;
                });
                code += `  }\n`;
            }

            code += `};\n\n`;

            // CSS للتطبيق
            code += `// CSS للتطبيق في المشروع\n`;
            code += `@media screen and (width: ${device.width}px) and (height: ${device.height}px) {\n`;

            buttons.forEach(button => {
                const symbol = button.dataset.symbol;
                const type = button.dataset.type;
                const key = symbol.toLowerCase();
                code += `  .${type}-${key} {\n`;
                code += `    left: ${button.style.left} !important;\n`;
                code += `    top: ${button.style.top} !important;\n`;
                code += `  }\n`;
            });

            code += `}`;

            document.getElementById('codeOutput').textContent = code;
        }

        // تصدير الكود
        function exportCode() {
            const code = document.getElementById('codeOutput').textContent;
            
            // إنشاء ملف للتحميل
            const blob = new Blob([code], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `${currentDevice}-layout.js`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            updateStatusBar('💾 تم تصدير الكود');
            
            setTimeout(() => {
                updateStatusBar();
            }, 2000);
        }

        // نسخ الكود
        function copyCode() {
            const code = document.getElementById('codeOutput').textContent;
            
            navigator.clipboard.writeText(code).then(() => {
                updateStatusBar('📋 تم نسخ الكود للحافظة');
                
                setTimeout(() => {
                    updateStatusBar();
                }, 2000);
            });
        }

        // تحديث شريط الحالة
        function updateStatusBar(message) {
            const statusBar = document.getElementById('statusBar');
            const device = devices[currentDevice];
            
            if (message) {
                statusBar.textContent = message;
            } else {
                statusBar.textContent = `🎯 جاهز للاستخدام | اسحب الأزرار لترتيبها | الجهاز: ${device.name} (${device.width}×${device.height})`;
            }
        }

        // تغيير حجم الأزرار
        function changeButtonSize() {
            const size = document.getElementById('buttonSize').value;
            const buttons = document.querySelectorAll('.game-button');

            buttons.forEach(button => {
                button.style.width = size + 'px';
                button.style.height = size + 'px';

                // تعديل حجم الخط حسب حجم الزر
                const fontSize = Math.max(6, Math.min(12, size * 0.25));
                button.style.fontSize = fontSize + 'px';

                // تعديل سمك الحدود
                const borderWidth = Math.max(1, Math.min(3, size * 0.06));
                button.style.borderWidth = borderWidth + 'px';
            });

            updateStatusBar(`📏 تم تغيير حجم الأزرار إلى ${size}px`);

            setTimeout(() => {
                updateStatusBar();
            }, 2000);
        }

        // تغيير شفافية الأزرار
        function changeButtonOpacity() {
            const opacity = document.getElementById('buttonOpacity').value;
            const buttons = document.querySelectorAll('.game-button');

            buttons.forEach(button => {
                // تحديث لون الخلفية مع الشفافية الجديدة
                button.style.background = `rgba(0, 255, 136, ${opacity})`;
            });

            const percentage = Math.round(opacity * 100);
            updateStatusBar(`🎨 تم تغيير شفافية الأزرار إلى ${percentage}%`);

            setTimeout(() => {
                updateStatusBar();
            }, 2000);
        }

        // إضافة زر جديد
        function addNewButton() {
            const name = document.getElementById('newButtonName').value.trim();
            const type = document.getElementById('newButtonType').value;

            if (!name) {
                alert('يرجى إدخال اسم للزر');
                return;
            }

            const screen = document.getElementById('deviceScreen');
            const newButton = document.createElement('div');

            newButton.className = `game-button ${type}-button`;
            newButton.dataset.symbol = name.toUpperCase();
            newButton.dataset.type = type;
            newButton.style.left = '50%';
            newButton.style.top = '50%';
            newButton.textContent = name.toUpperCase();

            // تطبيق الأنماط حسب النوع
            const typeStyles = {
                symbol: { background: 'rgba(0, 255, 136, 0.8)', borderColor: '#00ff88' },
                amount: { background: 'rgba(255, 165, 0, 0.8)', borderColor: '#ffa500' },
                action: { background: 'rgba(255, 69, 0, 0.8)', borderColor: '#ff4500' },
                display: { background: 'rgba(0, 191, 255, 0.8)', borderColor: '#00bfff' },
                'bet-display': { background: 'rgba(255, 255, 255, 0.9)', borderColor: '#333', color: '#000' },
                'bet-info': { background: 'rgba(255, 215, 0, 0.8)', borderColor: '#ffd700', color: '#000' }
            };

            if (typeStyles[type]) {
                Object.assign(newButton.style, typeStyles[type]);
            }

            screen.appendChild(newButton);

            // تفعيل السحب للزر الجديد
            makeButtonDraggable(newButton);

            // مسح الحقول
            document.getElementById('newButtonName').value = '';

            updateCode();
            updateStatusBar(`➕ تم إضافة زر جديد: ${name}`);

            setTimeout(() => {
                updateStatusBar();
            }, 2000);
        }

        // حذف آخر زر
        function removeLastButton() {
            const buttons = document.querySelectorAll('.game-button');
            if (buttons.length === 0) {
                alert('لا توجد أزرار لحذفها');
                return;
            }

            const lastButton = buttons[buttons.length - 1];
            const buttonName = lastButton.dataset.symbol;

            if (confirm(`هل تريد حذف الزر: ${buttonName}؟`)) {
                lastButton.remove();
                updateCode();
                updateStatusBar(`🗑️ تم حذف الزر: ${buttonName}`);

                setTimeout(() => {
                    updateStatusBar();
                }, 2000);
            }
        }

        // تحميل صورة خلفية مخصصة
        document.getElementById('backgroundUpload').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                const screen = document.getElementById('deviceScreen');
                screen.style.backgroundImage = `url(${e.target.result})`;
                updateStatusBar('🖼️ تم تحميل صورة خلفية جديدة');

                setTimeout(() => {
                    updateStatusBar();
                }, 2000);
            };
            reader.readAsDataURL(file);
        });
    </script>
</body>
</html>
