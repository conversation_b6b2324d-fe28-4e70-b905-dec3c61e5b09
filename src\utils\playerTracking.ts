// نظام تتبع اللاعبين المتقدم
export interface Player {
  id: string;
  name: string;
  totalBets: number;
  totalWinnings: number;
  netProfit: number;
  lastActive: string;
  riskLevel: 'low' | 'medium' | 'high';
  isTracked: boolean;
  betHistory: BetRecord[];
  sessionData: SessionData;
  flags: PlayerFlags;
}

export interface BetRecord {
  timestamp: string;
  symbol: string;
  amount: number;
  result: 'win' | 'loss';
  winAmount?: number;
}

export interface SessionData {
  sessionStart: string;
  totalBetsThisSession: number;
  totalWinningsThisSession: number;
  consecutiveLosses: number;
  consecutiveWins: number;
  averageBetSize: number;
  largestBet: number;
}

export interface PlayerFlags {
  isHighRoller: boolean;
  isSuspicious: boolean;
  isWhale: boolean;
  isNewPlayer: boolean;
  hasUnusualPattern: boolean;
}

export class PlayerTrackingSystem {
  private static instance: PlayerTrackingSystem;
  private players: Map<string, Player> = new Map();
  private settings: TrackingSettings;

  constructor() {
    this.settings = {
      highRollerThreshold: 50000,
      suspiciousActivityThreshold: 100000,
      whaleThreshold: 200000,
      maxConsecutiveLosses: 10,
      unusualPatternThreshold: 0.8,
      autoTrackHighRollers: true,
      autoTrackSuspicious: true
    };
    this.loadPlayers();
  }

  public static getInstance(): PlayerTrackingSystem {
    if (!PlayerTrackingSystem.instance) {
      PlayerTrackingSystem.instance = new PlayerTrackingSystem();
    }
    return PlayerTrackingSystem.instance;
  }

  // تسجيل رهان جديد
  public recordBet(playerId: string, symbol: string, amount: number, result: 'win' | 'loss', winAmount?: number): void {
    const player = this.getOrCreatePlayer(playerId);
    
    // تحديث إحصائيات اللاعب
    player.totalBets += amount;
    if (result === 'win' && winAmount) {
      player.totalWinnings += winAmount;
    }
    player.netProfit = player.totalWinnings - player.totalBets;
    player.lastActive = new Date().toISOString();

    // تسجيل الرهان
    const betRecord: BetRecord = {
      timestamp: new Date().toISOString(),
      symbol,
      amount,
      result,
      winAmount
    };
    player.betHistory.push(betRecord);

    // تحديث بيانات الجلسة
    this.updateSessionData(player, betRecord);

    // تحليل المخاطر
    this.analyzePlayerRisk(player);

    // حفظ البيانات
    this.savePlayers();
  }

  // الحصول على لاعب أو إنشاؤه
  private getOrCreatePlayer(playerId: string): Player {
    if (!this.players.has(playerId)) {
      const newPlayer: Player = {
        id: playerId,
        name: `اللاعب ${playerId}`,
        totalBets: 0,
        totalWinnings: 0,
        netProfit: 0,
        lastActive: new Date().toISOString(),
        riskLevel: 'low',
        isTracked: false,
        betHistory: [],
        sessionData: {
          sessionStart: new Date().toISOString(),
          totalBetsThisSession: 0,
          totalWinningsThisSession: 0,
          consecutiveLosses: 0,
          consecutiveWins: 0,
          averageBetSize: 0,
          largestBet: 0
        },
        flags: {
          isHighRoller: false,
          isSuspicious: false,
          isWhale: false,
          isNewPlayer: true,
          hasUnusualPattern: false
        }
      };
      this.players.set(playerId, newPlayer);
    }
    return this.players.get(playerId)!;
  }

  // تحديث بيانات الجلسة
  private updateSessionData(player: Player, betRecord: BetRecord): void {
    const session = player.sessionData;
    session.totalBetsThisSession += betRecord.amount;
    
    if (betRecord.result === 'win') {
      session.consecutiveWins++;
      session.consecutiveLosses = 0;
      if (betRecord.winAmount) {
        session.totalWinningsThisSession += betRecord.winAmount;
      }
    } else {
      session.consecutiveLosses++;
      session.consecutiveWins = 0;
    }

    // تحديث متوسط حجم الرهان
    const totalBets = player.betHistory.length;
    session.averageBetSize = player.totalBets / totalBets;

    // تحديث أكبر رهان
    if (betRecord.amount > session.largestBet) {
      session.largestBet = betRecord.amount;
    }
  }

  // تحليل مخاطر اللاعب
  private analyzePlayerRisk(player: Player): void {
    const flags = player.flags;
    const session = player.sessionData;

    // تحليل اللاعبين عاليي الرهان
    flags.isHighRoller = session.largestBet >= this.settings.highRollerThreshold;
    flags.isWhale = session.largestBet >= this.settings.whaleThreshold;

    // تحليل النشاط المشبوه
    flags.isSuspicious = this.detectSuspiciousActivity(player);

    // تحليل الأنماط غير العادية
    flags.hasUnusualPattern = this.detectUnusualPatterns(player);

    // تحديث مستوى المخاطر
    player.riskLevel = this.calculateRiskLevel(player);

    // تحديث حالة التتبع
    if (this.settings.autoTrackHighRollers && flags.isHighRoller) {
      player.isTracked = true;
    }
    if (this.settings.autoTrackSuspicious && flags.isSuspicious) {
      player.isTracked = true;
    }
  }

  // اكتشاف النشاط المشبوه
  private detectSuspiciousActivity(player: Player): boolean {
    const session = player.sessionData;
    
    // رهانات كبيرة جداً
    if (session.largestBet >= this.settings.suspiciousActivityThreshold) {
      return true;
    }

    // خسائر متتالية كثيرة
    if (session.consecutiveLosses >= this.settings.maxConsecutiveLosses) {
      return true;
    }

    // رهانات متسارعة
    const recentBets = player.betHistory.slice(-5);
    if (recentBets.length >= 3) {
      const timeSpan = new Date(recentBets[recentBets.length - 1].timestamp).getTime() - 
                      new Date(recentBets[0].timestamp).getTime();
      const minutes = timeSpan / (1000 * 60);
      if (minutes < 2) { // 3 رهانات في أقل من دقيقتين
        return true;
      }
    }

    return false;
  }

  // اكتشاف الأنماط غير العادية
  private detectUnusualPatterns(player: Player): boolean {
    const history = player.betHistory;
    if (history.length < 10) return false;

    // تحليل تكرار الرموز
    const symbolCounts: { [symbol: string]: number } = {};
    history.forEach(bet => {
      symbolCounts[bet.symbol] = (symbolCounts[bet.symbol] || 0) + 1;
    });

    const totalBets = history.length;
    const mostFrequentSymbol = Object.entries(symbolCounts)
      .sort(([,a], [,b]) => b - a)[0];
    
    if (mostFrequentSymbol && (mostFrequentSymbol[1] / totalBets) > this.settings.unusualPatternThreshold) {
      return true; // رهان على رمز واحد بنسبة عالية
    }

    return false;
  }

  // حساب مستوى المخاطر
  private calculateRiskLevel(player: Player): 'low' | 'medium' | 'high' {
    const flags = player.flags;
    const session = player.sessionData;
    
    let riskScore = 0;

    // عوامل المخاطر
    if (flags.isWhale) riskScore += 3;
    if (flags.isHighRoller) riskScore += 2;
    if (flags.isSuspicious) riskScore += 3;
    if (flags.hasUnusualPattern) riskScore += 2;
    if (session.consecutiveLosses >= 5) riskScore += 2;
    if (session.largestBet > 100000) riskScore += 2;

    if (riskScore >= 5) return 'high';
    if (riskScore >= 2) return 'medium';
    return 'low';
  }

  // الحصول على تقرير اللاعبين
  public getPlayersReport(): {
    totalPlayers: number;
    trackedPlayers: number;
    highRiskPlayers: number;
    suspiciousPlayers: number;
    players: Player[];
  } {
    const playersArray = Array.from(this.players.values());
    
    return {
      totalPlayers: playersArray.length,
      trackedPlayers: playersArray.filter(p => p.isTracked).length,
      highRiskPlayers: playersArray.filter(p => p.riskLevel === 'high').length,
      suspiciousPlayers: playersArray.filter(p => p.flags.isSuspicious).length,
      players: playersArray
    };
  }

  // الحصول على لاعبين محددين
  public getPlayersByRiskLevel(riskLevel: 'low' | 'medium' | 'high'): Player[] {
    return Array.from(this.players.values()).filter(p => p.riskLevel === riskLevel);
  }

  public getTrackedPlayers(): Player[] {
    return Array.from(this.players.values()).filter(p => p.isTracked);
  }

  public getSuspiciousPlayers(): Player[] {
    return Array.from(this.players.values()).filter(p => p.flags.isSuspicious);
  }

  // تحديث إعدادات التتبع
  public updateSettings(newSettings: Partial<TrackingSettings>): void {
    this.settings = { ...this.settings, ...newSettings };
    this.saveSettings();
  }

  // تبديل حالة تتبع اللاعب
  public togglePlayerTracking(playerId: string): void {
    const player = this.players.get(playerId);
    if (player) {
      player.isTracked = !player.isTracked;
      this.savePlayers();
    }
  }

  // إعادة تعيين بيانات اللاعب
  public resetPlayerData(playerId: string): void {
    this.players.delete(playerId);
    this.savePlayers();
  }

  // حفظ البيانات
  private savePlayers(): void {
    const data = {
      players: Array.from(this.players.entries()),
      settings: this.settings
    };
    localStorage.setItem('player_tracking_data', JSON.stringify(data));
  }

  private loadPlayers(): void {
    const saved = localStorage.getItem('player_tracking_data');
    if (saved) {
      try {
        const data = JSON.parse(saved);
        this.players = new Map(data.players || []);
        this.settings = { ...this.settings, ...data.settings };
      } catch (error) {
        console.error('خطأ في تحميل بيانات اللاعبين:', error);
      }
    }
  }

  private saveSettings(): void {
    localStorage.setItem('player_tracking_settings', JSON.stringify(this.settings));
  }
}

export interface TrackingSettings {
  highRollerThreshold: number;
  suspiciousActivityThreshold: number;
  whaleThreshold: number;
  maxConsecutiveLosses: number;
  unusualPatternThreshold: number;
  autoTrackHighRollers: boolean;
  autoTrackSuspicious: boolean;
}

// تصدير instance واحد للاستخدام في التطبيق
export const playerTracking = PlayerTrackingSystem.getInstance(); 