import { BetsOnTypes } from '../types/game';
import { ACTIVE_GAME_SQUARES, GAME_SYMBOLS, PAYOUT_MULTIPLIERS } from '../constants/gameConfig';

// إعدادات اللعبة الجديدة
const LIGHT_CYCLES = 3; // عدد الدورات قبل التوقف

// مسار الضوء الصحيح - حركة مرتبة ومتسلسلة
const LIGHT_PATH = [
  // المسار المطلوب: 2 → 3 → 4 → 9 → 14 → 19 → 24 → 23 → 22 → 21 → 20 → 15 → 10 → 5 → 0 → 1 → 2
  2, 3, 4, 9, 14, 19, 24, 23, 22, 21, 20, 15, 10, 5, 0, 1, 2
];

// مسار دوران منتظم ومتسلسل - حركة مرتبة
const SMOOTH_LIGHT_PATH = [
  // المسار المطلوب: 2 → 3 → 4 → 9 → 14 → 19 → 24 → 23 → 22 → 21 → 20 → 15 → 10 → 5 → 0 → 1 → 2
  2, 3, 4, 9, 14, 19, 24, 23, 22, 21, 20, 15, 10, 5, 0, 1, 2
];

// نظام اختيار الموقع الفائز الجديد - يختار فقط من المسار الصحيح
export const selectWinningPosition = (betsOnTypes: BetsOnTypes): number => {
  // console.log('🎯 بدء اختيار الموقع الفائز...');
  // console.log('📊 الرهانات الحالية:', betsOnTypes);
  
  // 1. إنشاء قائمة بالمواقع المتاحة (فقط المربعات الموجودة في المسار)
  const availablePositions = ACTIVE_GAME_SQUARES
    .filter(square => SMOOTH_LIGHT_PATH.includes(square.gridIndex)) // فقط المربعات في المسار
    .map((square, index) => ({
      position: square.gridIndex,
      symbol: square.symbol,
      index: index,
      betAmount: betsOnTypes[square.symbol as keyof BetsOnTypes] || 0,
      type: square.type
    }));
  
  console.log('📍 المربعات في المسار:', availablePositions.map(p => `${p.position}(${p.symbol}-${p.type})`));
  
  // 2. تصنيف المواقع إلى آمنة وخطرة
  const safePositions = availablePositions.filter(pos => pos.betAmount === 0);
  const riskyPositions = availablePositions.filter(pos => pos.betAmount > 0);
  
  // 3. تصنيف المواقع حسب النوع
  const luckyPositions = availablePositions.filter(pos => 
    pos.type === 'luckyDoubleText' || pos.type === 'luckyTripleText'
  );
  const normalPositions = availablePositions.filter(pos => 
    pos.type !== 'luckyDoubleText' && pos.type !== 'luckyTripleText'
  );
  
  console.log('✅ المواقع الآمنة:', safePositions.map(p => `${p.position}(${p.symbol})`));
  console.log('⚠️ المواقع الخطرة:', riskyPositions.map(p => `${p.position}(${p.symbol})`));
  console.log('🍀 مواقع اللاكي:', luckyPositions.map(p => `${p.position}(${p.symbol}-${p.type})`));
  
  // 4. اختيار الموقع الفائز
  let selectedPosition: typeof availablePositions[0];
  
  // إعطاء أولوية للاكي (30% من الحالات)
  const shouldChooseLucky = Math.random() < 0.3 && luckyPositions.length > 0;
  
  if (shouldChooseLucky) {
    // اختيار موقع لاكي عشوائياً
    const randomIndex = Math.floor(Math.random() * luckyPositions.length);
    selectedPosition = luckyPositions[randomIndex];
    console.log('🍀 تم اختيار موقع لاكي عشوائياً');
  } else if (safePositions.length > 0) {
    // إذا كانت هناك مواقع آمنة، اختر منها عشوائياً
    const randomIndex = Math.floor(Math.random() * safePositions.length);
    selectedPosition = safePositions[randomIndex];
    console.log('🎲 تم اختيار موقع آمن عشوائياً');
  } else {
    // إذا لم تكن هناك مواقع آمنة، اختر من الخطرة
    const randomIndex = Math.floor(Math.random() * riskyPositions.length);
    selectedPosition = riskyPositions[randomIndex];
    console.log('⚠️ تم اختيار موقع خطير (لا توجد مواقع آمنة)');
  }
  
  console.log(`🏆 الموقع المختار: ${selectedPosition.position} (${selectedPosition.symbol})`);
  console.log(`💰 الرهان على هذا الرمز: ${selectedPosition.betAmount}`);
  
  return selectedPosition.position;
};

// نظام تسلسل الإضاءة الجديد - دوران منتظم وسلس
export const generateLightSequence = (targetPosition: number, startPosition?: number): number[] => {
  // console.log(`💡 إنشاء تسلسل الإضاءة للوصول إلى الموقع ${targetPosition}`);
  
  const sequence: number[] = [];
  
  // تحديد نقطة البداية
  let currentPosition = startPosition || SMOOTH_LIGHT_PATH[0];
  if (startPosition !== undefined) {
    // البحث عن أقرب موقع في المسار لنقطة البداية
    const startIndex = SMOOTH_LIGHT_PATH.indexOf(startPosition);
    if (startIndex !== -1) {
      currentPosition = startPosition;
      // console.log(`🚀 بدء من الموقع: ${currentPosition}`);
    }
  }
  
  // إضافة الدورات الكاملة بشكل متسلسل ومنتظم
  for (let cycle = 0; cycle < LIGHT_CYCLES; cycle++) {
    // إضافة دورة كاملة من الموقع الحالي
    const currentIndex = SMOOTH_LIGHT_PATH.indexOf(currentPosition);
    if (currentIndex !== -1) {
      // إضافة المسار من الموقع الحالي للنهاية
      sequence.push(...SMOOTH_LIGHT_PATH.slice(currentIndex));
      // إضافة المسار من البداية للموقع الحالي (لإكمال الدورة)
      if (currentIndex > 0) {
        sequence.push(...SMOOTH_LIGHT_PATH.slice(0, currentIndex));
      }
    } else {
      // إذا لم يكن الموقع في المسار، ابدأ من البداية
      sequence.push(...SMOOTH_LIGHT_PATH);
    }
    
    // تحديث الموقع الحالي ليكون آخر موقع في الدورة
    currentPosition = sequence[sequence.length - 1];
    
    // إضافة انتقال سلس بين الدورات (تكرار آخر موقع)
    if (cycle < LIGHT_CYCLES - 1) {
      sequence.push(currentPosition);
    }
  }
  
  // إضافة المسار للوصول إلى الموقع المستهدف
  const targetIndex = SMOOTH_LIGHT_PATH.indexOf(targetPosition);
  if (targetIndex !== -1) {
    // إضافة انتقال سلس من آخر موقع في الدورة الأخيرة
    const lastPosition = sequence[sequence.length - 1];
    const lastIndex = SMOOTH_LIGHT_PATH.indexOf(lastPosition);
    
    // إنشاء مسار سلس للوصول إلى الهدف
    let pathToTarget: number[] = [];
    
    if (lastIndex <= targetIndex) {
      // المسار للأمام
      pathToTarget = SMOOTH_LIGHT_PATH.slice(lastIndex + 1, targetIndex + 1);
    } else {
      // المسار للخلف (عبر نهاية المصفوفة)
      pathToTarget = [
        ...SMOOTH_LIGHT_PATH.slice(lastIndex + 1), // من الموقع الحالي للنهاية
        ...SMOOTH_LIGHT_PATH.slice(0, targetIndex + 1) // من البداية للهدف
      ];
    }
    
    sequence.push(...pathToTarget);
    // console.log(`✅ تم إضافة مسار سلس للوصول إلى الموقع ${targetPosition}`);
  } else {
    // console.log(`❌ الموقع ${targetPosition} غير موجود في المسار!`);
    // إضافة مسار عشوائي كبديل
    const randomEnd = Math.floor(Math.random() * SMOOTH_LIGHT_PATH.length);
    const randomPath = SMOOTH_LIGHT_PATH.slice(0, randomEnd + 1);
    sequence.push(...randomPath);
  }
  
      // console.log(`📊 إجمالي طول التسلسل: ${sequence.length}`);
    // console.log(`🔄 المسار: ${sequence.slice(0, 10).join('→')}...`);
  
  return sequence;
};

// حساب سرعة الإضاءة - سلسة وجميلة مع تدرج طبيعي
export const calculateLightSpeed = (currentIndex: number, totalLength: number): number => {
  const progress = currentIndex / totalLength;

  // منحنى سرعة سلس ومتدرج
  let baseSpeed: number;

  if (progress < 0.1) {
    // البداية: سرعة متوسطة للإحماء
    baseSpeed = 120;
  } else if (progress < 0.3) {
    // التسارع التدريجي: سرعة سريعة
    const accelerationProgress = (progress - 0.1) / 0.2; // 0 to 1
    baseSpeed = 120 - (50 * accelerationProgress); // 120ms إلى 70ms
  } else if (progress < 0.7) {
    // المرحلة السريعة: حركة سلسة وسريعة
    baseSpeed = 70 + Math.sin(progress * Math.PI * 4) * 15; // تذبذب سلس بين 55-85ms
  } else if (progress < 0.9) {
    // التباطؤ التدريجي: للتشويق
    const decelerationProgress = (progress - 0.7) / 0.2; // 0 to 1
    baseSpeed = 70 + (80 * Math.pow(decelerationProgress, 2)); // 70ms إلى 150ms (منحنى تربيعي)
  } else {
    // النهاية: تباطؤ دراماتيكي
    const finalProgress = (progress - 0.9) / 0.1; // 0 to 1
    baseSpeed = 150 + (200 * Math.pow(finalProgress, 3)); // 150ms إلى 350ms (منحنى تكعيبي)
  }

  // إضافة تنوع طفيف للطبيعية (بدون إفراط)
  const naturalVariation = (Math.sin(currentIndex * 0.5) + Math.cos(currentIndex * 0.3)) * 8; // ±16ms تقريباً

  // تأثير نبضة خفيفة للجمال
  const pulseEffect = Math.sin(progress * Math.PI * 2) * 5; // ±5ms

  const finalSpeed = Math.max(50, Math.min(400, baseSpeed + naturalVariation + pulseEffect));

  return Math.round(finalSpeed);
};

// تحديد المربعات الفائزة - مع معالجة المواقع غير الموجودة في المسار
export const determineFinalSelection = (lightPosition: number): number[] => {
  // التحقق من أن الموقع موجود في المسار
  if (!SMOOTH_LIGHT_PATH.includes(lightPosition)) {
    console.log(`❌ الموقع ${lightPosition} غير موجود في المسار!`);
    console.log(`📍 المواقع في المسار: ${SMOOTH_LIGHT_PATH.join(', ')}`);
    
    // اختيار موقع عشوائي من المسار كبديل
    const randomIndex = Math.floor(Math.random() * SMOOTH_LIGHT_PATH.length);
    const alternativePosition = SMOOTH_LIGHT_PATH[randomIndex];
    console.log(`🔄 اختيار موقع بديل: ${alternativePosition}`);
    
    const squareIndex = ACTIVE_GAME_SQUARES.findIndex(sq => sq.gridIndex === alternativePosition);
    if (squareIndex !== -1) {
      const winningSquare = ACTIVE_GAME_SQUARES[squareIndex];
      console.log(`🎯 المربع الفائز البديل: ${squareIndex} (${winningSquare.symbol}) - نوع: ${winningSquare.type}`);
      return [squareIndex];
    }
    
    return [];
  }
  
  const squareIndex = ACTIVE_GAME_SQUARES.findIndex(sq => sq.gridIndex === lightPosition);
  
  if (squareIndex === -1) {
    console.log(`❌ لم يتم العثور على المربع للموقع ${lightPosition}`);
    console.log(`📍 المواقع المتاحة: ${ACTIVE_GAME_SQUARES.map(sq => sq.gridIndex).join(', ')}`);
    return [];
  }
  
  const winningSquare = ACTIVE_GAME_SQUARES[squareIndex];
  console.log(`🎯 المربع الفائز: ${squareIndex} (${winningSquare.symbol}) - نوع: ${winningSquare.type}`);
  return [squareIndex];
};

// حساب الأرباح - مع آلية اللاكي الصحيحة
export const calculateWin = (
  finalSelectedIndices: number[],
  betsOnTypes: BetsOnTypes
): { totalWinAmount: number; collectedSymbols: string[]; messages: string[] } => {
  
  let totalWinAmount = 0;
  let collectedSymbols: string[] = [];
  let messages: string[] = [];
  
  console.log('💰 بدء حساب الأرباح...');
  
  finalSelectedIndices.forEach(index => {
    const squareConfig = ACTIVE_GAME_SQUARES[index];
    const symbol = squareConfig.symbol;
    
    console.log(`🔍 فحص المربع ${index}: ${symbol} - نوع: ${squareConfig.type}`);
    
    if (squareConfig.type === 'luckyDoubleText') {
      // LUCKY 2 - اختيار مربعين عشوائيين
      console.log('🍀 LUCKY 2 تم تفعيله - اختيار مربعين عشوائيين');
      const luckyWins = calculateLuckyWin(betsOnTypes, 2);
      totalWinAmount += luckyWins.totalWin;
      collectedSymbols.push(...luckyWins.symbols);
      messages.push(...luckyWins.messages);
    } else if (squareConfig.type === 'luckyTripleText') {
      // LUCKY 3 - اختيار 3 مربعات عشوائية
      console.log('🍀 LUCKY 3 تم تفعيله - اختيار 3 مربعات عشوائية');
      const luckyWins = calculateLuckyWin(betsOnTypes, 3);
      totalWinAmount += luckyWins.totalWin;
      collectedSymbols.push(...luckyWins.symbols);
      messages.push(...luckyWins.messages);
    } else {
      // مربع عادي
      const bet = betsOnTypes[symbol as keyof BetsOnTypes] || 0;
      let symbolToShow = symbol;
      let multiplier = 1;
      if (symbol === 'BAR') {
        symbolToShow = 'BAR'; // بدون رموز إضافية
        multiplier = PAYOUT_MULTIPLIERS[symbol as keyof typeof PAYOUT_MULTIPLIERS] || 1;
      } else if (squareConfig.type === 'halfFruit') {
        multiplier = 2;
        symbolToShow = `${symbol} x2`;
      } else {
        multiplier = PAYOUT_MULTIPLIERS[symbol as keyof typeof PAYOUT_MULTIPLIERS] || 1;
        symbolToShow = symbol;
      }
      collectedSymbols.push(symbolToShow);
      if (bet > 0) {
        const win = bet * multiplier;
        totalWinAmount += win;
        const symbolName = getSymbolName(symbol);
        console.log(`🎯 ${symbolName}: فزت بـ ${win.toLocaleString()}$ (مضاعف: x${multiplier})`);
      } else {
        const symbolName = getSymbolName(symbol);
        console.log(`😔 لا يوجد رهان على ${symbolName}`);
      }
    }
  });
  
  // لا نضيف رسائل إجمالية للاعب - فقط للكونسول
  if (totalWinAmount > 0) {
    console.log(`🎉 إجمالي الفوز: ${totalWinAmount.toLocaleString()}$`);
  } else {
    console.log('😔 لم تفز هذه المرة، جرب مرة أخرى!');
  }
  
  console.log(`💰 إجمالي الفوز: ${totalWinAmount.toLocaleString()}$`);
  
  return {
    totalWinAmount,
    collectedSymbols,
    messages
  };
};

// دالة اختيار رموز اللاكي اقتصاديًا
const selectLuckyWinnersEconomically = (betsOnTypes: BetsOnTypes, luckyCategories: any[], luckyCount: number) => {
  // رتب الرموز حسب الرهان (من الأقل للأعلى)، مع خلط الرموز المتساوية في الرهان
  const shuffled = [...luckyCategories].sort(() => Math.random() - 0.5);
  const sorted = shuffled.sort((a, b) => (betsOnTypes[a.textKey as keyof BetsOnTypes] || 0) - (betsOnTypes[b.textKey as keyof BetsOnTypes] || 0));
  return sorted.slice(0, luckyCount);
};

// دالة حساب أرباح اللاكي مع حماية اقتصادية
const calculateLuckyWin = (betsOnTypes: BetsOnTypes, luckyCount: number) => {
  let totalWin = 0;
  let symbols: string[] = [];
  let messages: string[] = [];

  // الأصناف المحددة للاكي (9 أصناف بدون تكرار)
  const luckyCategories: any[] = [
    { symbol: 'BAR', name: 'بار', type: 'normal', textKey: 'BAR' },
    { symbol: 'LEMON', name: 'ليمون x2', type: 'halfFruit', textKey: 'LEMON' },
    { symbol: 'APPLE', name: 'تفاح', type: 'normal', textKey: 'APPLE' },
    { symbol: 'APPLE', name: 'تفاح x2', type: 'halfFruit', textKey: 'APPLE' },
    { symbol: 'BANANA', name: 'موز', type: 'normal', textKey: 'BANANA' },
    { symbol: 'BANANA', name: 'موز x2', type: 'halfFruit', textKey: 'BANANA' },
    { symbol: 'WATERMELON', name: 'بطيخ', type: 'normal', textKey: 'WATERMELON' },
    { symbol: 'WATERMELON', name: 'بطيخ x2', type: 'halfFruit', textKey: 'WATERMELON' },
    { symbol: 'LEMON', name: 'ليمون', type: 'normal', textKey: 'LEMON' }
  ];

  // اختيار الرموز الأقل رهانًا أولاً
  const selectedCategories = selectLuckyWinnersEconomically(betsOnTypes, luckyCategories, luckyCount);

  const symbolNames = selectedCategories.map(cat => cat.name);
  console.log(`🎲 الأصناف المختارة اقتصاديًا: ${symbolNames.join(', ')}`);

  // حساب الأرباح لكل صنف مختار
  selectedCategories.forEach(category => {
    let symbolToShow = category.textKey;
    let multiplier = 1;
    let bet = betsOnTypes[category.textKey as keyof BetsOnTypes] || 0;
    if (category.textKey === 'BAR') {
      multiplier = PAYOUT_MULTIPLIERS[category.textKey as keyof typeof PAYOUT_MULTIPLIERS] || 1;
      symbolToShow = 'BAR'; // بدون رموز إضافية
    } else if (category.type === 'halfFruit') {
      multiplier = 2;
      symbolToShow = `${category.textKey} x2`;
      bet = betsOnTypes[category.textKey as keyof BetsOnTypes] || 0;
    } else {
      multiplier = PAYOUT_MULTIPLIERS[category.textKey as keyof typeof PAYOUT_MULTIPLIERS] || 1;
      symbolToShow = category.textKey;
    }
    symbols.push(symbolToShow);
    if (bet > 0) {
      const win = bet * multiplier;
      totalWin += win;
      console.log(`🍀 LUCKY ${luckyCount}: ${category.name} - فزت بـ ${win.toLocaleString()}$ (مضاعف: x${multiplier})`);
    } else {
      console.log(`🍀 LUCKY ${luckyCount}: ${category.name} - لا يوجد رهان`);
    }
  });

  return { totalWin, symbols, messages };
};

// دالة لتحويل الرموز إلى أسماء
const getSymbolName = (symbol: string): string => {
  const symbolNames: { [key: string]: string } = {
    '🍌': 'موز',
    '🍋': 'ليمون',
    '🍎': 'تفاح',
    '🍉': 'بطيخ',
    'BAR': 'بار',
    'LUCKY': 'لاكي',
    'LUCKY 2': 'لاكي 2',
    'LUCKY 3': 'لاكي 3'
  };
  
  return symbolNames[symbol] || symbol;
};

// دالة مساعدة للحصول على رمز عشوائي
export const getRandomSymbol = (): string => {
  const randomIndex = Math.floor(Math.random() * GAME_SYMBOLS.length);
  return GAME_SYMBOLS[randomIndex];
};

// دالة اختبار النظام الجديد
export const testNewSystem = (betsOnTypes: BetsOnTypes) => {
  console.log('🧪 بدء اختبار النظام الجديد...');
  
  const winningPosition = selectWinningPosition(betsOnTypes);
  const lightSequence = generateLightSequence(winningPosition);
  
  console.log('✅ اختبار النظام الجديد مكتمل');
  console.log(`🎯 الموقع الفائز: ${winningPosition}`);
  console.log(`💡 طول تسلسل الإضاءة: ${lightSequence.length}`);
  
  return { winningPosition, lightSequence };
}; 

// دالة خاصة لحركة اللاكي المتعددة
export const generateLuckyAnimation = (luckyType: 'luckyDoubleText' | 'luckyTripleText', betsOnTypes?: BetsOnTypes): {
  sequences: number[][];
  finalPositions: number[];
} => {
  const luckyCount = luckyType === 'luckyDoubleText' ? 2 : 3;
  const sequences: number[][] = [];
  const finalPositions: number[] = [];

  // اختيار مواقع فواكه عشوائية (ليس لاكي!) بدون تكرار الرمز
  const fruitPositions = ACTIVE_GAME_SQUARES
    .map((square, index) => ({ square, index }))
    .filter(({ square }) => {
      return square.symbol && square.symbol !== '' &&
             square.type !== 'luckyDoubleText' &&
             square.type !== 'luckyTripleText' &&
             square.type !== 'inner';
    });

  // ربط كل موقع بقيمة الرهان عليه
  const fruitWithBets = fruitPositions.map(({ square, index }) => ({
    ...square,
    index,
    betAmount: betsOnTypes ? (betsOnTypes[square.symbol as keyof BetsOnTypes] || 0) : 0
  }));

  // رتب الرموز حسب الرهان (من الأقل للأعلى)، مع خلط الرموز المتساوية
  const shuffled = [...fruitWithBets].sort(() => Math.random() - 0.5);
  const sorted = shuffled.sort((a, b) => a.betAmount - b.betAmount);
  const selectedPositions: number[] = [];
  const usedSymbols = new Set<string>();
  for (const item of sorted) {
    if (!usedSymbols.has(item.symbol)) {
      usedSymbols.add(item.symbol);
      selectedPositions.push(item.index);
      if (selectedPositions.length === luckyCount) break;
    }
  }

  // إنشاء تسلسل حركة لكل موقع
  selectedPositions.forEach((targetPosition, index) => {
    const startPosition = index === 0 ? undefined : ACTIVE_GAME_SQUARES[finalPositions[index - 1]]?.gridIndex;
    const targetGridIndex = ACTIVE_GAME_SQUARES[targetPosition].gridIndex;
    const sequence = generateLightSequence(targetGridIndex, startPosition);
    sequences.push(sequence);
    finalPositions.push(targetGridIndex);
  });

  return { sequences, finalPositions };
};

// دالة حساب الأرباح للاكي المتعدد
export const calculateLuckyMultiWin = (
  finalPositions: number[],
  betsOnTypes: BetsOnTypes
): { totalWinAmount: number; collectedSymbols: string[]; messages: string[] } => {

  let totalWinAmount = 0;
  let collectedSymbols: string[] = [];
  let messages: string[] = [];

  const usedSymbols = new Set<string>();

  finalPositions.forEach((gridIndex, index) => {
    const squareIndex = ACTIVE_GAME_SQUARES.findIndex(sq => sq.gridIndex === gridIndex);
    if (squareIndex === -1) return;
    const squareConfig = ACTIVE_GAME_SQUARES[squareIndex];
    // أضف الرمز فقط إذا لم يكن مكرراً
    if (!usedSymbols.has(squareConfig.symbol) && collectedSymbols.length < finalPositions.length) {
      usedSymbols.add(squareConfig.symbol);
      collectedSymbols.push(squareConfig.symbol);
    }
    // حساب الأرباح كما هو
    const bet = betsOnTypes[squareConfig.symbol as keyof BetsOnTypes] || 0;
    let multiplier = 1;
    if (squareConfig.type === 'halfFruit') {
      multiplier = 2;
    } else {
      multiplier = PAYOUT_MULTIPLIERS[squareConfig.symbol as keyof typeof PAYOUT_MULTIPLIERS] || 1;
    }
    if (bet > 0) {
      const win = bet * multiplier;
      totalWinAmount += win;
      const symbolName = getSymbolName(squareConfig.symbol);
      messages.push(`🎉 ربحت ${win.toLocaleString()} من ${symbolName}!`);
    }
  });

  // تأكد أن collectedSymbols لا تتجاوز 2 أو 3 حسب نوع اللاكي
  if (collectedSymbols.length > 3) collectedSymbols = collectedSymbols.slice(0, 3);
  if (collectedSymbols.length > 2 && finalPositions.length === 2) collectedSymbols = collectedSymbols.slice(0, 2);

  if (totalWinAmount > 0) {
    messages.unshift(`🍀 إجمالي أرباح اللاكي: ${totalWinAmount.toLocaleString()}!`);
  } else {
    messages.push('😔 لم تفز هذه المرة، جرب مرة أخرى!');
  }

  return {
    totalWinAmount,
    collectedSymbols,
    messages
  };
}; 