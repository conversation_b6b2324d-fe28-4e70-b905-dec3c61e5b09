// نظام الخلفيات المتعددة للأجهزة المختلفة
// Multi-Device Background System

export interface DeviceConfig {
  name: string;
  width: number;
  height: number;
  backgroundImage: string;
  buttonLayout: ButtonLayout;
}

export interface ButtonLayout {
  [key: string]: {
    left: string;
    top: string;
    name: string;
  };
}

// تكوين الأجهزة المدعومة
export const DEVICE_CONFIGS: DeviceConfig[] = [
  {
    name: 'iPhone SE',
    width: 375,
    height: 667,
    backgroundImage: '/images/bg-375x667.webp',
    buttonLayout: {
      // سيتم تحديثها من الأداة
      button1: { left: '5%', top: '75%', name: 'BAR' },
      button2: { left: '22%', top: '73%', name: 'WATERMELON' },
      button3: { left: '50%', top: '73%', name: '<PERSON>EM<PERSON>' },
      button4: { left: '78%', top: '73%', name: 'BA<PERSON><PERSON>' },
      button5: { left: '95%', top: '75%', name: 'APPLE' },
    }
  },
  {
    name: 'iPhone 12/13/14',
    width: 390,
    height: 844,
    backgroundImage: '/images/bg-390x844.webp',
    buttonLayout: {
      // تم تحديثها من الأداة - iPhone 12/13/14 - مع جميع العناصر الجديدة
      bar: { left: '10.8882%', top: '64.8958%', name: 'BAR' },
      watermelon: { left: '30.3427%', top: '65.099%', name: 'WATERMELON' },
      lemon: { left: '50.3607%', top: '64.9983%', name: 'LEMON' },
      banana: { left: '70.375%', top: '65.0972%', name: 'BANANA' },
      apple: { left: '89.4687%', top: '65.0625%', name: 'APPLE' },
    }
  },
  {
    name: 'iPhone 14 Pro Max',
    width: 428,
    height: 926,
    backgroundImage: '/images/bg-428x926.webp',
    buttonLayout: {
      // تم التعديل: رفع الأزرار للأعلى بشكل واضح
      button1: { left: '5%', top: '60%', name: 'BAR' },
      button2: { left: '22%', top: '58%', name: 'WATERMELON' },
      button3: { left: '50%', top: '58%', name: 'LEMON' },
      button4: { left: '78%', top: '58%', name: 'BANANA' },
      button5: { left: '95%', top: '60%', name: 'APPLE' },
    }
  },
  {
    name: 'Samsung Galaxy S8',
    width: 412,
    height: 915,
    backgroundImage: '/images/bg-412x915.webp',
    buttonLayout: {
      // تم تحديثها لـ Galaxy S8
      bar: { left: '10.8882%', top: '64.8958%', name: 'BAR' },
      watermelon: { left: '30.3427%', top: '65.099%', name: 'WATERMELON' },
      lemon: { left: '50.3607%', top: '64.9983%', name: 'LEMON' },
      banana: { left: '70.375%', top: '65.0972%', name: 'BANANA' },
      apple: { left: '89.4687%', top: '65.0625%', name: 'APPLE' },
    }
  },
  {
    name: 'Samsung Galaxy (360x740)',
    width: 360,
    height: 740,
    backgroundImage: '/images/bg-360x740.webp',
    buttonLayout: {
      // تكوين محسن لـ 360x740 - محدث
      bar: { left: '8%', top: '85%', name: 'BAR' },
      watermelon: { left: '26%', top: '85%', name: 'WATERMELON' },
      lemon: { left: '50%', top: '85%', name: 'LEMON' },
      banana: { left: '74%', top: '85%', name: 'BANANA' },
      apple: { left: '92%', top: '85%', name: 'APPLE' },
    }
  },
  {
    name: 'iPad Portrait',
    width: 768,
    height: 1024,
    backgroundImage: '/images/bg-768x1024.webp',
    buttonLayout: {
      // سيتم تحديثها من الأداة
      button1: { left: '10%', top: '75%', name: 'BAR' },
      button2: { left: '25%', top: '73%', name: 'WATERMELON' },
      button3: { left: '50%', top: '73%', name: 'LEMON' },
      button4: { left: '75%', top: '73%', name: 'BANANA' },
      button5: { left: '90%', top: '75%', name: 'APPLE' },
    }
  },
  {
    name: 'Desktop',
    width: 1920,
    height: 1080,
    backgroundImage: '/images/bg-1920x1080.webp',
    buttonLayout: {
      // سيتم تحديثها من الأداة
      button1: { left: '20%', top: '70%', name: 'BAR' },
      button2: { left: '30%', top: '68%', name: 'WATERMELON' },
      button3: { left: '50%', top: '68%', name: 'LEMON' },
      button4: { left: '70%', top: '68%', name: 'BANANA' },
      button5: { left: '80%', top: '70%', name: 'APPLE' },
    }
  }
];

// دالة تحديد الجهاز الحالي
export function detectCurrentDevice(): DeviceConfig {
  const width = window.innerWidth;
  const height = window.innerHeight;
  
  // console.log(`🔍 Detecting device: ${width}x${height}`);
  
  // للاختبار - إجبار استخدام Galaxy S8
  if (width >= 412 && height >= 915) {
    const galaxyConfig = DEVICE_CONFIGS.find(config => config.name === 'Samsung Galaxy S8');
    if (galaxyConfig) {
      // console.log(`✅ Forcing Galaxy S8: ${galaxyConfig.name}`);
      return galaxyConfig;
    }
  }
  
  // البحث عن تطابق دقيق
  const exactMatch = DEVICE_CONFIGS.find(config => 
    config.width === width && config.height === height
  );
  
  if (exactMatch) {
    // console.log(`✅ Exact match found: ${exactMatch.name}`);
    return exactMatch;
  }
  
  // البحث عن أقرب تطابق
  const closestMatch = DEVICE_CONFIGS.reduce((closest, config) => {
    const currentDiff = Math.abs(config.width - width) + Math.abs(config.height - height);
    const closestDiff = Math.abs(closest.width - width) + Math.abs(closest.height - height);
    
    return currentDiff < closestDiff ? config : closest;
  });
  
      // console.log(`📱 Closest match found: ${closestMatch.name}`);
  return closestMatch;
}

// دالة تحميل الخلفية المناسبة
export function getDeviceBackground(): { image: string; layout: ButtonLayout } {
  const device = detectCurrentDevice();
  
  return {
    image: device.backgroundImage,
    layout: device.buttonLayout
  };
}

// دالة تحديث تكوين جهاز معين
export function updateDeviceConfig(deviceName: string, newLayout: ButtonLayout): void {
  const deviceIndex = DEVICE_CONFIGS.findIndex(config => config.name === deviceName);
  
  if (deviceIndex !== -1) {
    DEVICE_CONFIGS[deviceIndex].buttonLayout = newLayout;
    console.log(`✅ Updated layout for ${deviceName}`);
  } else {
    console.warn(`⚠️ Device ${deviceName} not found`);
  }
}

// دالة إضافة جهاز جديد
export function addDeviceConfig(config: DeviceConfig): void {
  const existingIndex = DEVICE_CONFIGS.findIndex(existing => 
    existing.width === config.width && existing.height === config.height
  );
  
  if (existingIndex !== -1) {
    DEVICE_CONFIGS[existingIndex] = config;
    console.log(`✅ Updated existing device: ${config.name}`);
  } else {
    DEVICE_CONFIGS.push(config);
    console.log(`✅ Added new device: ${config.name}`);
  }
}

// دالة تصدير التكوين الحالي
export function exportCurrentConfig(): string {
  const device = detectCurrentDevice();
  
  const config = {
    device: {
      name: device.name,
      width: device.width,
      height: device.height
    },
    backgroundImage: device.backgroundImage,
    buttonLayout: device.buttonLayout
  };
  
  return JSON.stringify(config, null, 2);
}

// دالة استيراد تكوين من الأداة
export function importConfigFromTool(configString: string): boolean {
  try {
    const config = JSON.parse(configString);
    
    const deviceConfig: DeviceConfig = {
      name: config.device.name,
      width: config.device.width,
      height: config.device.height,
      backgroundImage: config.backgroundImage || `/images/bg-${config.device.width}x${config.device.height}.webp`,
      buttonLayout: config.buttonLayout
    };
    
    addDeviceConfig(deviceConfig);
    return true;
  } catch (error) {
    console.error('❌ Error importing config:', error);
    return false;
  }
}

// دالة التحقق من وجود صورة الخلفية
export async function checkBackgroundExists(imagePath: string): Promise<boolean> {
  try {
    const response = await fetch(imagePath, { method: 'HEAD' });
    return response.ok;
  } catch {
    return false;
  }
}

// دالة الحصول على خلفية احتياطية
export function getFallbackBackground(): { image: string; layout: ButtonLayout } {
  console.log('🔄 Using fallback background');
  
  return {
    image: '/222.jpg', // الصورة الأصلية
    layout: {
      // تخطيط افتراضي
      button1: { left: '5%', top: '75%', name: 'BAR' },
      button2: { left: '22%', top: '73%', name: 'WATERMELON' },
      button3: { left: '50%', top: '73%', name: 'LEMON' },
      button4: { left: '78%', top: '73%', name: 'BANANA' },
      button5: { left: '95%', top: '75%', name: 'APPLE' },
    }
  };
}

// دالة تحميل الخلفية مع التحقق من الوجود
export async function loadDeviceBackground(): Promise<{ image: string; layout: ButtonLayout }> {
  const deviceBackground = getDeviceBackground();
  
  // التحقق من وجود الصورة
  const exists = await checkBackgroundExists(deviceBackground.image);
  
  if (exists) {
    // console.log(`✅ Loading device-specific background: ${deviceBackground.image}`);
    return deviceBackground;
  } else {
    console.log(`⚠️ Device background not found, using fallback: ${deviceBackground.image}`);
    return getFallbackBackground();
  }
}

// دالة تطبيق تخطيط الأزرار
export function applyButtonLayout(layout: ButtonLayout): void {
  Object.entries(layout).forEach(([buttonId, position]) => {
    const element = document.querySelector(`[data-button="${position.name}"]`) as HTMLElement;
    if (element) {
      element.style.position = 'absolute';
      element.style.left = position.left;
      element.style.top = position.top;
      console.log(`📍 Positioned ${position.name} at ${position.left}, ${position.top}`);
    }
  });
}

// أوامر وحدة التحكم للتطوير
if (typeof window !== 'undefined') {
  (window as any).detectDevice = detectCurrentDevice;
  (window as any).getDeviceBackground = getDeviceBackground;
  (window as any).exportConfig = exportCurrentConfig;
  (window as any).importConfig = importConfigFromTool;
  (window as any).loadBackground = loadDeviceBackground;
}
