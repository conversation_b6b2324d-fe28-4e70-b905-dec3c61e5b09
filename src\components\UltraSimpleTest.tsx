import React from 'react';

interface UltraSimpleTestProps {
  balance: number;
  gamePhase: string;
  currentBetValueToApply: number;
}

const UltraSimpleTest: React.FC<UltraSimpleTestProps> = ({
  balance,
  gamePhase,
  currentBetValueToApply
}) => {
  const isBettingPhase = gamePhase === 'betting';

  return (
    <div style={{
      position: 'fixed',
      top: '10px',
      left: '10px',
      background: 'rgba(0,0,0,0.95)',
      color: 'white',
      padding: '15px',
      borderRadius: '8px',
      fontSize: '14px',
      zIndex: 10000,
      border: '2px solid #FFD700',
      minWidth: '250px'
    }}>
      <h4 style={{ color: '#FFD700', marginBottom: '10px' }}>🔧 تشخيص فوري</h4>
      
      <div style={{ marginBottom: '8px' }}>
        <strong>الرصيد:</strong> {balance.toLocaleString()}
      </div>
      
      <div style={{ marginBottom: '8px' }}>
        <strong>مرحلة اللعبة:</strong> {gamePhase}
      </div>
      
      <div style={{ marginBottom: '8px' }}>
        <strong>مرحلة الرهان:</strong> {isBettingPhase ? '✅ نعم' : '❌ لا'}
      </div>
      
      <div style={{ marginBottom: '8px' }}>
        <strong>المبلغ المختار:</strong> {currentBetValueToApply.toLocaleString()}
      </div>

      <div style={{ 
        marginTop: '10px', 
        padding: '8px',
        background: isBettingPhase ? 'rgba(0,255,0,0.2)' : 'rgba(255,0,0,0.2)',
        borderRadius: '5px',
        fontSize: '12px'
      }}>
        <strong>الحالة:</strong> {isBettingPhase ? 'الأزرار يجب أن تعمل' : 'الأزرار معطلة - انتظر مرحلة الرهان'}
      </div>

      <button
        onClick={() => {
          const testDiv = document.querySelector('[data-test="ultra-simple-test"]');
          if (testDiv) {
            testDiv.remove();
          }
        }}
        style={{
          marginTop: '10px',
          padding: '5px 10px',
          background: '#FFD700',
          color: '#000',
          border: 'none',
          borderRadius: '3px',
          cursor: 'pointer',
          fontSize: '12px'
        }}
      >
        إغلاق
      </button>
    </div>
  );
};

export default UltraSimpleTest; 