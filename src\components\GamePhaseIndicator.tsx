import React from 'react';
import { COLORS } from '../theme/colors';

interface GamePhaseIndicatorProps {
  gamePhase: string;
  countdown: number;
}

/**
 * مؤشر مرحلة اللعبة مع العد التنازلي
 */
const GamePhaseIndicator: React.FC<GamePhaseIndicatorProps> = ({ gamePhase, countdown }) => {
  const getPhaseInfo = () => {
    switch (gamePhase) {
      case 'betting':
        return {
          title: '🔥 فترة الرهان النشطة',
          color: '#fff',
          bgColor: 'linear-gradient(90deg, #FF9800 0%, #FFB347 100%)' // برتقالي
        };
      case 'light_animation':
        return {
          title: '🎰 التوهج الناري يدور',
          color: '#fff',
          bgColor: 'linear-gradient(90deg, #4A0E4E 0%, #2D1B3D 100%)' // بنفسجي موحد
        };
      case 'result_display':
        return {
          title: '🏆 عرض النتائج',
          color: '#fff',
          bgColor: 'linear-gradient(90deg, #4A0E4E 0%, #2D1B3D 100%)' // بنفسجي موحد
        };
      default:
        return {
          title: '⏰ اللعبة',
          color: '#fff',
          bgColor: 'linear-gradient(90deg, #4A0E4E 0%, #2D1B3D 100%)'
        };
    }
  };

  const phaseInfo = getPhaseInfo();

  return (
    <div
      className="flex items-center justify-center px-6 rounded-lg shadow-lg border-2 mb-2"
      style={{
        background: phaseInfo.bgColor,
        borderColor: 'transparent',
        color: phaseInfo.color,
        minHeight: '48px',
        height: '48px',
        maxHeight: '48px',
        lineHeight: '48px',
        fontSize: '1.15rem',
        marginTop: '16px',
        paddingTop: 0,
        paddingBottom: 0,
      }}
    >
      <div className="text-center w-full max-w-md">
        <div className="font-bold text-base" style={{ color: phaseInfo.color, lineHeight: '32px' }}>
          {phaseInfo.title}
        </div>
      </div>
      {gamePhase === 'light_animation' && (
        <div className="flex items-center gap-1">
          <div className="w-2 h-2 bg-current rounded-full animate-pulse"></div>
          <div className="w-2 h-2 bg-current rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
          <div className="w-2 h-2 bg-current rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
        </div>
      )}
    </div>
  );
};

export default GamePhaseIndicator;
