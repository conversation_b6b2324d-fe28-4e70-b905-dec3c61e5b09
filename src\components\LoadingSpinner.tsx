import React from 'react';
import { COLORS } from '../theme/colors';

interface LoadingSpinnerProps {
  size?: 'small' | 'medium' | 'large';
  color?: 'gold' | 'purple' | 'white';
  message?: string;
}

/**
 * مكون Loading Spinner مع تصميم يتماشى مع ثيم اللعبة
 */
const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ 
  size = 'medium', 
  color = 'gold',
  message 
}) => {
  const sizeClasses = {
    small: 'w-6 h-6',
    medium: 'w-12 h-12',
    large: 'w-16 h-16'
  };

  const colorStyles = {
    gold: {
      borderColor: `${COLORS.GOLD_ACCENT.BRIGHT} transparent ${COLORS.GOLD_ACCENT.BRIGHT} transparent`,
      textColor: COLORS.GOLD_ACCENT.BRIGHT
    },
    purple: {
      borderColor: `${COLORS.PURPLE_INNER.SHIMMER} transparent ${COLORS.PURPLE_INNER.SHIMMER} transparent`,
      textColor: COLORS.PURPLE_INNER.SHIMMER
    },
    white: {
      borderColor: `${COLORS.COMMON.WHITE} transparent ${COLORS.COMMON.WHITE} transparent`,
      textColor: COLORS.COMMON.WHITE
    }
  };

  return (
    <div className="flex flex-col items-center justify-center gap-3">
      <div 
        className={`${sizeClasses[size]} border-4 border-solid rounded-full animate-spin`}
        style={{
          borderColor: colorStyles[color].borderColor,
          borderWidth: '3px'
        }}
      />
      {message && (
        <div 
          className="text-sm font-semibold animate-pulse"
          style={{ 
            color: colorStyles[color].textColor,
            textShadow: COLORS.GOLD_ACCENT.TEXT_SHADOW
          }}
        >
          {message}
        </div>
      )}
      
      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
        .animate-spin {
          animation: spin 1s linear infinite;
        }
      `}</style>
    </div>
  );
};

export default LoadingSpinner;
