import React from 'react';

interface SimpleDebuggerProps {
  gamePhase: string;
  countdown: number;
  balance: number;
  currentBetValueToApply: number;
  isBettingPhase: boolean;
}

const SimpleDebugger: React.FC<SimpleDebuggerProps> = ({
  gamePhase,
  countdown,
  balance,
  currentBetValueToApply,
  isBettingPhase
}) => {
  return (
    <div style={{
      position: 'fixed',
      top: '10px',
      left: '10px',
      background: 'rgba(0,0,0,0.9)',
      color: 'white',
      padding: '10px',
      borderRadius: '8px',
      fontSize: '12px',
      zIndex: 9999,
      border: '1px solid #FFD700',
      minWidth: '200px'
    }}>
      <h4 style={{ margin: '0 0 8px 0', color: '#FFD700' }}>🔧 تشخيص بسيط</h4>
      
      <div style={{ marginBottom: '5px' }}>
        <strong>مرحلة اللعبة:</strong> {gamePhase}
      </div>
      
      <div style={{ marginBottom: '5px' }}>
        <strong>العد التنازلي:</strong> {countdown}
      </div>
      
      <div style={{ marginBottom: '5px' }}>
        <strong>الرصيد:</strong> {balance.toLocaleString()}
      </div>
      
      <div style={{ marginBottom: '5px' }}>
        <strong>المبلغ المختار:</strong> {currentBetValueToApply.toLocaleString()}
      </div>
      
      <div style={{ marginBottom: '5px' }}>
        <strong>مرحلة الرهان:</strong> {isBettingPhase ? '✅ نعم' : '❌ لا'}
      </div>

      <div style={{ 
        marginTop: '8px', 
        padding: '5px', 
        background: isBettingPhase ? 'rgba(0,255,0,0.2)' : 'rgba(255,0,0,0.2)',
        borderRadius: '4px',
        fontSize: '11px'
      }}>
        <strong>الحالة:</strong> {isBettingPhase ? 'الأزرار يجب أن تعمل' : 'الأزرار معطلة - انتظر مرحلة الرهان'}
      </div>
    </div>
  );
};

export default SimpleDebugger; 