import React, { useState, useEffect } from 'react';

interface PWAManagerProps {
  onInstallPrompt?: (canInstall: boolean) => void;
}

interface BeforeInstallPromptEvent extends Event {
  prompt(): Promise<void>;
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>;
}

const PWAManager: React.FC<PWAManagerProps> = ({ onInstallPrompt }) => {
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [isInstallable, setIsInstallable] = useState(false);
  const [isInstalled, setIsInstalled] = useState(false);
  const [swRegistration, setSwRegistration] = useState<ServiceWorkerRegistration | null>(null);
  const [updateAvailable, setUpdateAvailable] = useState(false);

  // تسجيل Service Worker
  useEffect(() => {
    if ('serviceWorker' in navigator) {
      registerServiceWorker();
    }
  }, []);

  // مراقبة أحداث التثبيت
  useEffect(() => {
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault();
      const event = e as BeforeInstallPromptEvent;
      setDeferredPrompt(event);
      setIsInstallable(true);
      onInstallPrompt?.(true);
    };

    const handleAppInstalled = () => {
      setIsInstalled(true);
      setIsInstallable(false);
      setDeferredPrompt(null);
      onInstallPrompt?.(false);
      console.log('🎉 تم تثبيت التطبيق بنجاح!');
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', handleAppInstalled);

    // فحص إذا كان التطبيق مثبت بالفعل
    if (window.matchMedia('(display-mode: standalone)').matches) {
      setIsInstalled(true);
    }

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);
    };
  }, [onInstallPrompt]);

  // تسجيل Service Worker
  const registerServiceWorker = async () => {
    try {
      const registration = await navigator.serviceWorker.register('/sw.js');
      setSwRegistration(registration);
      
      console.log('✅ تم تسجيل Service Worker بنجاح');

      // مراقبة التحديثات
      registration.addEventListener('updatefound', () => {
        const newWorker = registration.installing;
        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              setUpdateAvailable(true);
              console.log('🔄 تحديث جديد متاح!');
            }
          });
        }
      });

      // مراقبة التحكم في الصفحة
      navigator.serviceWorker.addEventListener('controllerchange', () => {
        window.location.reload();
      });

    } catch (error) {
      console.error('❌ فشل في تسجيل Service Worker:', error);
    }
  };

  // تثبيت التطبيق
  const installApp = async () => {
    if (!deferredPrompt) return;

    try {
      await deferredPrompt.prompt();
      const choiceResult = await deferredPrompt.userChoice;
      
      if (choiceResult.outcome === 'accepted') {
        console.log('👍 المستخدم وافق على التثبيت');
      } else {
        console.log('👎 المستخدم رفض التثبيت');
      }
      
      setDeferredPrompt(null);
      setIsInstallable(false);
    } catch (error) {
      console.error('❌ فشل في تثبيت التطبيق:', error);
    }
  };

  // تطبيق التحديث
  const applyUpdate = () => {
    if (swRegistration?.waiting) {
      swRegistration.waiting.postMessage({ type: 'SKIP_WAITING' });
    }
  };

  // الحصول على حجم التخزين المؤقت
  const getCacheSize = async (): Promise<number> => {
    if (!swRegistration) return 0;

    return new Promise((resolve) => {
      const messageChannel = new MessageChannel();
      messageChannel.port1.onmessage = (event) => {
        if (event.data.type === 'CACHE_SIZE') {
          resolve(event.data.size);
        }
      };

      swRegistration.active?.postMessage(
        { type: 'GET_CACHE_SIZE' },
        [messageChannel.port2]
      );
    });
  };

  // مسح التخزين المؤقت
  const clearCache = async (): Promise<void> => {
    if (!swRegistration) return;

    return new Promise((resolve) => {
      const messageChannel = new MessageChannel();
      messageChannel.port1.onmessage = (event) => {
        if (event.data.type === 'CACHE_CLEARED') {
          resolve();
        }
      };

      swRegistration.active?.postMessage(
        { type: 'CLEAR_CACHE' },
        [messageChannel.port2]
      );
    });
  };

  // تحميل مسبق للصور
  const preloadImages = async (imageUrls: string[]): Promise<void> => {
    if (!swRegistration) return;

    return new Promise((resolve) => {
      const messageChannel = new MessageChannel();
      messageChannel.port1.onmessage = (event) => {
        if (event.data.type === 'IMAGES_PRELOADED') {
          resolve();
        }
      };

      swRegistration.active?.postMessage(
        { type: 'PRELOAD_IMAGES', payload: { images: imageUrls } },
        [messageChannel.port2]
      );
    });
  };

  // إتاحة الوظائف للاستخدام الخارجي
  useEffect(() => {
    if (typeof window !== 'undefined') {
      (window as any).pwaManager = {
        installApp,
        getCacheSize,
        clearCache,
        preloadImages,
        isInstallable,
        isInstalled,
        updateAvailable,
        applyUpdate
      };
    }
  }, [isInstallable, isInstalled, updateAvailable]);

  return (
    <>
      {/* زر التثبيت */}
      {isInstallable && (
        <div
          style={{
            position: 'fixed',
            top: '50px',
            right: '10px',
            zIndex: 10000,
            background: 'linear-gradient(45deg, #4CAF50, #45a049)',
            color: 'white',
            padding: '8px 16px',
            borderRadius: '20px',
            cursor: 'pointer',
            fontSize: '12px',
            fontWeight: 'bold',
            boxShadow: '0 2px 10px rgba(0,0,0,0.3)',
            animation: 'pulse 2s infinite'
          }}
          onClick={installApp}
        >
          📱 تثبيت التطبيق
        </div>
      )}

      {/* إشعار التحديث */}
      {updateAvailable && (
        <div
          style={{
            position: 'fixed',
            bottom: '10px',
            left: '50%',
            transform: 'translateX(-50%)',
            zIndex: 10000,
            background: 'linear-gradient(45deg, #2196F3, #1976D2)',
            color: 'white',
            padding: '12px 20px',
            borderRadius: '25px',
            cursor: 'pointer',
            fontSize: '12px',
            fontWeight: 'bold',
            boxShadow: '0 4px 15px rgba(0,0,0,0.3)',
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
          }}
          onClick={applyUpdate}
        >
          🔄 تحديث متاح - اضغط للتطبيق
        </div>
      )}

      {/* مؤشر الحالة */}
      {isInstalled && (
        <div
          style={{
            position: 'fixed',
            top: '10px',
            right: '10px',
            zIndex: 9999,
            background: 'rgba(76, 175, 80, 0.9)',
            color: 'white',
            padding: '4px 8px',
            borderRadius: '10px',
            fontSize: '10px',
            fontWeight: 'bold'
          }}
        >
          📱 مثبت
        </div>
      )}

      <style>{`
        @keyframes pulse {
          0% { transform: scale(1); }
          50% { transform: scale(1.05); }
          100% { transform: scale(1); }
        }
      `}</style>
    </>
  );
};

export default PWAManager;
