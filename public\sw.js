// Service Worker للعمل دون اتصال
const CACHE_NAME = 'lucky-ocean-v1.0.0';
const STATIC_CACHE = 'lucky-ocean-static-v1.0.0';
const DYNAMIC_CACHE = 'lucky-ocean-dynamic-v1.0.0';

// الملفات الأساسية للتخزين المؤقت
const STATIC_FILES = [
  '/',
  '/index.html',
  '/manifest.json',
  '/222.jpg',
  '/3.png',
  '/6.png',
  '/8.png',
  '/12.png',
  '/30.png'
];

// الملفات الديناميكية (JavaScript, CSS)
const DYNAMIC_FILES = [
  '/static/js/',
  '/static/css/',
  '/assets/'
];

// تثبيت Service Worker
self.addEventListener('install', (event) => {
  console.log('🔧 تثبيت Service Worker...');
  
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then((cache) => {
        console.log('📦 تخزين الملفات الأساسية...');
        return cache.addAll(STATIC_FILES);
      })
      .then(() => {
        console.log('✅ تم تثبيت Service Worker بنجاح');
        return self.skipWaiting();
      })
      .catch((error) => {
        console.error('❌ فشل في تثبيت Service Worker:', error);
      })
  );
});

// تفعيل Service Worker
self.addEventListener('activate', (event) => {
  console.log('🚀 تفعيل Service Worker...');
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            // حذف التخزين المؤقت القديم
            if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
              console.log('🗑️ حذف التخزين المؤقت القديم:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('✅ تم تفعيل Service Worker بنجاح');
        return self.clients.claim();
      })
  );
});

// اعتراض طلبات الشبكة
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);
  
  // تجاهل طلبات غير HTTP
  if (!request.url.startsWith('http')) {
    return;
  }
  
  // استراتيجية Cache First للملفات الثابتة
  if (STATIC_FILES.some(file => request.url.includes(file))) {
    event.respondWith(cacheFirst(request));
    return;
  }
  
  // استراتيجية Network First للملفات الديناميكية
  if (DYNAMIC_FILES.some(pattern => request.url.includes(pattern))) {
    event.respondWith(networkFirst(request));
    return;
  }
  
  // استراتيجية Network First للباقي
  event.respondWith(networkFirst(request));
});

// استراتيجية Cache First
async function cacheFirst(request) {
  try {
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    const networkResponse = await fetch(request);
    const cache = await caches.open(STATIC_CACHE);
    cache.put(request, networkResponse.clone());
    
    return networkResponse;
  } catch (error) {
    console.error('خطأ في Cache First:', error);
    
    // إرجاع صفحة احتياطية للملفات المهمة
    if (request.destination === 'document') {
      return caches.match('/index.html');
    }
    
    throw error;
  }
}

// استراتيجية Network First
async function networkFirst(request) {
  try {
    const networkResponse = await fetch(request);
    
    // تخزين الاستجابة الناجحة
    if (networkResponse.ok) {
      const cache = await caches.open(DYNAMIC_CACHE);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.log('🔄 الشبكة غير متاحة، البحث في التخزين المؤقت...');
    
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // إرجاع صفحة احتياطية للوثائق
    if (request.destination === 'document') {
      return caches.match('/index.html');
    }
    
    throw error;
  }
}

// معالجة رسائل من التطبيق الرئيسي
self.addEventListener('message', (event) => {
  const { type, payload } = event.data;
  
  switch (type) {
    case 'SKIP_WAITING':
      self.skipWaiting();
      break;
      
    case 'GET_CACHE_SIZE':
      getCacheSize().then(size => {
        event.ports[0].postMessage({ type: 'CACHE_SIZE', size });
      });
      break;
      
    case 'CLEAR_CACHE':
      clearAllCaches().then(() => {
        event.ports[0].postMessage({ type: 'CACHE_CLEARED' });
      });
      break;
      
    case 'PRELOAD_IMAGES':
      preloadImages(payload.images).then(() => {
        event.ports[0].postMessage({ type: 'IMAGES_PRELOADED' });
      });
      break;
  }
});

// حساب حجم التخزين المؤقت
async function getCacheSize() {
  const cacheNames = await caches.keys();
  let totalSize = 0;
  
  for (const cacheName of cacheNames) {
    const cache = await caches.open(cacheName);
    const requests = await cache.keys();
    
    for (const request of requests) {
      const response = await cache.match(request);
      if (response) {
        const blob = await response.blob();
        totalSize += blob.size;
      }
    }
  }
  
  return totalSize;
}

// مسح جميع التخزين المؤقت
async function clearAllCaches() {
  const cacheNames = await caches.keys();
  await Promise.all(
    cacheNames.map(cacheName => caches.delete(cacheName))
  );
}

// تحميل مسبق للصور
async function preloadImages(imageUrls) {
  const cache = await caches.open(STATIC_CACHE);
  
  const preloadPromises = imageUrls.map(async (url) => {
    try {
      const response = await fetch(url);
      if (response.ok) {
        await cache.put(url, response);
      }
    } catch (error) {
      console.warn('فشل في تحميل الصورة:', url, error);
    }
  });
  
  await Promise.all(preloadPromises);
}

// معالجة تحديثات الخلفية
self.addEventListener('backgroundsync', (event) => {
  if (event.tag === 'game-data-sync') {
    event.waitUntil(syncGameData());
  }
});

// مزامنة بيانات اللعبة
async function syncGameData() {
  try {
    // مزامنة البيانات المحفوظة محلياً مع الخادم
    const gameData = await getStoredGameData();
    if (gameData) {
      await sendGameDataToServer(gameData);
      await clearStoredGameData();
    }
  } catch (error) {
    console.error('فشل في مزامنة بيانات اللعبة:', error);
  }
}

// الحصول على البيانات المحفوظة
async function getStoredGameData() {
  // تنفيذ منطق الحصول على البيانات من IndexedDB أو localStorage
  return null;
}

// إرسال البيانات للخادم
async function sendGameDataToServer(data) {
  // تنفيذ منطق إرسال البيانات
  return Promise.resolve();
}

// مسح البيانات المحفوظة
async function clearStoredGameData() {
  // تنفيذ منطق مسح البيانات
  return Promise.resolve();
}
