import React, { useState, useEffect } from 'react';
import { BetsOnTypes } from '../types/game';
import { getCurrentDeviceConfig, getButtonStyle } from '../utils/buttonPositions';
// import { GAME_TEXT_STYLES } from '../theme/typography';
// import { COLORS } from '../theme/colors';

interface BettingControlsProps {
  balance: number;
  betsOnTypes: BetsOnTypes;
  pendingBetsOnTypes: BetsOnTypes;
  currentBetValueToApply: number;
  isBettingPhase: boolean;
  onBetValueSelect: (value: number) => void;
  onSymbolBet: (symbol: keyof BetsOnTypes) => void;
  onSpin: () => void;
  gamePhase?: 'betting' | 'light_animation' | 'result_display';
}

const BettingControls: React.FC<BettingControlsProps> = ({
  balance,
  betsOnTypes,
  pendingBetsOnTypes,
  currentBetValueToApply,
  isBettingPhase,
  onBetValueSelect,
  onSymbolBet,
  onSpin,
  gamePhase = 'betting'
}) => {
  const symbols = [
    { symbol: 'BAR' },
    { symbol: 'WATERMELON' },
    { symbol: 'LEMON' },
    { symbol: 'BANANA' },
    { symbol: 'APPLE' }
  ];

  const [isBarActive, setIsBarActive] = React.useState(false);
  const [pressed, setPressed] = useState<string | null>(null);

  // إضافة CSS animation للإضاءة الذهبية
  useEffect(() => {
    const styleElement = document.getElementById('golden-glow-keyframes');
    if (!styleElement) {
      const style = document.createElement('style');
      style.id = 'golden-glow-keyframes';
      style.textContent = `
        @keyframes goldenPulse {
          0% {
            box-shadow: 
              0 0 25px rgba(255, 215, 0, 1),
              0 0 50px rgba(255, 215, 0, 0.8),
              0 0 75px rgba(255, 215, 0, 0.6),
              0 0 100px rgba(255, 215, 0, 0.4),
              inset 0 0 15px rgba(255, 255, 255, 0.3);
            filter: brightness(1);
            border-color: #ffd700 !important;
          }
          100% {
            box-shadow: 
              0 0 35px rgba(255, 215, 0, 1),
              0 0 70px rgba(255, 215, 0, 1),
              0 0 105px rgba(255, 215, 0, 0.8),
              0 0 140px rgba(255, 215, 0, 0.6),
              inset 0 0 25px rgba(255, 255, 255, 0.5);
            filter: brightness(1.3);
            border-color: #ffed4e !important;
          }
        }
        
        @keyframes goldenRotate {
          0% {
            filter: brightness(1) hue-rotate(0deg) saturate(1);
          }
          25% {
            filter: brightness(1.1) hue-rotate(5deg) saturate(1.2);
          }
          50% {
            filter: brightness(1.2) hue-rotate(10deg) saturate(1.4);
          }
          75% {
            filter: brightness(1.1) hue-rotate(5deg) saturate(1.2);
          }
          100% {
            filter: brightness(1) hue-rotate(0deg) saturate(1);
          }
        }
        
        @keyframes fruitGoldenPulse {
          0% {
            opacity: 0.5;
            transform: translate(-50%, -50%) scale(1);
            boxShadow: 0 0 25px rgba(255, 215, 0, 0.6), 0 0 50px rgba(255, 215, 0, 0.4);
          }
          100% {
            opacity: 0.8;
            transform: translate(-50%, -50%) scale(1.1);
            boxShadow: 0 0 35px rgba(255, 215, 0, 0.8), 0 0 70px rgba(255, 215, 0, 0.6);
          }
        }

        @keyframes selectedPulse {
          0%, 100% {
            boxShadow: 0 8px 25px rgba(0, 0, 0, 0.4), 0 0 30px rgba(255, 215, 0, 0.8), inset 0 0 15px rgba(255, 255, 255, 0.3);
          }
          50% {
            boxShadow: 0 8px 25px rgba(0, 0, 0, 0.4), 0 0 40px rgba(255, 215, 0, 1), inset 0 0 20px rgba(255, 255, 255, 0.4);
          }
        }
      `;
      document.head.appendChild(style);
    }
  }, []);

  // أضف كود CSS للنبض الذهبي لمرة واحدة فقط
  useEffect(() => {
    if (!document.getElementById('golden-pulse-keyframes')) {
      const style = document.createElement('style');
      style.id = 'golden-pulse-keyframes';
      style.textContent = `
        @keyframes goldenPulse {
          0% {
            transform: translate(-50%, -50%) scale(1);
            opacity: 1;
            box-shadow: 0 0 0 0 rgba(255, 215, 0, 0.18);
          }
          50% {
            transform: translate(-50%, -50%) scale(1.18);
            opacity: 0.85;
            box-shadow: 0 0 12px 6px rgba(255, 215, 0, 0.22);
          }
          100% {
            transform: translate(-50%, -50%) scale(1);
            opacity: 1;
            box-shadow: 0 0 0 0 rgba(255, 215, 0, 0.18);
          }
        }
      `;
      document.head.appendChild(style);
    }
  }, []);

    // استخدام النظام الجديد للحصول على تكوين الجهاز
  const deviceConfig = getCurrentDeviceConfig();
  
  // console.log('Current device:', deviceConfig.device.name);
  // console.log('Screen size:', window.innerWidth, 'x', window.innerHeight);

  const balancePos = deviceConfig.topDisplays.balanceDisplay;
  const totalBetPos = deviceConfig.topDisplays.totalBetDisplay;
  const symbolPositions = deviceConfig.symbolButtons;
  const betRectangles = deviceConfig.betRectangles;
  const amountButtons = deviceConfig.amountButtons;

  return (
    <div>
      {/* حاوية الرصيد - لليسار */}
      <div style={{
        position: 'fixed',
        left: balancePos.left,
        top: `calc(${balancePos.top} + 6px)`, // إنزال الرصيد للأسفل قليلاً جداً
        zIndex: 1000,
        pointerEvents: 'auto',
        transform: 'translate(-50%, 0)',
      }}>
        <div className="balance-display">
          <div style={{
            fontFamily: 'Roboto Mono, monospace',
            fontSize: '11px', // تصغير الخط أكثر
            fontWeight: '700',
            color: '#FFFFFF', // لون أبيض
            textShadow: '0 0 4px rgba(255, 255, 255, 0.8), 0 1px 2px rgba(0, 0, 0, 0.8)',
            letterSpacing: '0.5px',
            textAlign: 'center'
          }}>
            {balance.toLocaleString()}
          </div>
        </div>
      </div>

      {/* حاوية الرهان - منفصلة لليمين */}
      <div style={{
        position: 'fixed',
        left: `calc(${totalBetPos.left} + 70px)`, // تحريك الرهان لليسار قليلاً
        top: `calc(${totalBetPos.top} + 6px)`,
        zIndex: 1000,
        pointerEvents: 'auto',
        width: '120px',
        transform: 'translate(-50%, 0)',
      }}>
        <div className="total-bet-display">
          <div style={{
            fontFamily: 'Roboto Mono, monospace',
            fontSize: '12px', // تصغير الخط
            fontWeight: '600',
            color: '#FFFFFF', // لون أبيض
            textShadow: '0 0 4px rgba(255, 255, 255, 0.8)',
            letterSpacing: '0.3px',
            textAlign: 'left',
            paddingLeft: '11px'
          }}>
            {Object.values(betsOnTypes).reduce((sum, bet) => sum + bet, 0).toLocaleString()}
          </div>
        </div>
      </div>

      {/* أزرار الفواكه الأصلية */}
      <div style={{
        position: 'absolute',
        top: '0%',
        left: '0%',
        width: '100%',
        height: '100%',
        zIndex: 20
      }}>
        {symbols.map(({ symbol }, index) => {
        const canBet = isBettingPhase && currentBetValueToApply > 0;
        let imgSrc = '';
        if (symbol === 'BAR') imgSrc = '/images/222.jpg';
        else if (symbol === 'WATERMELON') imgSrc = '/images/12.png';
        else if (symbol === 'LEMON') imgSrc = '/images/8.png';
        else if (symbol === 'BANANA') imgSrc = '/images/6.png';
        else if (symbol === 'APPLE') imgSrc = '/images/3.png';

        // استخدام النظام الجديد للحصول على المواقع
        const buttonStyle = getButtonStyle(deviceConfig.device.name, 'symbol', symbol);
        
        // console.log(`${symbol} button style:`, buttonStyle);

        if (!buttonStyle.left || !buttonStyle.top) return null;

        // إذا لم تكن مرحلة الرهان، لا تظهر أي شيء
        if (gamePhase !== 'betting') {
          return null;
        }
        const isPressed = pressed === symbol;
        const hasBet = betsOnTypes[symbol as keyof BetsOnTypes] > 0;
        const shouldShow = isPressed || hasBet; // إظهار عند النقر أو عند وجود رهان
        
        return (
          <div
            key={`fruit-container-${symbol}`}
            style={{
              position: 'absolute',
              left: buttonStyle.left,
              top: buttonStyle.top,
              transform: 'translate(-50%, -50%)',
              zIndex: 20,
              cursor: 'pointer',
              pointerEvents: 'auto',
              width: '45px',
              height: '45px',
              background: 'none',
              border: 'none',
              boxShadow: 'none',
              padding: 0,
              margin: 0,
            }}
            onMouseDown={() => setPressed(symbol)}
            onMouseUp={() => setPressed(null)}
            onMouseLeave={() => setPressed(null)}
            onTouchStart={() => setPressed(symbol)}
            onTouchEnd={() => setPressed(null)}
            onClick={() => {
              if (gamePhase === 'betting') {
                onSymbolBet(symbol as keyof BetsOnTypes);
              }
            }}
            title={symbol}
          >
            <div
              style={{
                width: '50px',
                height: '50px',
                borderRadius: '10px',
                background: 'transparent', // إزالة الخلفية
                border: 'none', // إزالة الإطار
                boxShadow: 'none', // إزالة الظلال
                opacity: shouldShow ? 0.7 : 0, // إظهار شفاف عند النقر أو الرهان، مخفي تماماً في الحالات الأخرى
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                pointerEvents: 'none',
                padding: '0', // إزالة الحشو
                margin: 0,
                transition: 'all 0.15s ease',
                position: 'absolute',
                left: '50%',
                top: '50%',
                transform: 'translate(-50%, -50%) rotateX(20deg)',
                animation: isPressed ? 'goldenPulse 1.6s infinite' : 'none',
              }}
            >
              {/* الصور فقط - بدون إطار أو مضاعفات */}
              {symbol === 'BAR' ? (
                <div style={{
                  width: '45px',
                  height: '45px',
                  background: 'linear-gradient(135deg, #8B4513 0%, #A0522D 50%, #CD853F 100%)',
                  borderRadius: '8px',
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  border: '2px solid #654321',
                  boxShadow: 'inset 0 2px 4px rgba(255, 255, 255, 0.2), 0 2px 4px rgba(0, 0, 0, 0.3)',
                  filter: 'drop-shadow(0 0 8px rgba(255, 215, 0, 0.8))',
                  transition: 'all 0.15s ease',
                  opacity: shouldShow ? 0.7 : 0 // إظهار شفاف عند النقر أو الرهان
                }}>
                  <div style={{
                    fontSize: '8px',
                    fontWeight: 'bold',
                    color: '#FFD700',
                    textShadow: '0 1px 2px rgba(0, 0, 0, 0.8), 0 0 4px rgba(255, 215, 0, 0.6)',
                    lineHeight: '1',
                    letterSpacing: '0.5px',
                    textAlign: 'center'
                  }}>
                    BAR
                  </div>
                  <div style={{
                    fontSize: '8px',
                    fontWeight: 'bold',
                    color: '#FFD700',
                    textShadow: '0 1px 2px rgba(0, 0, 0, 0.8), 0 0 4px rgba(255, 215, 0, 0.6)',
                    lineHeight: '1',
                    letterSpacing: '0.5px',
                    textAlign: 'center'
                  }}>
                    BAR
                  </div>
                  <div style={{
                    fontSize: '8px',
                    fontWeight: 'bold',
                    color: '#FFD700',
                    textShadow: '0 1px 2px rgba(0, 0, 0, 0.8), 0 0 4px rgba(255, 215, 0, 0.6)',
                    lineHeight: '1',
                    letterSpacing: '0.5px',
                    textAlign: 'center'
                  }}>
                    BAR
                  </div>
                </div>
              ) : (
                <img 
                  src={imgSrc} 
                  alt={symbol} 
                  style={{ 
                    width: '45px', 
                    height: '45px', 
                    objectFit: 'contain',
                    filter: 'drop-shadow(0 0 8px rgba(255, 215, 0, 0.8))',
                    transition: 'all 0.15s ease',
                    opacity: shouldShow ? 0.7 : 0 // إظهار شفاف عند النقر أو الرهان
                  }} 
                />
              )}
            </div>
          </div>
        );
        })}
      </div>

      {/* أزرار المبالغ */}
      {Object.entries(amountButtons).map(([key, pos]) => {
        const isSelected = currentBetValueToApply === pos.value;
        const canSelect = isBettingPhase;

        return (
          <div
            key={`amount-${key}`}
            className={`amount-button ${isSelected ? 'selected' : ''} ${!canSelect ? 'disabled' : ''}`}
            style={{
              position: 'absolute',
              left: pos.left,
              top: pos.top,
              width: '40px',
              height: '40px',
              borderRadius: '50%',
              background: isSelected
                ? 'linear-gradient(135deg, #FFD700, #FFA500)'
                : 'linear-gradient(135deg, #F5DEB3 0%, #DEB887 100%)',
              border: isSelected
                ? '3px solid #FFD700'
                : '2px solid #8B7355',
              color: isSelected ? '#000000' : '#FFD700',
              fontSize: '10px',
              fontWeight: '700',
              fontFamily: 'Roboto Mono, monospace',
              textShadow: isSelected
                ? '0 1px 1px rgba(0,0,0,0.8)'
                : '0 1px 1px rgba(255,255,255,0.8)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: canSelect ? 'pointer' : 'not-allowed',
              opacity: 1, // إظهار دائماً للضبط
              transform: 'translate(-50%, -50%)',
              transition: 'all 0.3s ease',
              zIndex: 20,
              boxShadow: isSelected
                ? '0 8px 25px rgba(0, 0, 0, 0.4), 0 0 30px rgba(255, 215, 0, 0.8), inset 0 0 10px rgba(255, 255, 255, 0.3)'
                : '0 2px 4px rgba(0, 0, 0, 0.3)',
              animation: isSelected ? 'selectedPulse 2s ease-in-out infinite' : 'none'
            }}
            onClick={() => {
              if (canSelect) {
                onBetValueSelect(pos.value || 0);
              }
            }}
            onMouseEnter={(e) => {
              if (canSelect && !isSelected) {
                e.currentTarget.style.transform = 'translate(-50%, -50%) scale(1.1)';
                e.currentTarget.style.borderColor = '#FFD700';
                e.currentTarget.style.boxShadow = '0 6px 20px rgba(0, 0, 0, 0.3), 0 0 25px rgba(255, 215, 0, 0.5)';
              }
            }}
            onMouseLeave={(e) => {
              if (canSelect && !isSelected) {
                e.currentTarget.style.transform = 'translate(-50%, -50%) scale(1)';
                e.currentTarget.style.borderColor = '#8B7355';
                e.currentTarget.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.3)';
              }
            }}
            title={`رهان ${pos.value}$`}
          >
            <span style={{
              fontSize: '9px',
              lineHeight: '1',
              letterSpacing: '0.5px'
            }}>
              {pos.value}$
            </span>
          </div>
        );
      })}

      {/* عرض أرقام الرهانات فوق كل زر */}
      {symbols.map(({ symbol }) => {
        const betRect = betRectangles[symbol.toLowerCase()];
        if (!betRect) return null;

        const totalBet = (betsOnTypes[symbol as keyof BetsOnTypes] || 0) +
                        (pendingBetsOnTypes[symbol as keyof BetsOnTypes] || 0);

        // إذا كان البطيخ، أضف 1 سم لليمين فقط
        let leftValue = betRect.left;
        if (symbol === 'WATERMELON') leftValue = `calc(${betRect.left} + 10px)`;
        return (
          <div
            key={`bet-${symbol}`}
            className={`bet-display bet-${symbol.toLowerCase()}`}
            style={{
              position: 'absolute',
              left: leftValue,
              top: betRect.top,
              zIndex: 25,
              transform: 'translate(-50%, -50%)',
              background: 'transparent',
              display: 'block', // إظهار دائماً للضبط
            }}
          >
            <span style={{
              fontFamily: 'Roboto Mono, monospace',
              fontSize: '14px',
              fontWeight: '700',
              color: '#FF0000',
              textShadow: 'none',
              textAlign: 'center'
            }}>
              {totalBet.toLocaleString()}
            </span>
          </div>
        );
      })}
    </div>
  );
};

export default BettingControls;
