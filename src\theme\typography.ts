// 🔤 نظام الخطوط المركزي - تحسين القراءة والجاذبية البصرية

export const TYPOGRAPHY = {
  // 📚 عائلات الخطوط
  FONT_FAMILIES: {
    // خط رئيسي للعناوين والنصوص المهمة
    PRIMARY: '"<PERSON><PERSON><PERSON>", "Times New Roman", serif',
    // خط ثانوي للنصوص العادية
    SECONDARY: '"Playfair Display", "Georgia", serif',
    // خط للأرقام والبيانات
    NUMERIC: '"Roboto Mono", "Courier New", monospace',
    // خط للواجهة العامة
    UI: '"Inter", "Segoe UI", "Roboto", sans-serif',
    // خط للعناوين الكبيرة
    DISPLAY: '"<PERSON><PERSON><PERSON>", "Playfair Display", serif'
  },

  // 📏 أحجام الخطوط
  FONT_SIZES: {
    // أحجام صغيرة
    XS: '10px',
    SM: '12px',
    BASE: '14px',
    MD: '16px',
    
    // أحجام متوسطة
    LG: '18px',
    XL: '20px',
    '2XL': '24px',
    '3XL': '28px',
    
    // أحجام كبيرة
    '4XL': '32px',
    '5XL': '40px',
    '6XL': '48px',
    '7XL': '56px',
    
    // أحجام عملاقة
    '8XL': '64px',
    '9XL': '72px'
  },

  // ⚖️ أوزان الخطوط
  FONT_WEIGHTS: {
    THIN: '100',
    LIGHT: '300',
    NORMAL: '400',
    MEDIUM: '500',
    SEMIBOLD: '600',
    BOLD: '700',
    EXTRABOLD: '800',
    BLACK: '900'
  },

  // 📐 ارتفاع الأسطر
  LINE_HEIGHTS: {
    TIGHT: '1.1',
    SNUG: '1.25',
    NORMAL: '1.5',
    RELAXED: '1.75',
    LOOSE: '2'
  },

  // 🔤 تباعد الأحرف
  LETTER_SPACING: {
    TIGHTER: '-0.05em',
    TIGHT: '-0.025em',
    NORMAL: '0',
    WIDE: '0.025em',
    WIDER: '0.05em',
    WIDEST: '0.1em'
  }
};

// 🎨 أنماط النصوص المحددة مسبقاً
export const TEXT_STYLES = {
  // 🏆 عناوين رئيسية
  HEADING_LARGE: {
    fontFamily: TYPOGRAPHY.FONT_FAMILIES.DISPLAY,
    fontSize: TYPOGRAPHY.FONT_SIZES['6XL'],
    fontWeight: TYPOGRAPHY.FONT_WEIGHTS.BOLD,
    lineHeight: TYPOGRAPHY.LINE_HEIGHTS.TIGHT,
    letterSpacing: TYPOGRAPHY.LETTER_SPACING.TIGHT,
    textShadow: '0 2px 4px rgba(0,0,0,0.8), 0 0 8px rgba(255,215,0,0.6)'
  },

  // 📝 عناوين متوسطة
  HEADING_MEDIUM: {
    fontFamily: TYPOGRAPHY.FONT_FAMILIES.PRIMARY,
    fontSize: TYPOGRAPHY.FONT_SIZES['3XL'],
    fontWeight: TYPOGRAPHY.FONT_WEIGHTS.SEMIBOLD,
    lineHeight: TYPOGRAPHY.LINE_HEIGHTS.SNUG,
    letterSpacing: TYPOGRAPHY.LETTER_SPACING.NORMAL,
    textShadow: '0 1px 2px rgba(0,0,0,0.6)'
  },

  // 🔖 عناوين صغيرة
  HEADING_SMALL: {
    fontFamily: TYPOGRAPHY.FONT_FAMILIES.PRIMARY,
    fontSize: TYPOGRAPHY.FONT_SIZES.XL,
    fontWeight: TYPOGRAPHY.FONT_WEIGHTS.MEDIUM,
    lineHeight: TYPOGRAPHY.LINE_HEIGHTS.SNUG,
    letterSpacing: TYPOGRAPHY.LETTER_SPACING.WIDE
  },

  // 📄 نص عادي
  BODY_LARGE: {
    fontFamily: TYPOGRAPHY.FONT_FAMILIES.SECONDARY,
    fontSize: TYPOGRAPHY.FONT_SIZES.MD,
    fontWeight: TYPOGRAPHY.FONT_WEIGHTS.NORMAL,
    lineHeight: TYPOGRAPHY.LINE_HEIGHTS.NORMAL,
    letterSpacing: TYPOGRAPHY.LETTER_SPACING.NORMAL
  },

  // 📝 نص متوسط
  BODY_MEDIUM: {
    fontFamily: TYPOGRAPHY.FONT_FAMILIES.UI,
    fontSize: TYPOGRAPHY.FONT_SIZES.BASE,
    fontWeight: TYPOGRAPHY.FONT_WEIGHTS.NORMAL,
    lineHeight: TYPOGRAPHY.LINE_HEIGHTS.NORMAL,
    letterSpacing: TYPOGRAPHY.LETTER_SPACING.NORMAL
  },

  // 📋 نص صغير
  BODY_SMALL: {
    fontFamily: TYPOGRAPHY.FONT_FAMILIES.UI,
    fontSize: TYPOGRAPHY.FONT_SIZES.SM,
    fontWeight: TYPOGRAPHY.FONT_WEIGHTS.NORMAL,
    lineHeight: TYPOGRAPHY.LINE_HEIGHTS.RELAXED,
    letterSpacing: TYPOGRAPHY.LETTER_SPACING.NORMAL
  },

  // 🔢 أرقام كبيرة (الرصيد، الرهانات)
  NUMERIC_LARGE: {
    fontFamily: TYPOGRAPHY.FONT_FAMILIES.NUMERIC,
    fontSize: TYPOGRAPHY.FONT_SIZES['2XL'],
    fontWeight: TYPOGRAPHY.FONT_WEIGHTS.BOLD,
    lineHeight: TYPOGRAPHY.LINE_HEIGHTS.TIGHT,
    letterSpacing: TYPOGRAPHY.LETTER_SPACING.WIDE,
    textShadow: '0 1px 2px rgba(0,0,0,0.8)'
  },

  // 🔢 أرقام متوسطة
  NUMERIC_MEDIUM: {
    fontFamily: TYPOGRAPHY.FONT_FAMILIES.NUMERIC,
    fontSize: TYPOGRAPHY.FONT_SIZES.LG,
    fontWeight: TYPOGRAPHY.FONT_WEIGHTS.SEMIBOLD,
    lineHeight: TYPOGRAPHY.LINE_HEIGHTS.TIGHT,
    letterSpacing: TYPOGRAPHY.LETTER_SPACING.NORMAL
  },

  // 🔢 أرقام صغيرة
  NUMERIC_SMALL: {
    fontFamily: TYPOGRAPHY.FONT_FAMILIES.NUMERIC,
    fontSize: TYPOGRAPHY.FONT_SIZES.BASE,
    fontWeight: TYPOGRAPHY.FONT_WEIGHTS.MEDIUM,
    lineHeight: TYPOGRAPHY.LINE_HEIGHTS.TIGHT,
    letterSpacing: TYPOGRAPHY.LETTER_SPACING.NORMAL
  },

  // 🎮 نصوص الأزرار
  BUTTON_LARGE: {
    fontFamily: TYPOGRAPHY.FONT_FAMILIES.PRIMARY,
    fontSize: TYPOGRAPHY.FONT_SIZES.LG,
    fontWeight: TYPOGRAPHY.FONT_WEIGHTS.SEMIBOLD,
    lineHeight: TYPOGRAPHY.LINE_HEIGHTS.TIGHT,
    letterSpacing: TYPOGRAPHY.LETTER_SPACING.WIDE,
    textTransform: 'uppercase' as const
  },

  BUTTON_MEDIUM: {
    fontFamily: TYPOGRAPHY.FONT_FAMILIES.UI,
    fontSize: TYPOGRAPHY.FONT_SIZES.BASE,
    fontWeight: TYPOGRAPHY.FONT_WEIGHTS.MEDIUM,
    lineHeight: TYPOGRAPHY.LINE_HEIGHTS.TIGHT,
    letterSpacing: TYPOGRAPHY.LETTER_SPACING.NORMAL
  },

  BUTTON_SMALL: {
    fontFamily: TYPOGRAPHY.FONT_FAMILIES.UI,
    fontSize: TYPOGRAPHY.FONT_SIZES.SM,
    fontWeight: TYPOGRAPHY.FONT_WEIGHTS.MEDIUM,
    lineHeight: TYPOGRAPHY.LINE_HEIGHTS.TIGHT,
    letterSpacing: TYPOGRAPHY.LETTER_SPACING.NORMAL
  },

  // ⚠️ نصوص التنبيهات
  ALERT_TITLE: {
    fontFamily: TYPOGRAPHY.FONT_FAMILIES.PRIMARY,
    fontSize: TYPOGRAPHY.FONT_SIZES.XL,
    fontWeight: TYPOGRAPHY.FONT_WEIGHTS.BOLD,
    lineHeight: TYPOGRAPHY.LINE_HEIGHTS.TIGHT,
    letterSpacing: TYPOGRAPHY.LETTER_SPACING.NORMAL,
    textShadow: '0 1px 2px rgba(0,0,0,0.8)'
  },

  ALERT_MESSAGE: {
    fontFamily: TYPOGRAPHY.FONT_FAMILIES.SECONDARY,
    fontSize: TYPOGRAPHY.FONT_SIZES.MD,
    fontWeight: TYPOGRAPHY.FONT_WEIGHTS.NORMAL,
    lineHeight: TYPOGRAPHY.LINE_HEIGHTS.NORMAL,
    letterSpacing: TYPOGRAPHY.LETTER_SPACING.NORMAL
  }
};

// 🎯 أنماط خاصة بعناصر اللعبة
export const GAME_TEXT_STYLES = {
  // 💰 عرض الرصيد
  BALANCE_DISPLAY: {
    ...TEXT_STYLES.NUMERIC_LARGE,
    color: '#FFD700',
    textShadow: '0 0 4px rgba(255, 255, 255, 0.8), 0 1px 2px rgba(0,0,0,0.8)'
  },

  // 🎲 عرض الرهان الإجمالي
  TOTAL_BET_DISPLAY: {
    ...TEXT_STYLES.NUMERIC_MEDIUM,
    color: '#FFFFFF',
    textShadow: '0 0 4px rgba(255, 255, 255, 0.8)'
  },

  // 🔢 أرقام الرهانات على الأزرار
  BET_AMOUNT_DISPLAY: {
    ...TEXT_STYLES.NUMERIC_SMALL,
    color: '#FF0000',
    fontWeight: TYPOGRAPHY.FONT_WEIGHTS.BOLD,
    textShadow: '0 0 4px rgba(255, 215, 0, 0.8)'
  },

  // ⏰ العد التنازلي
  COUNTDOWN_DISPLAY: {
    ...TEXT_STYLES.NUMERIC_LARGE,
    fontSize: TYPOGRAPHY.FONT_SIZES['4XL'],
    color: '#FFD700',
    textShadow: '0 0 8px rgba(255, 215, 0, 0.8), 0 2px 4px rgba(0,0,0,0.8)'
  },

  // 🎯 نصوص الأزرار الرئيسية
  PRIMARY_BUTTON: {
    ...TEXT_STYLES.BUTTON_LARGE,
    color: '#8B4513',
    textShadow: '0 1px 1px rgba(255,255,255,0.8)'
  },

  // 🔘 نصوص أزرار المبالغ
  AMOUNT_BUTTON: {
    ...TEXT_STYLES.BUTTON_SMALL,
    color: '#FFD700',
    textShadow: '0 1px 1px rgba(0,0,0,0.8)'
  },

  // 🍎 نصوص أزرار الفواكه
  FRUIT_BUTTON: {
    ...TEXT_STYLES.BUTTON_SMALL,
    color: '#000000',
    textShadow: '0 1px 1px rgba(255,255,255,0.8)'
  },

  // 📢 رسائل التنبيه
  PHASE_ALERT: {
    ...TEXT_STYLES.ALERT_MESSAGE,
    fontSize: TYPOGRAPHY.FONT_SIZES.LG,
    color: '#FFD700',
    textShadow: '0 1px 2px rgba(0,0,0,0.8)'
  }
};

// 🔧 دوال مساعدة للخطوط
export const typographyHelpers = {
  // تطبيق نمط نص
  applyTextStyle: (style: any) => ({
    fontFamily: style.fontFamily,
    fontSize: style.fontSize,
    fontWeight: style.fontWeight,
    lineHeight: style.lineHeight,
    letterSpacing: style.letterSpacing,
    textShadow: style.textShadow,
    color: style.color,
    textTransform: style.textTransform
  }),

  // إنشاء نمط نص مخصص
  createTextStyle: (options: {
    family?: string;
    size?: string;
    weight?: string;
    lineHeight?: string;
    letterSpacing?: string;
    color?: string;
    shadow?: string;
  }) => ({
    fontFamily: options.family || TYPOGRAPHY.FONT_FAMILIES.UI,
    fontSize: options.size || TYPOGRAPHY.FONT_SIZES.BASE,
    fontWeight: options.weight || TYPOGRAPHY.FONT_WEIGHTS.NORMAL,
    lineHeight: options.lineHeight || TYPOGRAPHY.LINE_HEIGHTS.NORMAL,
    letterSpacing: options.letterSpacing || TYPOGRAPHY.LETTER_SPACING.NORMAL,
    color: options.color || '#000000',
    textShadow: options.shadow || 'none'
  }),

  // تحسين القراءة للنصوص الصغيرة
  enhanceReadability: (baseStyle: any) => ({
    ...baseStyle,
    textRendering: 'optimizeLegibility',
    fontSmooth: 'antialiased',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale'
  })
};
