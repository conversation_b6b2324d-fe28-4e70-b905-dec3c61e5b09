import React from 'react';

interface HistoryBoxProps {
  results: Array<{ symbols: string[] }>; // الآن كل نتيجة مصفوفة رموز فقط
}

// دالة لجلب صورة الرمز
const getSymbolImage = (symbol: string) => {
  switch (symbol.toUpperCase()) {
    case 'APPLE': return '/images/3.png';
    case 'BANANA': return '/images/6.png';
    case 'LEMON': return '/images/8.png';
    case 'WATERMELON': return '/images/12.png';
    case 'BAR': return '/images/30.png';
    default: return '';
  }
};

const HistoryBox: React.FC<HistoryBoxProps> = ({ results }) => {
  // عرض آخر 20 نتيجة فقط (الأقدم يميناً، الأحدث يساراً)
  const lastTwenty = results.slice(-20);

  return (
    <div style={{
              width: '100%',
      height: 46,
      position: 'fixed',
      left: '49.5%',
      bottom: 9,
      transform: 'translateX(-50%)',
      background: '#111',
      border: 'none',
      boxShadow: 'none',
      borderRadius: 0,
      fontSize: 16,
      color: '#fff',
      zIndex: 9999,
      overflowX: 'auto',
      overflowY: 'hidden',
      display: 'flex',
      alignItems: 'center',
      gap: 8,
      direction: 'ltr',
      padding: '0 8px',
      scrollbarWidth: 'thin',
    }}>
      {lastTwenty.length === 0 ? (
        <span style={{fontSize: 13, color: '#ccc'}}>لا توجد نتائج بعد</span>
      ) : (
        lastTwenty.map((result, idx) => {
          // حماية من undefined أو خطأ في البيانات
          const symbols = Array.isArray(result?.symbols) ? result.symbols : (result?.symbols ? [result.symbols] : []);
          if (symbols.length > 1) {
            // مربع ذهبي فقط إذا كانت النتيجة أكثر من رمز (لاكي)
            return (
              <div key={idx} style={{
                minWidth: 48 + 22 * (symbols.length - 1),
                minHeight: 38,
                width: 48 + 22 * (symbols.length - 1),
                height: 38,
                background: '#b8860b', // ذهبي داكن فقط للاكي
                borderRadius: 10,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontWeight: 'bold',
                fontSize: 14,
                color: '#fff',
                border: '2px solid #a97c1b',
                boxShadow: 'none',
                position: 'relative',
                overflow: 'hidden',
                padding: 0,
                gap: 2,
              }}>
                {symbols.map((symbolStr, i) => {
                  const match = symbolStr.match(/^([A-Z]+)(?: x(\d+))?$/i);
                  const symbol = match ? match[1] : symbolStr;
                  const multiplier = match && match[2] ? match[2] : null;
                  const imgSrc = getSymbolImage(symbol);
                  const isBar = symbol.toUpperCase() === 'BAR';
                  return (
                    <div key={i} style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', margin: '0 1px' }}>
                      {isBar ? (
                        <div style={{
                          width: 18,
                          height: 18,
                          background: '#8B5C2A',
                          borderRadius: 4,
                          display: 'flex',
                          flexDirection: 'column',
                          alignItems: 'center',
                          justifyContent: 'center',
                          fontWeight: 'bold',
                          fontSize: 7,
                          color: '#FFD700',
                          lineHeight: 1.1,
                          textAlign: 'center',
                          boxShadow: 'none',
                        }}>
                          {'BAR'}<br />{'BAR'}<br />{'BAR'}
                        </div>
                      ) : (
                        imgSrc ? (
                          <img src={imgSrc} alt={symbol} style={{ width: 18, height: 18, objectFit: 'contain' }} />
                        ) : (
                          <span>{symbol}</span>
                        )
                      )}
                      {multiplier && !isBar && (
                        <span style={{
                          position: 'relative',
                          top: 1,
                          background: '#FFD700',
                          color: '#3a1859',
                          borderRadius: '50%',
                          fontSize: 7,
                          fontWeight: 900,
                          width: 11,
                          height: 11,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          border: 'none',
                          boxShadow: 'none',
                          lineHeight: 1,
                          padding: 0,
                        }}>
                          x{multiplier}
                        </span>
                      )}
                    </div>
                  );
                })}
              </div>
            );
          } else {
            // مربع عادي: خلفية بنفسجية داكنة
            const symbolStr = symbols[0] || '';
            const match = symbolStr.match(/^([A-Z]+)(?: x(\d+))?$/i);
            const symbol = match ? match[1] : symbolStr;
            const multiplier = match && match[2] ? match[2] : null;
            const imgSrc = getSymbolImage(symbol);
            const isBar = symbol.toUpperCase() === 'BAR';
            return (
              <div key={idx} style={{
                minWidth: 38,
                minHeight: 38,
                width: 38,
                height: 38,
                background: '#4a267a', // بنفسجي داكن فقط للنتائج العادية
                borderRadius: 8,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontWeight: 'bold',
                fontSize: 14,
                color: '#fff',
                border: 'none',
                boxShadow: 'none',
                position: 'relative',
                overflow: 'hidden',
                padding: 0,
              }}>
                {isBar ? (
                  <div style={{
                    width: 28,
                    height: 28,
                    background: '#8B5C2A',
                    borderRadius: 6,
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontWeight: 'bold',
                    fontSize: 10,
                    color: '#FFD700',
                    lineHeight: 1.1,
                    textAlign: 'center',
                    boxShadow: 'none',
                  }}>
                    {'BAR'}<br />{'BAR'}<br />{'BAR'}
                  </div>
                ) : (
                  imgSrc ? (
                    <img src={imgSrc} alt={symbol} style={{ width: 28, height: 28, objectFit: 'contain' }} />
                  ) : (
                    <span>{symbol}</span>
                  )
                )}
                {multiplier === '2' && !isBar && (
                  <span style={{
                    position: 'absolute',
                    bottom: 2,
                    left: '50%',
                    transform: 'translateX(-50%)',
                    background: '#FFD700',
                    color: '#3a1859',
                    borderRadius: '50%',
                    fontSize: 10,
                    fontWeight: 900,
                    width: 15,
                    height: 15,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    border: 'none',
                    boxShadow: 'none',
                    lineHeight: 1,
                    padding: 0,
                  }}>
                    x2
                  </span>
                )}
              </div>
            );
          }
        })
      )}
    </div>
  );
};

export default HistoryBox; 