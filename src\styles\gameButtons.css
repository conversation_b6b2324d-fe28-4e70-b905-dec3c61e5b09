/* تنسيقات أزرار اللعبة المحسنة */

.game-button {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  user-select: none;
  backdrop-filter: blur(5px);
  box-sizing: border-box;
  font-weight: bold;
  text-align: center;
  transform: translate(-50%, -50%);
}

/* أزرار الرموز (الفواكه) */
.symbol-button {
  width: 60px;
  height: 60px;
  background: rgba(0, 0, 0, 0.7);
  border: 2px solid #00ff88;
  color: #00ff88;
  font-size: 12px;
  z-index: 20;
}

.symbol-button:hover {
  transform: translate(-50%, -50%) scale(1.1);
  border-color: #00ffff;
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
}

.symbol-button:active {
  transform: translate(-50%, -50%) scale(0.95);
}

.symbol-button.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  border-color: #666;
}

.symbol-button.disabled:hover {
  transform: translate(-50%, -50%) scale(1);
  border-color: #666;
  box-shadow: none;
}

/* أزرار المبالغ */
.amount-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.7);
  border: 2px solid #666;
  color: #FFD700;
  font-size: 10px;
  z-index: 30;
}

.amount-button:hover {
  transform: translate(-50%, -50%) scale(1.1);
  border-color: #00ffff;
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
}

.amount-button.selected {
  background: linear-gradient(135deg, #FFD700, #FFA500);
  border: 3px solid #FFD700;
  color: #000;
  box-shadow: 0 0 20px rgba(255, 215, 0, 0.8), inset 0 0 10px rgba(255, 255, 255, 0.3);
}

.amount-button.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.amount-button.disabled:hover {
  transform: translate(-50%, -50%) scale(1);
  border-color: #666;
  box-shadow: none;
}

/* مربعات عرض الرهانات */
.bet-display {
  position: absolute;
  z-index: 25;
  color: #FFD700;
  font-weight: bold;
  font-size: 14px;
  text-align: center;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.35); /* خلفية أغمق وأقل شفافية */
  padding: 4px 8px;
  border-radius: 8px;
  border: none; /* حذف الإطار */
  min-width: 40px;
  text-shadow: none; /* إزالة مؤثرات الخط */
}

/* عروض الرصيد والرهان الإجمالي */
.balance-display {
  position: fixed;
  z-index: 1000;
  pointer-events: auto;
  transform: translate(-50%, 0);
}

.balance-display .balance-text {
  font-size: 18px;
  font-weight: bold;
  color: #fff;
  text-shadow: 0 0 4px rgba(255, 255, 255, 0.8);
}

.total-bet-display {
  position: fixed;
  z-index: 1000;
  pointer-events: auto;
  width: 120px;
  transform: translate(-50%, 0);
}

.total-bet-display .bet-text {
  font-size: 18px;
  font-weight: bold;
  color: #fff;
  text-align: left;
  padding-left: 11px;
  text-shadow: 0 0 4px rgba(255, 255, 255, 0.8);
}

/* تأثيرات خاصة لزر BAR */
.bar-button {
  transform: translate(-50%, -50%) skewX(-18deg);
}

.bar-button.active {
  transform: translate(-50%, -50%) translateY(6px) scale(0.92) skewX(-18deg);
}

.bar-button .bar-content {
  width: 40px;
  height: 32px;
  background: transparent;
  border: 4px solid #FFD700;
  opacity: 1;
  border-radius: 6px;
  margin-left: -14px;
  margin-top: -6px;
  margin-right: -8px;
  margin-bottom: -16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  clip-path: polygon(8% 0, 92% 0, 100% 100%, 0 100%);
  transition: transform 0.12s cubic-bezier(.4,2,.6,1);
}

.bar-button.active .bar-content {
  transform: translateY(-6px) scale(0.92);
}

.bar-button .bar-inner {
  width: 32px;
  height: 18px;
  background: #6B3F19;
  border-radius: 4px;
  margin: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  opacity: 1;
}

.bar-button .bar-text {
  color: #FFD700;
  font-weight: bold;
  font-size: 10px;
  line-height: 10px;
  transform: skewX(0deg);
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .symbol-button {
    width: 50px;
    height: 50px;
    font-size: 10px;
  }

  .amount-button {
    width: 35px;
    height: 35px;
    font-size: 9px;
  }
  
  .bet-display {
    font-size: 12px;
    padding: 2px 6px;
  }
  
  .balance-display .balance-text,
  .total-bet-display .bet-text {
    font-size: 16px;
  }
}

/* تحسينات للشاشات الكبيرة */
@media (min-width: 1200px) {
  .symbol-button {
    width: 70px;
    height: 70px;
    font-size: 14px;
  }

  .amount-button {
    width: 45px;
    height: 45px;
    font-size: 12px;
  }
  
  .bet-display {
    font-size: 16px;
    padding: 6px 10px;
  }
  
  .balance-display .balance-text,
  .total-bet-display .bet-text {
    font-size: 20px;
  }
}

/* تأثيرات الإضاءة والتوهج */
.glow-effect {
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
  from {
    box-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
  }
  to {
    box-shadow: 0 0 20px rgba(0, 255, 136, 0.8), 0 0 30px rgba(0, 255, 136, 0.6);
  }
}

.pulse-effect {
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    transform: translate(-50%, -50%) scale(1.05);
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
  }
}
