// Game Grid Layout for Samsung Galaxy (412x915)
const galaxyGridLayout = {
  device: {
    name: "Samsung Galaxy",
    width: 412,
    height: 915
  },
  gameGrid: {
    position: {
      left: "49.6935%",
      top: "41.3333%",
      width: "242px",
      height: "237px"
    },
    squares: [
      { gridIndex: 0, symbol: 'APPLE', type: 'normal' },
      { gridIndex: 1, symbol: 'LEMON', type: 'halfFruit' },
      { gridIndex: 2, symbol: 'BAR', type: 'stackedBar' },
      { gridIndex: 3, symbol: 'LEMON', type: 'normal' },
      { gridIndex: 4, symbol: 'APPLE', type: 'normal' },
      { gridIndex: 5, symbol: 'BANANA', type: 'halfFruit' },
      { gridIndex: 6, symbol: '', type: 'inner' },
      { gridIndex: 7, symbol: '', type: 'inner' },
      { gridIndex: 8, symbol: '', type: 'inner' },
      { gridIndex: 9, symbol: 'APPLE', type: 'halfFruit' },
      { gridIndex: 10, symbol: 'WATERMELON', type: 'normal' },
      { gridIndex: 11, symbol: '', type: 'inner' },
      { gridIndex: 12, symbol: '', type: 'inner' },
      { gridIndex: 13, symbol: '', type: 'inner' },
      { gridIndex: 14, symbol: 'BAR', type: 'normal' },
      { gridIndex: 15, symbol: 'WATERMELON', type: 'halfFruit' },
      { gridIndex: 16, symbol: '', type: 'inner' },
      { gridIndex: 17, symbol: '', type: 'inner' },
      { gridIndex: 18, symbol: '', type: 'inner' },
      { gridIndex: 19, symbol: 'BANANA', type: 'normal' },
      { gridIndex: 20, symbol: 'APPLE', type: 'normal' },
      { gridIndex: 21, symbol: 'BANANA', type: 'normal' },
      { gridIndex: 22, symbol: 'WATERMELON', type: 'normal' },
      { gridIndex: 23, symbol: 'WATERMELON', type: 'halfFruit' },
      { gridIndex: 24, symbol: 'APPLE', type: 'normal' }
    ]
  }
};

// CSS للتطبيق
@media screen and (width: 412px) and (height: 915px) {
  .game-grid {
    left: 49.6935% !important;
    top: 41.3333% !important;
    width: 242px !important;
    height: 237px !important;
  }
}