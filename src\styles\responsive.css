/* نظام العرض المتجاوب المحسن */

/* الأساسيات */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  /* إضافة خصائص للجوال */
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}

/* تحسينات خاصة للجوال */
@media screen and (max-width: 768px) {
  html, body {
    /* منع التمرير والتكبير */
    overflow: hidden;
    position: fixed;
    width: 100vw;
    height: 100vh;
    /* منع مشاكل Safari على iOS */
    -webkit-overflow-scrolling: touch;
    /* تأكد من الحجم الصحيح */
    min-height: 100vh;
    min-width: 100vw;
  }

  #root {
    width: 100vw !important;
    height: calc(var(--vh, 1vh) * 100) !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    overflow: hidden !important;
  }
}

#root {
  width: 100vw;
  height: 100vh;
  position: relative;
}

/* الخلفية المتجاوبة */
.responsive-background {
  transition: all 0.3s ease;
  /* تأكد من عدم تكرار الصورة */
  background-repeat: no-repeat !important;
  /* تحسين جودة الصورة */
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  /* تحسين الأداء */
  will-change: background-size, background-position;
  /* تأكد من تغطية كامل المنطقة */
  min-height: 100vh;
  min-width: 100vw;
  /* إضافة خصائص لضمان التغطية الكاملة */
  background-origin: border-box;
  background-clip: border-box;
}

/* قاعدة خاصة لجميع أجهزة الجوال */
@media screen and (max-width: 768px) {
  .responsive-background {
    background-size: cover !important;
    background-position: center center !important;
    background-attachment: scroll !important;
    /* تأكد من التغطية الكاملة باستخدام الارتفاع الديناميكي */
    width: 100vw !important;
    height: calc(var(--vh, 1vh) * 100) !important;
    min-width: 100vw !important;
    min-height: calc(var(--vh, 1vh) * 100) !important;
    /* منع أي مساحات فارغة */
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    outline: none !important;
    /* تأكد من عدم وجود overflow */
    overflow: hidden !important;
    /* تحسين خاص للشاشات الطويلة */
    background-origin: border-box !important;
    background-clip: border-box !important;
  }
}

/* تحسين خاص للشاشات الطويلة جداً (iPhone 14 Pro Max وما شابه) */
@media screen and (max-width: 430px) and (min-height: 920px) {
  .responsive-background {
    /* تأكد من عدم قطع الأجزاء المهمة */
    background-position: center center !important;
    background-size: cover !important;
    /* تمديد إضافي للشاشات الطويلة */
    min-height: calc(var(--vh, 1vh) * 100) !important;
    height: calc(var(--vh, 1vh) * 100) !important;
  }
}

/* Classes خاصة بأنواع الأجهزة */
.mobile-bg {
  /* تحسينات خاصة بالهاتف المحمول */
  background-attachment: scroll; /* تجنب مشاكل iOS */
}

.tablet-bg {
  /* تحسينات خاصة بالتابلت */
  background-attachment: fixed;
}

.desktop-bg {
  /* تحسينات خاصة بالكمبيوتر */
  background-attachment: fixed;
}

/* Classes خاصة بالاتجاه */
.portrait-bg {
  /* تحسينات للوضع العمودي */
}

.landscape-bg {
  /* تحسينات للوضع الأفقي */
}

/* الهاتف المحمول - عمودي */
@media screen and (max-width: 768px) and (orientation: portrait) {
  .responsive-background {
    background-size: cover !important;
    background-position: center center !important;
    /* تأكد من تغطية كامل الشاشة */
    min-height: 100vh;
    min-width: 100vw;
    /* إضافة تغطية إضافية للأجهزة الصغيرة */
    height: 100vh;
    width: 100vw;
    /* تأكد من عدم وجود مساحات فارغة */
    background-repeat: no-repeat !important;
    /* تمديد الخلفية لتغطي المنطقة كاملة */
    background-attachment: scroll;
  }

  .game-container {
    padding: 8px;
  }

  .game-board {
    transform: scale(0.85);
    top: 25% !important;
  }

  .betting-controls {
    bottom: 15px;
  }

  .amount-controls {
    bottom: 8px;
  }

  .balance-display {
    font-size: 14px !important;
    top: 8px !important;
    left: 8px !important;
  }

  .fruit-button {
    width: 45px !important;
    height: 45px !important;
  }

  .amount-button {
    width: 35px !important;
    height: 35px !important;
  }
}

/* الهاتف المحمول - أفقي */
@media screen and (max-width: 768px) and (orientation: landscape) {
  .responsive-background {
    background-size: cover !important;
    background-position: center center !important;
    /* تأكد من تغطية كامل الشاشة الأفقية */
    min-height: 100vh;
    min-width: 100vw;
  }

  .game-board {
    transform: scale(0.75);
    top: 20% !important;
    left: 30% !important;
  }

  .betting-controls {
    right: 10px;
    bottom: 60px;
  }

  .amount-controls {
    right: 10px;
    bottom: 20px;
  }

  .balance-display {
    font-size: 12px !important;
    top: 5px !important;
    left: 5px !important;
  }

  .fruit-button {
    width: 40px !important;
    height: 40px !important;
  }

  .amount-button {
    width: 30px !important;
    height: 30px !important;
  }
}

/* التابلت */
@media screen and (min-width: 769px) and (max-width: 1024px) {
  .responsive-background {
    background-size: contain !important;
    background-position: center center !important;
    /* خلفية احتياطية للمناطق الفارغة */
    background-color: #1a1a2e !important;
  }

  .game-board {
    transform: scale(1.0);
    top: 30% !important;
  }

  .fruit-button {
    width: 55px !important;
    height: 55px !important;
  }

  .amount-button {
    width: 45px !important;
    height: 45px !important;
  }

  .balance-display {
    font-size: 16px !important;
  }
}

/* الكمبيوتر */
@media screen and (min-width: 1025px) {
  .responsive-background {
    background-size: contain !important;
    background-position: center center !important;
    /* خلفية احتياطية للمناطق الفارغة */
    background-color: #1a1a2e !important;
  }

  .game-board {
    transform: scale(1.0);
  }

  .fruit-button {
    width: 50px !important;
    height: 50px !important;
  }

  .amount-button {
    width: 40px !important;
    height: 40px !important;
  }
}

/* تحسينات خاصة للشاشات الصغيرة جداً */
@media screen and (max-width: 480px) {
  .responsive-background {
    background-size: cover !important;
    background-position: center center !important;
    /* تأكد من تغطية كامل الشاشة الصغيرة */
    min-height: 100vh;
    min-width: 100vw;
    height: 100vh;
    width: 100vw;
    /* تمديد إضافي للشاشات الصغيرة جداً */
    background-attachment: scroll;
    background-repeat: no-repeat !important;
  }
}

/* قواعد خاصة لأحجام الجوال الشائعة */

/* iPhone SE وأجهزة مشابهة (375x667) */
@media screen and (max-width: 375px) and (max-height: 667px) {
  .responsive-background {
    background-size: cover !important;
    background-position: center center !important;
    height: 100vh;
    width: 100vw;
    min-height: 667px;
    min-width: 375px;
  }
}

/* iPhone 12/13/14 وأجهزة مشابهة (390x844) */
@media screen and (max-width: 390px) and (max-height: 844px) {
  .responsive-background {
    background-size: cover !important;
    background-position: center center !important;
    height: 100vh;
    width: 100vw;
    min-height: 844px;
    min-width: 390px;
  }
}

/* Samsung Galaxy وأجهزة Android الشائعة */
@media screen and (max-width: 412px) and (max-height: 915px) {
  .responsive-background {
    background-size: cover !important;
    background-position: center center !important;
    height: 100vh;
    width: 100vw;
    min-height: 915px;
    min-width: 412px;
  }
}

/* iPhone 14 Pro Max وأجهزة مشابهة (428x926) */
@media screen and (max-width: 428px) and (min-height: 900px) {
  .responsive-background {
    background-size: cover !important;
    background-position: center center !important;
    height: calc(var(--vh, 1vh) * 100);
    width: 100vw;
    /* تمديد إضافي للأجهزة الطويلة */
    min-height: calc(var(--vh, 1vh) * 100);
    min-width: 100vw;
    /* تأكد من عدم قطع الأجزاء المهمة */
    background-attachment: scroll;
    background-repeat: no-repeat !important;
  }

  .game-board {
    transform: scale(0.7);
    top: 20% !important;
  }

  .fruit-button {
    width: 35px !important;
    height: 35px !important;
  }

  .amount-button {
    width: 28px !important;
    height: 28px !important;
  }

  .balance-display {
    font-size: 12px !important;
  }
}

/* قاعدة محددة لـ iPhone 14 Pro Max (428x926) */
@media screen and (width: 428px) and (height: 926px),
       screen and (width: 428px) and (height: 926px) and (orientation: portrait),
       screen and (max-width: 428px) and (min-height: 920px) {
  .responsive-background {
    /* تغيير الاستراتيجية: استخدام contain بدلاً من cover */
    background-size: contain !important;
    background-position: center center !important;
    height: calc(var(--vh, 1vh) * 100) !important;
    width: 100vw !important;
    min-height: 926px !important;
    min-width: 428px !important;
    /* إضافة خلفية لونية للمناطق الفارغة */
    background-color: #1a1a2e !important;
    background-attachment: scroll !important;
    background-repeat: no-repeat !important;
    background-origin: border-box !important;
    background-clip: border-box !important;
  }

  /* تحسين عناصر اللعبة للشاشة الكبيرة */
  .game-board {
    transform: scale(1.05) !important;
    top: 18% !important;
  }

  .betting-controls {
    bottom: 30px !important;
  }

  .amount-controls {
    bottom: 20px !important;
  }

  .balance-display {
    font-size: 16px !important;
    top: 15px !important;
    left: 15px !important;
  }
}

/* حل بديل لـ iPhone 14 Pro Max - خلفية مركبة */
@media screen and (max-width: 428px) and (min-height: 920px) {
  .responsive-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('/222.jpg');
    background-size: cover;
    background-position: center center;
    background-repeat: no-repeat;
    z-index: -1;
    /* تمديد إضافي للتأكد من التغطية */
    transform: scale(1.1);
  }

  .responsive-background {
    background: #1a1a2e !important;
    overflow: hidden !important;
  }
}

/* تحسينات للشاشات العريضة */
@media screen and (min-width: 1920px) {
  .responsive-background {
    background-size: contain !important;
    background-position: center center !important;
    /* خلفية احتياطية للمناطق الفارغة */
    background-color: #1a1a2e !important;
  }

  .game-board {
    transform: scale(1.2);
  }

  .fruit-button {
    width: 60px !important;
    height: 60px !important;
  }

  .amount-button {
    width: 50px !important;
    height: 50px !important;
  }

  .balance-display {
    font-size: 18px !important;
  }
}

/* قواعد خاصة للشاشات ذات النسب المختلفة */

/* الشاشات العريضة جداً (21:9) */
@media screen and (min-aspect-ratio: 21/9) {
  .responsive-background {
    background-size: contain !important;
    background-position: center center !important;
    background-color: #1a1a2e !important;
  }
}

/* الشاشات الطويلة (9:21) */
@media screen and (max-aspect-ratio: 9/21) {
  .responsive-background {
    background-size: cover !important;
    background-position: center center !important;
  }
}

/* الشاشات المربعة تقريباً (1:1) */
@media screen and (min-aspect-ratio: 3/4) and (max-aspect-ratio: 4/3) {
  .responsive-background {
    background-size: contain !important;
    background-position: center center !important;
    background-color: #1a1a2e !important;
  }
}

/* تحسينات للأداء */
.game-board,
.betting-controls,
.amount-controls {
  will-change: transform;
  backface-visibility: hidden;
  perspective: 1000px;
}

/* تحسينات للمس */
@media (hover: none) and (pointer: coarse) {
  .fruit-button,
  .amount-button {
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }
  
  .fruit-button:active,
  .amount-button:active {
    transform: scale(0.95);
  }
}

/* تحسينات للوضع الليلي */
@media (prefers-color-scheme: dark) {
  .balance-display {
    color: #ffffff !important;
  }
}

/* تحسينات لتوفير البيانات */
@media (prefers-reduced-data: reduce) {
  .game-board {
    background-image: none;
  }
}

/* تحسينات للحركة المخفضة */
@media (prefers-reduced-motion: reduce) {
  .game-board,
  .betting-controls,
  .amount-controls {
    transition: none !important;
    animation: none !important;
  }
}
