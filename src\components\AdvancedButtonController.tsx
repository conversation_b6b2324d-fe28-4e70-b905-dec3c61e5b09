import React, { useState, useEffect, useRef } from 'react';
import { BUTTON_POSITIONS } from '../utils/buttonPositions';
import { ConfigManager, ButtonConfig } from '../utils/configManager';

interface AdvancedButtonControllerProps {
  isVisible: boolean;
  onToggle: () => void;
}

interface ButtonData {
  id: string;
  type: 'symbol' | 'amount' | 'bet' | 'display';
  name: string;
  left: string;
  top: string;
  value?: number;
  symbol?: string;
}

const AdvancedButtonController: React.FC<AdvancedButtonControllerProps> = ({
  isVisible,
  onToggle
}) => {
  const [currentDevice, setCurrentDevice] = useState<any>(null);
  const [buttons, setButtons] = useState<ButtonData[]>([]);
  const [selectedButton, setSelectedButton] = useState<string>('');
  const [isDragging, setIsDragging] = useState(false);
  const [showGrid, setShowGrid] = useState(true);
  const [gridSize, setGridSize] = useState(5);
  const [snapToGrid, setSnapToGrid] = useState(true);
  const [precision, setPrecision] = useState(1); // دقة الحركة
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    updateDevice();
    window.addEventListener('resize', updateDevice);
    return () => window.removeEventListener('resize', updateDevice);
  }, []);

  const updateDevice = () => {
    const width = window.innerWidth;
    const height = window.innerHeight;
    
    let deviceConfig = BUTTON_POSITIONS.find(d => 
      Math.abs(d.device.width - width) < 50 && Math.abs(d.device.height - height) < 50
    );
    
    if (!deviceConfig) {
      if (width <= 768) {
        deviceConfig = BUTTON_POSITIONS.find(d => d.device.width <= 768) || BUTTON_POSITIONS[0];
      } else {
        deviceConfig = BUTTON_POSITIONS.find(d => d.device.width > 768) || BUTTON_POSITIONS[1] || BUTTON_POSITIONS[0];
      }
    }
    
    setCurrentDevice(deviceConfig);
    loadButtons(deviceConfig);
  };

  const loadButtons = (device: any) => {
    if (!device) return;
    
    const buttonList: ButtonData[] = [];
    
    // أزرار الفواكه
    Object.entries(device.symbolButtons).forEach(([key, pos]: [string, any]) => {
      buttonList.push({
        id: `symbol-${key}`,
        type: 'symbol',
        name: pos.name || key.toUpperCase(),
        left: pos.left,
        top: pos.top
      });
    });
    
    // أزرار المبالغ
    Object.entries(device.amountButtons).forEach(([key, pos]: [string, any]) => {
      buttonList.push({
        id: `amount-${key}`,
        type: 'amount',
        name: pos.name || key.toUpperCase(),
        left: pos.left,
        top: pos.top,
        value: pos.value
      });
    });
    
    // مربعات الرهانات
    Object.entries(device.betRectangles).forEach(([key, pos]: [string, any]) => {
      buttonList.push({
        id: `bet-${key}`,
        type: 'bet',
        name: `BET_${key.toUpperCase()}`,
        left: pos.left,
        top: pos.top,
        symbol: pos.symbol
      });
    });
    
    // العروض العلوية
    Object.entries(device.topDisplays).forEach(([key, pos]: [string, any]) => {
      buttonList.push({
        id: `display-${key}`,
        type: 'display',
        name: pos.name || key.toUpperCase(),
        left: pos.left,
        top: pos.top
      });
    });
    
    setButtons(buttonList);
  };

  const handleMouseDown = (buttonId: string, e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setSelectedButton(buttonId);
    setIsDragging(true);
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging || !selectedButton || !containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    let x = ((e.clientX - rect.left) / rect.width) * 100;
    let y = ((e.clientY - rect.top) / rect.height) * 100;

    // تطبيق المحاذاة للشبكة
    if (snapToGrid) {
      x = Math.round(x / gridSize) * gridSize;
      y = Math.round(y / gridSize) * gridSize;
    } else {
      x = Math.round(x * (10 / precision)) / (10 / precision);
      y = Math.round(y * (10 / precision)) / (10 / precision);
    }

    // تحديد الحدود
    x = Math.max(0, Math.min(100, x));
    y = Math.max(0, Math.min(100, y));

    updateButtonPosition(selectedButton, `${x.toFixed(1)}%`, `${y.toFixed(1)}%`);
  };

  const handleMouseUp = () => {
    setIsDragging(false);
    setSelectedButton('');
  };

  const updateButtonPosition = (buttonId: string, left: string, top: string) => {
    setButtons(prev => prev.map(btn => 
      btn.id === buttonId ? { ...btn, left, top } : btn
    ));
  };

  const moveButton = (buttonId: string, direction: 'up' | 'down' | 'left' | 'right') => {
    const button = buttons.find(b => b.id === buttonId);
    if (!button) return;

    const currentLeft = parseFloat(button.left);
    const currentTop = parseFloat(button.top);
    const step = snapToGrid ? gridSize : precision;

    let newLeft = currentLeft;
    let newTop = currentTop;

    switch (direction) {
      case 'up': newTop = Math.max(0, currentTop - step); break;
      case 'down': newTop = Math.min(100, currentTop + step); break;
      case 'left': newLeft = Math.max(0, currentLeft - step); break;
      case 'right': newLeft = Math.min(100, currentLeft + step); break;
    }

    updateButtonPosition(buttonId, `${newLeft.toFixed(1)}%`, `${newTop.toFixed(1)}%`);
  };

  const saveConfiguration = () => {
    if (!currentDevice) return;

    try {
      const symbolButtons = {};
      const amountButtons = {};
      const betRectangles = {};
      const topDisplays = {};

      buttons.forEach(button => {
        const pos = { left: button.left, top: button.top };

        switch (button.type) {
          case 'symbol':
            const symbolKey = button.id.replace('symbol-', '');
            symbolButtons[symbolKey] = { ...pos, name: button.name };
            break;
          case 'amount':
            const amountKey = button.id.replace('amount-', '');
            amountButtons[amountKey] = { ...pos, name: button.name, value: button.value };
            break;
          case 'bet':
            const betKey = button.id.replace('bet-', '');
            betRectangles[betKey] = { ...pos, symbol: button.symbol };
            break;
          case 'display':
            const displayKey = button.id.replace('display-', '');
            topDisplays[displayKey] = { ...pos, name: button.name };
            break;
        }
      });

      const newConfig: ButtonConfig = {
        device: currentDevice.device,
        backgroundImage: currentDevice.backgroundImage,
        symbolButtons,
        amountButtons,
        betRectangles,
        betNumbers: currentDevice.betNumbers,
        topDisplays,
        gameBoard: currentDevice.gameBoard
      };

      // حفظ باستخدام مدير التكوينات
      ConfigManager.saveConfig(currentDevice.device.name, newConfig);

      // إنشاء كود TypeScript
      const configCode = ConfigManager.generateTypeScriptCode(newConfig);
      navigator.clipboard.writeText(configCode);

      // إحصائيات التكوين
      const stats = ConfigManager.getConfigStats(newConfig);

      alert(`✅ تم حفظ التكوين بنجاح!
📋 تم نسخ الكود للحافظة
💾 تم حفظه محلياً مع نسخة احتياطية
📊 الإحصائيات:
   • أزرار الفواكه: ${stats.symbolButtons}
   • أزرار المبالغ: ${stats.amountButtons}
   • مربعات الرهانات: ${stats.betRectangles}
   • عروض المعلومات: ${stats.topDisplays}
   • المجموع: ${stats.total} عنصر`);

    } catch (error) {
      console.error('خطأ في حفظ التكوين:', error);
      alert('❌ حدث خطأ أثناء حفظ التكوين!');
    }
  };

  const loadSavedConfiguration = () => {
    if (!currentDevice) return;

    try {
      const config = ConfigManager.loadConfig(currentDevice.device.name);
      if (config) {
        loadButtons(config);
        const stats = ConfigManager.getConfigStats(config);
        alert(`✅ تم تحميل التكوين المحفوظ!
📊 تم تحميل ${stats.total} عنصر:
   • أزرار الفواكه: ${stats.symbolButtons}
   • أزرار المبالغ: ${stats.amountButtons}
   • مربعات الرهانات: ${stats.betRectangles}
   • عروض المعلومات: ${stats.topDisplays}`);
      } else {
        alert('❌ لا يوجد تكوين محفوظ لهذا الجهاز');
      }
    } catch (error) {
      console.error('خطأ في تحميل التكوين:', error);
      alert('❌ حدث خطأ أثناء تحميل التكوين!');
    }
  };

  const resetToDefault = () => {
    if (confirm('هل تريد إعادة تعيين جميع المواقع للقيم الافتراضية؟')) {
      updateDevice();
      alert('✅ تم إعادة التعيين للقيم الافتراضية!');
    }
  };

  const restoreBackup = () => {
    if (!currentDevice) return;

    try {
      const backup = ConfigManager.restoreBackup(currentDevice.device.name);
      if (backup) {
        if (confirm('هل تريد استرداد النسخة الاحتياطية؟ سيتم فقدان التغييرات الحالية.')) {
          loadButtons(backup);
          alert('✅ تم استرداد النسخة الاحتياطية!');
        }
      } else {
        alert('❌ لا توجد نسخة احتياطية لهذا الجهاز');
      }
    } catch (error) {
      console.error('خطأ في استرداد النسخة الاحتياطية:', error);
      alert('❌ حدث خطأ أثناء استرداد النسخة الاحتياطية!');
    }
  };

  const exportAllConfigs = () => {
    try {
      const allConfigs = ConfigManager.exportAllConfigs();
      navigator.clipboard.writeText(allConfigs);
      const devices = ConfigManager.getSavedDevices();
      alert(`✅ تم تصدير جميع التكوينات!
📋 تم نسخها للحافظة
📱 الأجهزة المصدرة: ${devices.length}
${devices.map(d => `   • ${d}`).join('\n')}`);
    } catch (error) {
      console.error('خطأ في تصدير التكوينات:', error);
      alert('❌ حدث خطأ أثناء تصدير التكوينات!');
    }
  };

  if (!isVisible) {
    return (
      <button
        onClick={onToggle}
        style={{
          position: 'fixed',
          top: '250px',
          right: '10px',
          zIndex: 9999,
          background: 'rgba(255, 0, 100, 0.9)',
          color: '#fff',
          border: '2px solid #ff0066',
          borderRadius: '6px',
          padding: '10px 14px',
          cursor: 'pointer',
          fontSize: '12px',
          fontWeight: 'bold',
          boxShadow: '0 4px 12px rgba(255, 0, 100, 0.4)',
        }}
      >
        🎛️ Advanced Controller
      </button>
    );
  }

  if (!currentDevice) return null;

  return (
    <>
      {/* الشبكة المرجعية */}
      {showGrid && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100vw',
          height: '100vh',
          pointerEvents: 'none',
          zIndex: 9995,
        }}>
          {Array.from({ length: Math.floor(100 / gridSize) + 1 }, (_, i) => i * gridSize).map(percent => (
            <React.Fragment key={percent}>
              <div style={{
                position: 'absolute',
                left: `${percent}%`,
                top: 0,
                width: '1px',
                height: '100%',
                background: percent % (gridSize * 2) === 0 ? 'rgba(255, 255, 255, 0.4)' : 'rgba(255, 255, 255, 0.2)',
              }} />
              <div style={{
                position: 'absolute',
                top: `${percent}%`,
                left: 0,
                width: '100%',
                height: '1px',
                background: percent % (gridSize * 2) === 0 ? 'rgba(255, 255, 255, 0.4)' : 'rgba(255, 255, 255, 0.2)',
              }} />
            </React.Fragment>
          ))}
        </div>
      )}

      {/* منطقة السحب */}
      <div
        ref={containerRef}
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100vw',
          height: '100vh',
          zIndex: 9996,
          cursor: isDragging ? 'grabbing' : 'default',
        }}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
      >
        {/* عرض الأزرار */}
        {buttons.map(button => {
          const isSelected = selectedButton === button.id;
          const colors = {
            symbol: { bg: 'rgba(0, 255, 136, 0.9)', border: '#00ff88' },
            amount: { bg: 'rgba(255, 215, 0, 0.9)', border: '#FFD700' },
            bet: { bg: 'rgba(255, 100, 100, 0.9)', border: '#ff6464' },
            display: { bg: 'rgba(100, 150, 255, 0.9)', border: '#6496ff' }
          };
          
          const color = colors[button.type];
          const size = button.type === 'amount' ? 40 : button.type === 'symbol' ? 60 : 'auto';
          
          return (
            <div
              key={button.id}
              style={{
                position: 'absolute',
                left: button.left,
                top: button.top,
                width: typeof size === 'number' ? `${size}px` : size,
                height: typeof size === 'number' ? `${size}px` : 'auto',
                minWidth: typeof size !== 'number' ? '60px' : undefined,
                padding: typeof size !== 'number' ? '8px 12px' : '0',
                background: color.bg,
                border: `3px solid ${isSelected ? '#fff' : color.border}`,
                borderRadius: button.type === 'amount' ? '50%' : '8px',
                transform: 'translate(-50%, -50%)',
                zIndex: isSelected ? 9999 : 9997,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: button.type === 'amount' ? '10px' : '12px',
                fontWeight: 'bold',
                color: '#000',
                cursor: 'grab',
                userSelect: 'none',
                transition: isSelected ? 'none' : 'all 0.2s ease',
                boxShadow: isSelected ? '0 0 20px rgba(255, 255, 255, 0.8)' : '0 4px 12px rgba(0, 0, 0, 0.3)',
              }}
              onMouseDown={(e) => handleMouseDown(button.id, e)}
              title={`${button.name} - ${button.left}, ${button.top}`}
            >
              {button.type === 'amount' && button.value ? 
                (button.value >= 1000 ? `${button.value/1000}k` : button.value) :
                button.name.substring(0, 3)
              }
            </div>
          );
        })}
      </div>

      {/* لوحة التحكم المتقدمة */}
      <div style={{
        position: 'fixed',
        top: '20px',
        right: '20px',
        width: '350px',
        maxHeight: '90vh',
        background: 'rgba(0, 0, 0, 0.95)',
        border: '2px solid #ff0066',
        borderRadius: '12px',
        padding: '20px',
        zIndex: 10000,
        color: '#fff',
        fontSize: '12px',
        overflow: 'auto',
        boxShadow: '0 8px 32px rgba(0, 0, 0, 0.8)',
      }}>
        {/* العنوان */}
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
          <h2 style={{ margin: 0, fontSize: '16px', color: '#ff0066' }}>🎛️ Advanced Controller</h2>
          <button onClick={onToggle} style={{ background: 'none', border: 'none', color: '#ff4444', cursor: 'pointer', fontSize: '20px' }}>✕</button>
        </div>

        {/* معلومات الجهاز */}
        <div style={{ marginBottom: '20px', padding: '12px', background: 'rgba(255, 255, 255, 0.1)', borderRadius: '8px' }}>
          <strong>📱 الجهاز:</strong> {currentDevice.device.name}<br/>
          <strong>📏 الأبعاد:</strong> {window.innerWidth}×{window.innerHeight}<br/>
          <strong>🎯 الأزرار:</strong> {buttons.length} زر
        </div>

        {/* إعدادات الشبكة والدقة */}
        <div style={{ marginBottom: '20px' }}>
          <h3 style={{ margin: '0 0 12px 0', fontSize: '14px', color: '#00ff88' }}>⚙️ إعدادات التحكم</h3>

          <div style={{ marginBottom: '12px' }}>
            <label style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '8px' }}>
              <input
                type="checkbox"
                checked={showGrid}
                onChange={(e) => setShowGrid(e.target.checked)}
              />
              إظهار الشبكة المرجعية
            </label>

            <label style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <input
                type="checkbox"
                checked={snapToGrid}
                onChange={(e) => setSnapToGrid(e.target.checked)}
              />
              المحاذاة للشبكة
            </label>
          </div>

          <div style={{ display: 'flex', gap: '12px', marginBottom: '12px' }}>
            <div style={{ flex: 1 }}>
              <label style={{ display: 'block', marginBottom: '4px' }}>حجم الشبكة:</label>
              <select
                value={gridSize}
                onChange={(e) => setGridSize(Number(e.target.value))}
                style={{ width: '100%', padding: '4px', background: '#333', color: '#fff', border: '1px solid #666', borderRadius: '4px' }}
              >
                <option value={1}>1%</option>
                <option value={2.5}>2.5%</option>
                <option value={5}>5%</option>
                <option value={10}>10%</option>
              </select>
            </div>

            <div style={{ flex: 1 }}>
              <label style={{ display: 'block', marginBottom: '4px' }}>دقة الحركة:</label>
              <select
                value={precision}
                onChange={(e) => setPrecision(Number(e.target.value))}
                style={{ width: '100%', padding: '4px', background: '#333', color: '#fff', border: '1px solid #666', borderRadius: '4px' }}
              >
                <option value={0.1}>0.1%</option>
                <option value={0.5}>0.5%</option>
                <option value={1}>1%</option>
              </select>
            </div>
          </div>
        </div>

        {/* الزر المحدد */}
        {selectedButton && (
          <div style={{ marginBottom: '20px', padding: '12px', background: 'rgba(255, 0, 102, 0.2)', borderRadius: '8px', border: '1px solid #ff0066' }}>
            <h3 style={{ margin: '0 0 12px 0', fontSize: '14px', color: '#ff0066' }}>🎯 الزر المحدد</h3>
            {(() => {
              const button = buttons.find(b => b.id === selectedButton);
              if (!button) return null;

              return (
                <>
                  <div style={{ marginBottom: '12px' }}>
                    <strong>الاسم:</strong> {button.name}<br/>
                    <strong>النوع:</strong> {button.type}<br/>
                    <strong>الموقع:</strong> {button.left}, {button.top}
                  </div>

                  {/* أزرار التحكم الدقيق */}
                  <div style={{ display: 'grid', gridTemplateColumns: '1fr auto 1fr', gap: '4px', alignItems: 'center' }}>
                    <div></div>
                    <button
                      onClick={() => moveButton(selectedButton, 'up')}
                      style={{ padding: '4px 8px', background: '#333', color: '#fff', border: '1px solid #666', borderRadius: '4px', cursor: 'pointer' }}
                    >↑</button>
                    <div></div>

                    <button
                      onClick={() => moveButton(selectedButton, 'left')}
                      style={{ padding: '4px 8px', background: '#333', color: '#fff', border: '1px solid #666', borderRadius: '4px', cursor: 'pointer' }}
                    >←</button>
                    <div style={{ textAlign: 'center', fontSize: '10px', color: '#ccc' }}>حرك</div>
                    <button
                      onClick={() => moveButton(selectedButton, 'right')}
                      style={{ padding: '4px 8px', background: '#333', color: '#fff', border: '1px solid #666', borderRadius: '4px', cursor: 'pointer' }}
                    >→</button>

                    <div></div>
                    <button
                      onClick={() => moveButton(selectedButton, 'down')}
                      style={{ padding: '4px 8px', background: '#333', color: '#fff', border: '1px solid #666', borderRadius: '4px', cursor: 'pointer' }}
                    >↓</button>
                    <div></div>
                  </div>
                </>
              );
            })()}
          </div>
        )}

        {/* قائمة الأزرار */}
        <div style={{ marginBottom: '20px' }}>
          <h3 style={{ margin: '0 0 12px 0', fontSize: '14px', color: '#FFD700' }}>📋 قائمة الأزرار</h3>
          <div style={{ maxHeight: '200px', overflow: 'auto', border: '1px solid #333', borderRadius: '4px' }}>
            {buttons.map(button => (
              <div
                key={button.id}
                style={{
                  padding: '8px',
                  borderBottom: '1px solid #333',
                  background: selectedButton === button.id ? 'rgba(255, 0, 102, 0.3)' : 'transparent',
                  cursor: 'pointer',
                  fontSize: '10px',
                }}
                onClick={() => setSelectedButton(button.id)}
              >
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <span style={{
                    color: button.type === 'symbol' ? '#00ff88' :
                           button.type === 'amount' ? '#FFD700' :
                           button.type === 'bet' ? '#ff6464' : '#6496ff'
                  }}>
                    {button.name}
                  </span>
                  <span style={{ color: '#ccc', fontSize: '9px' }}>
                    {button.left}, {button.top}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* أزرار الحفظ والتحميل */}
        <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
          <button
            onClick={saveConfiguration}
            style={{
              padding: '12px',
              background: 'linear-gradient(135deg, #4caf50, #45a049)',
              color: '#fff',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer',
              fontWeight: 'bold',
              fontSize: '12px',
            }}
          >
            💾 حفظ التكوين
          </button>

          <div style={{ display: 'flex', gap: '8px', marginBottom: '8px' }}>
            <button
              onClick={loadSavedConfiguration}
              style={{
                flex: 1,
                padding: '8px',
                background: '#2196f3',
                color: '#fff',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '11px',
              }}
            >
              📂 تحميل محفوظ
            </button>

            <button
              onClick={restoreBackup}
              style={{
                flex: 1,
                padding: '8px',
                background: '#9c27b0',
                color: '#fff',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '11px',
              }}
            >
              🔙 استرداد نسخة
            </button>
          </div>

          <div style={{ display: 'flex', gap: '8px' }}>
            <button
              onClick={resetToDefault}
              style={{
                flex: 1,
                padding: '8px',
                background: '#ff9800',
                color: '#fff',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '11px',
              }}
            >
              🔄 إعادة تعيين
            </button>

            <button
              onClick={exportAllConfigs}
              style={{
                flex: 1,
                padding: '8px',
                background: '#607d8b',
                color: '#fff',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '11px',
              }}
            >
              📤 تصدير الكل
            </button>
          </div>
        </div>

        {/* مفاتيح الألوان */}
        <div style={{ marginTop: '16px', padding: '12px', background: 'rgba(255, 255, 255, 0.05)', borderRadius: '6px' }}>
          <div style={{ fontSize: '11px', lineHeight: '1.6' }}>
            <div><span style={{color: '#00ff88'}}>🟢</span> أزرار الفواكه</div>
            <div><span style={{color: '#FFD700'}}>🟡</span> أزرار المبالغ</div>
            <div><span style={{color: '#ff6464'}}>🔴</span> مربعات الرهانات</div>
            <div><span style={{color: '#6496ff'}}>🔵</span> عروض المعلومات</div>
          </div>
        </div>
      </div>
    </>
  );
};

export default AdvancedButtonController;
