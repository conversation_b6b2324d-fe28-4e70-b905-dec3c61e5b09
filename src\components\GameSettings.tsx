import React, { useState, useEffect } from 'react';
import { gameEconomics } from '../utils/economics';
import { playerTracking, Player as TrackingPlayer } from '../utils/playerTracking';
import { PAYOUT_MULTIPLIERS, SYMBOL_MAX_BETS, BET_AMOUNTS } from '../constants/gameConfig';

interface Player {
  id: string;
  name: string;
  totalBets: number;
  totalWinnings: number;
  netProfit: number;
  lastActive: string;
  riskLevel: 'low' | 'medium' | 'high';
  isTracked: boolean;
}

interface GameSettings {
  // نسب الربح والخسارة
  profitMargin: number;
  lossLimit: number;
  serviceFeeRate: number;
  
  // إعدادات اللعبة
  houseEdge: number;
  maxDailyLoss: number;
  maxSingleRoundLoss: number;
  
  // إعدادات اللاعبين
  trackHighRollers: boolean;
  highRollerThreshold: number;
  trackSuspiciousPlayers: boolean;
  suspiciousActivityThreshold: number;
  
  // إعدادات التقارير
  autoGenerateReports: boolean;
  reportFrequency: 'hourly' | 'daily' | 'weekly';
  emailNotifications: boolean;
}

const GameSettings: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [activeTab, setActiveTab] = useState<'overview' | 'economics' | 'players' | 'reports' | 'advanced'>('overview');
  const [settings, setSettings] = useState<GameSettings>({
    profitMargin: 15,
    lossLimit: 500000,
    serviceFeeRate: 2,
    houseEdge: 5,
    maxDailyLoss: 500000,
    maxSingleRoundLoss: 100000,
    trackHighRollers: true,
    highRollerThreshold: 50000,
    trackSuspiciousPlayers: true,
    suspiciousActivityThreshold: 100000,
    autoGenerateReports: true,
    reportFrequency: 'daily',
    emailNotifications: false
  });

  const [players, setPlayers] = useState<TrackingPlayer[]>([]);

  const [economicReport, setEconomicReport] = useState<any>(null);

  useEffect(() => {
    const updateReport = () => {
      const report = gameEconomics.getEconomicReport();
      setEconomicReport(report);
      
      // تحديث بيانات اللاعبين
      const playersReport = playerTracking.getPlayersReport();
      setPlayers(playersReport.players);
    };

    updateReport();
    const interval = setInterval(updateReport, 10000);
    return () => clearInterval(interval);
  }, []);

  const handleSettingChange = (key: keyof GameSettings, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  const togglePlayerTracking = (playerId: string) => {
    playerTracking.togglePlayerTracking(playerId);
    const playersReport = playerTracking.getPlayersReport();
    setPlayers(playersReport.players);
  };

  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'high': return 'text-red-600 bg-red-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const calculateProjectedProfit = () => {
    if (!economicReport) return 0;
    const currentProfitMargin = economicReport.profitMargin;
    const targetMargin = settings.profitMargin;
    const totalBets = economicReport.totalBets;
    
    return (targetMargin - currentProfitMargin) * totalBets / 100;
  };

  return (
    <div className="fixed bottom-4 left-4 z-50">
      {/* زر إظهار/إخفاء */}
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="bg-purple-600 text-white px-4 py-2 rounded-lg shadow-lg hover:bg-purple-700 transition-colors"
      >
        {isVisible ? '⚙️ إخفاء الإعدادات' : '⚙️ إعدادات اللعبة'}
      </button>

      {/* لوحة الإعدادات */}
      {isVisible && (
        <div className="mt-2 bg-white border border-gray-300 rounded-lg shadow-xl p-4 max-w-4xl max-h-96 overflow-y-auto">
          <h3 className="font-bold text-lg mb-4 text-gray-800">⚙️ إعدادات اللعبة المتقدمة</h3>
          
          {/* شريط التنقل */}
          <div className="flex space-x-2 mb-4 border-b">
            {[
              { id: 'overview', label: '📊 نظرة عامة', icon: '📊' },
              { id: 'economics', label: '💰 الاقتصاد', icon: '💰' },
              { id: 'players', label: '👥 اللاعبون', icon: '👥' },
              { id: 'reports', label: '📈 التقارير', icon: '📈' },
              { id: 'advanced', label: '🔧 متقدم', icon: '🔧' }
            ].map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`px-3 py-2 text-sm rounded-t-lg transition-colors ${
                  activeTab === tab.id 
                    ? 'bg-blue-600 text-white' 
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                {tab.icon} {tab.label}
              </button>
            ))}
          </div>

          {/* محتوى التبويبات */}
          <div className="min-h-64">
            {/* تبويب النظرة العامة */}
            {activeTab === 'overview' && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-blue-50 p-3 rounded-lg">
                    <h4 className="font-semibold text-blue-800">💰 الربحية الحالية</h4>
                    <div className="text-2xl font-bold text-blue-600">
                      {economicReport?.profitMargin?.toFixed(2) || '0'}%
                    </div>
                    <div className="text-sm text-blue-600">
                      الهدف: {settings.profitMargin}%
                    </div>
                  </div>
                  
                  <div className="bg-green-50 p-3 rounded-lg">
                    <h4 className="font-semibold text-green-800">📈 إجمالي الرهانات</h4>
                    <div className="text-2xl font-bold text-green-600">
                      {economicReport?.totalBets?.toLocaleString() || '0'}$
                    </div>
                    <div className="text-sm text-green-600">
                      اليوم: {economicReport?.todayStats?.bets?.toLocaleString() || '0'}$
                    </div>
                  </div>
                  
                  <div className="bg-purple-50 p-3 rounded-lg">
                    <h4 className="font-semibold text-purple-800">👥 اللاعبون النشطون</h4>
                    <div className="text-2xl font-bold text-purple-600">
                      {players.filter(p => p.isTracked).length}
                    </div>
                    <div className="text-sm text-purple-600">
                      تحت المراقبة
                    </div>
                  </div>
                </div>

                <div className="bg-yellow-50 p-3 rounded-lg">
                  <h4 className="font-semibold text-yellow-800 mb-2">🎯 التوقعات</h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-yellow-700">الربح المتوقع:</span>
                      <span className="font-bold text-yellow-800 ml-2">
                        {calculateProjectedProfit().toLocaleString()}$
                      </span>
                    </div>
                    <div>
                      <span className="text-yellow-700">اللاعبون عاليو المخاطر:</span>
                      <span className="font-bold text-yellow-800 ml-2">
                        {players.filter(p => p.riskLevel === 'high').length}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* تبويب الاقتصاد */}
            {activeTab === 'economics' && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-semibold mb-3">💰 نسب الربح والخسارة</h4>
                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          هامش الربح المستهدف (%)
                        </label>
                        <input
                          type="number"
                          value={settings.profitMargin}
                          onChange={(e) => handleSettingChange('profitMargin', Number(e.target.value))}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          min="0"
                          max="50"
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          حد الخسارة اليومية ($)
                        </label>
                        <input
                          type="number"
                          value={settings.maxDailyLoss}
                          onChange={(e) => handleSettingChange('maxDailyLoss', Number(e.target.value))}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          min="10000"
                          step="10000"
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          رسوم الخدمة (%)
                        </label>
                        <input
                          type="number"
                          value={settings.serviceFeeRate}
                          onChange={(e) => handleSettingChange('serviceFeeRate', Number(e.target.value))}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          min="0"
                          max="10"
                          step="0.1"
                        />
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="font-semibold mb-3">🎰 مضاعفات الرموز</h4>
                    <div className="space-y-2 text-sm">
                      {Object.entries(PAYOUT_MULTIPLIERS).map(([symbol, multiplier]) => (
                        <div key={symbol} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                          <span className="font-medium">{symbol}</span>
                          <span className="text-blue-600 font-bold">x{multiplier}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* تبويب اللاعبون */}
            {activeTab === 'players' && (
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h4 className="font-semibold">👥 قائمة اللاعبين</h4>
                  <div className="space-x-2">
                    <label className="flex items-center text-sm">
                      <input
                        type="checkbox"
                        checked={settings.trackHighRollers}
                        onChange={(e) => handleSettingChange('trackHighRollers', e.target.checked)}
                        className="mr-2"
                      />
                      تتبع اللاعبين عاليي الرهان
                    </label>
                    <label className="flex items-center text-sm">
                      <input
                        type="checkbox"
                        checked={settings.trackSuspiciousPlayers}
                        onChange={(e) => handleSettingChange('trackSuspiciousPlayers', e.target.checked)}
                        className="mr-2"
                      />
                      تتبع النشاط المشبوه
                    </label>
                  </div>
                </div>

                <div className="overflow-x-auto">
                  <table className="min-w-full text-sm">
                    <thead>
                      <tr className="bg-gray-50">
                        <th className="px-3 py-2 text-right">اللاعب</th>
                        <th className="px-3 py-2 text-right">إجمالي الرهانات</th>
                        <th className="px-3 py-2 text-right">صافي الربح</th>
                        <th className="px-3 py-2 text-right">مستوى المخاطر</th>
                        <th className="px-3 py-2 text-right">آخر نشاط</th>
                        <th className="px-3 py-2 text-right">المراقبة</th>
                      </tr>
                    </thead>
                    <tbody>
                      {players.map(player => (
                        <tr key={player.id} className="border-b hover:bg-gray-50">
                          <td className="px-3 py-2 font-medium">{player.name}</td>
                          <td className="px-3 py-2 text-blue-600">
                            {player.totalBets.toLocaleString()}$
                          </td>
                          <td className={`px-3 py-2 font-bold ${
                            player.netProfit >= 0 ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {player.netProfit >= 0 ? '+' : ''}{player.netProfit.toLocaleString()}$
                          </td>
                          <td className="px-3 py-2">
                            <span className={`px-2 py-1 rounded-full text-xs ${getRiskLevelColor(player.riskLevel)}`}>
                              {player.riskLevel === 'high' ? 'عالي' : 
                               player.riskLevel === 'medium' ? 'متوسط' : 'منخفض'}
                            </span>
                          </td>
                          <td className="px-3 py-2 text-gray-600">{player.lastActive}</td>
                          <td className="px-3 py-2">
                            <button
                              onClick={() => togglePlayerTracking(player.id)}
                              className={`px-2 py-1 rounded text-xs ${
                                player.isTracked 
                                  ? 'bg-red-600 text-white hover:bg-red-700' 
                                  : 'bg-green-600 text-white hover:bg-green-700'
                              }`}
                            >
                              {player.isTracked ? 'إيقاف' : 'تشغيل'}
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {/* تبويب التقارير */}
            {activeTab === 'reports' && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-semibold mb-3">📈 إعدادات التقارير</h4>
                    <div className="space-y-3">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={settings.autoGenerateReports}
                          onChange={(e) => handleSettingChange('autoGenerateReports', e.target.checked)}
                          className="mr-2"
                        />
                        إنشاء تقارير تلقائية
                      </label>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          تكرار التقارير
                        </label>
                        <select
                          value={settings.reportFrequency}
                          onChange={(e) => handleSettingChange('reportFrequency', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                          <option value="hourly">كل ساعة</option>
                          <option value="daily">يومياً</option>
                          <option value="weekly">أسبوعياً</option>
                        </select>
                      </div>
                      
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={settings.emailNotifications}
                          onChange={(e) => handleSettingChange('emailNotifications', e.target.checked)}
                          className="mr-2"
                        />
                        إشعارات البريد الإلكتروني
                      </label>
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="font-semibold mb-3">📊 تقرير اقتصادي سريع</h4>
                    {economicReport && (
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span>إجمالي الرهانات:</span>
                          <span className="font-bold">{economicReport.totalBets.toLocaleString()}$</span>
                        </div>
                        <div className="flex justify-between">
                          <span>إجمالي الأرباح:</span>
                          <span className="font-bold">{economicReport.totalWinnings.toLocaleString()}$</span>
                        </div>
                        <div className="flex justify-between border-t pt-1">
                          <span>صافي الربح:</span>
                          <span className={`font-bold ${
                            economicReport.netProfit >= 0 ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {economicReport.netProfit >= 0 ? '+' : ''}{economicReport.netProfit.toLocaleString()}$
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span>هامش الربح:</span>
                          <span className={`font-bold ${
                            economicReport.profitMargin >= 0 ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {economicReport.profitMargin.toFixed(2)}%
                          </span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="bg-blue-50 p-3 rounded-lg">
                  <h4 className="font-semibold text-blue-800 mb-2">📋 إجراءات سريعة</h4>
                  <div className="flex space-x-2">
                    <button className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700">
                      📊 إنشاء تقرير الآن
                    </button>
                    <button className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700">
                      📧 إرسال التقرير
                    </button>
                    <button className="px-3 py-1 bg-purple-600 text-white rounded text-sm hover:bg-purple-700">
                      💾 حفظ البيانات
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* تبويب متقدم */}
            {activeTab === 'advanced' && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-semibold mb-3">🔧 إعدادات متقدمة</h4>
                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          حد الخسارة للجولة الواحدة ($)
                        </label>
                        <input
                          type="number"
                          value={settings.maxSingleRoundLoss}
                          onChange={(e) => handleSettingChange('maxSingleRoundLoss', Number(e.target.value))}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          min="1000"
                          step="1000"
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          عتبة اللاعبين عاليي الرهان ($)
                        </label>
                        <input
                          type="number"
                          value={settings.highRollerThreshold}
                          onChange={(e) => handleSettingChange('highRollerThreshold', Number(e.target.value))}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          min="1000"
                          step="1000"
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          عتبة النشاط المشبوه ($)
                        </label>
                        <input
                          type="number"
                          value={settings.suspiciousActivityThreshold}
                          onChange={(e) => handleSettingChange('suspiciousActivityThreshold', Number(e.target.value))}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          min="1000"
                          step="1000"
                        />
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="font-semibold mb-3">🛡️ الحماية والأمان</h4>
                    <div className="space-y-3">
                      <div className="bg-green-50 p-3 rounded-lg">
                        <h5 className="font-medium text-green-800">✅ النظام آمن</h5>
                        <p className="text-sm text-green-700">جميع الإعدادات محمية ومشفرة</p>
                      </div>
                      
                      <div className="bg-blue-50 p-3 rounded-lg">
                        <h5 className="font-medium text-blue-800">📊 النسخ الاحتياطي</h5>
                        <p className="text-sm text-blue-700">آخر نسخة: اليوم 14:30</p>
                      </div>
                      
                      <div className="bg-yellow-50 p-3 rounded-lg">
                        <h5 className="font-medium text-yellow-800">⚠️ التنبيهات النشطة</h5>
                        <p className="text-sm text-yellow-700">3 تنبيهات نشطة</p>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="bg-red-50 p-3 rounded-lg">
                  <h4 className="font-semibold text-red-800 mb-2">🚨 إجراءات الطوارئ</h4>
                  <div className="flex space-x-2">
                    <button className="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700">
                      🛑 إيقاف اللعبة
                    </button>
                    <button className="px-3 py-1 bg-orange-600 text-white rounded text-sm hover:bg-orange-700">
                      ⚠️ وضع الطوارئ
                    </button>
                    <button className="px-3 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-700">
                      🔄 إعادة تعيين
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* أزرار التحكم */}
          <div className="flex justify-between items-center mt-4 pt-4 border-t">
            <div className="text-sm text-gray-600">
              آخر تحديث: {new Date().toLocaleTimeString('ar-SA')}
            </div>
            <div className="flex space-x-2">
              <button className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700">
                إلغاء
              </button>
              <button className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                حفظ الإعدادات
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default GameSettings; 