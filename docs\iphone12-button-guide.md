# دليل ضبط أزرار iPhone 12

## 📱 أداة ضبط أزرار iPhone 12 التفاعلية

تم إنشاء أداة خاصة لضبط مواقع الأزرار على شاشة iPhone 12 بطريقة بصرية وتفاعلية.

## 🚀 كيفية الاستخدام

### 1. تشغيل الأداة
- تأكد من أن المشروع يعمل في وضع التطوير (`npm run dev`)
- ابحث عن زر "📱 iPhone 12 Adjuster" في الجانب الأيمن من الشاشة
- اضغط على الزر لفتح أداة الضبط

### 2. واجهة الأداة
ستظهر لك شاشة تحتوي على:
- **محاكي iPhone 12**: يعرض الشاشة بأبعاد 390×844 بكسل
- **الأزرار القابلة للسحب**: جميع أزرار اللعبة
- **لوحة التحكم**: معلومات وأدوات إضافية

### 3. أنواع الأزرار

#### 🔵 أزرار الفواكه (Symbol Buttons)
- **BAR**: زر البار الذهبي
- **WATERMELON**: زر البطيخ
- **LEMON**: زر الليمون  
- **BANANA**: زر الموز
- **APPLE**: زر التفاح

#### 🟢 أزرار المبالغ (Amount Buttons)
- **5k**: 5000 عملة
- **3k**: 3000 عملة
- **1k**: 1000 عملة
- **500**: 500 عملة
- **100**: 100 عملة

#### 🟣 عروض المعلومات (Display Elements)
- **BALANCE**: عرض الرصيد الحالي
- **TOTAL BET**: عرض إجمالي الرهان

### 4. كيفية الضبط

#### السحب والإفلات:
1. اضغط على أي زر واسحبه إلى الموقع المطلوب
2. سيتغير لون الزر المحدد إلى اللون الأحمر أثناء السحب
3. ستظهر الإحداثيات الحالية في لوحة التحكم

#### مراقبة المواقع:
- **الموقع الحالي**: يظهر في لوحة التحكم
- **التحديث المباشر**: المواقع تتحدث فورياً أثناء السحب
- **النسب المئوية**: جميع المواقع محفوظة كنسب مئوية

### 5. حفظ التكوين الجديد

#### خطوات الحفظ:
1. بعد الانتهاء من ضبط جميع الأزرار
2. اضغط على زر "📋 نسخ التكوين الجديد"
3. سيتم نسخ الكود الجديد إلى الحافظة تلقائياً
4. افتح ملف `src/utils/buttonPositions.ts`
5. ابحث عن تكوين iPhone 12/13/14
6. استبدل التكوين القديم بالجديد
7. احفظ الملف

#### مثال على التكوين المُصدَّر:
```typescript
// تكوين iPhone 12/13/14 المحدث
{
  device: {
    name: 'iPhone 12/13/14',
    width: 390,
    height: 844,
  },
  backgroundImage: '/images/bg-390x844.webp',
  symbolButtons: {
    "bar": {
      "left": "10.0%",
      "top": "74.0%",
      "name": "BAR"
    },
    "watermelon": {
      "left": "27.0%",
      "top": "75.0%",
      "name": "WATERMELON"
    },
    // ... باقي الأزرار
  },
  // ... باقي التكوينات
}
```

## 💡 نصائح مهمة

### للحصول على أفضل النتائج:
1. **ابدأ بالأزرار الرئيسية**: أزرار الفواكه أولاً
2. **حافظ على التوازن**: وزع الأزرار بشكل متوازن
3. **تجنب التداخل**: تأكد من عدم تداخل الأزرار
4. **اختبر على الجهاز الفعلي**: جرب التكوين على iPhone 12 حقيقي

### مناطق آمنة:
- **الجزء العلوي**: 10%-15% للعروض
- **الوسط**: 25%-60% للوحة اللعبة
- **الجزء السفلي**: 65%-85% للأزرار

### تجنب هذه المناطق:
- **الحواف**: أقل من 5% من أي جانب
- **منطقة الشق**: الجزء العلوي الأوسط
- **منطقة المؤشر**: الجزء السفلي الأوسط

## 🔧 استكشاف الأخطاء

### المشكلة: الأزرار لا تتحرك
**الحل**: تأكد من الضغط والسحب بشكل صحيح

### المشكلة: المواقع غير دقيقة
**الحل**: استخدم الشبكة المرجعية في الخلفية

### المشكلة: التكوين لا يعمل
**الحل**: تأكد من نسخ الكود كاملاً ولصقه في المكان الصحيح

## 📊 المواقع الافتراضية لـ iPhone 12

### أزرار الفواكه:
- BAR: 10%, 74%
- WATERMELON: 27%, 75%
- LEMON: 50%, 75%
- BANANA: 73%, 75%
- APPLE: 90%, 74%

### أزرار المبالغ:
- 5000: 12%, 83%
- 3000: 30%, 83%
- 1000: 50%, 83%
- 500: 70%, 83%
- 100: 88%, 83%

### العروض:
- الرصيد: 22%, 10%
- إجمالي الرهان: 78%, 10%

## 🎯 الخطوات التالية

بعد ضبط المواقع:
1. اختبر اللعبة على iPhone 12
2. تأكد من سهولة الوصول لجميع الأزرار
3. اضبط المواقع حسب الحاجة
4. احفظ نسخة احتياطية من التكوين

---

**ملاحظة**: هذه الأداة تعمل فقط في وضع التطوير وتختفي في الإنتاج تلقائياً.
