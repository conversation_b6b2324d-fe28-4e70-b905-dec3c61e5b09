<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎮 مدير شبكة اللعبة - Lucky Ocean Game</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 350px 1fr;
            gap: 20px;
            height: calc(100vh - 40px);
        }

        .sidebar {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            overflow-y: auto;
        }

        .main-area {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 20px;
            position: relative;
            overflow: hidden;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #00ff88;
            margin-bottom: 10px;
            font-size: 2em;
        }

        .section {
            margin-bottom: 25px;
            padding: 15px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            border-left: 4px solid #00ff88;
        }

        .section h3 {
            color: #00ffff;
            margin-bottom: 15px;
            font-size: 1.2em;
        }

        .device-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }

        .device-btn {
            padding: 12px;
            background: linear-gradient(45deg, #333, #555);
            color: white;
            border: 2px solid #666;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
            text-align: center;
        }

        .device-btn:hover {
            border-color: #00ff88;
            transform: translateY(-2px);
        }

        .device-btn.active {
            background: linear-gradient(45deg, #00ff88, #00ffff);
            color: #000;
            border-color: #00ffff;
        }

        .control-group {
            margin-bottom: 15px;
        }

        .control-group label {
            display: block;
            margin-bottom: 8px;
            color: #00ff88;
            font-weight: bold;
        }

        .control-group input, .control-group select {
            width: 100%;
            padding: 10px;
            border: 2px solid #00ff88;
            border-radius: 6px;
            background: rgba(0, 0, 0, 0.5);
            color: white;
            font-size: 14px;
        }

        .btn {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(45deg, #00ff88, #00ffff);
            color: #000;
        }

        .btn-secondary {
            background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(45deg, #ffaa00, #ffcc00);
            color: #000;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .preview-area {
            position: relative;
            width: 100%;
            height: 100%;
            background: #000;
            border-radius: 10px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .device-frame {
            position: relative;
            background: #222;
            border-radius: 20px;
            padding: 20px;
            box-shadow: 0 0 30px rgba(0, 0, 0, 0.8);
        }

        .device-screen {
            position: relative;
            background-image: url('http://localhost:5173/222.jpg');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            border-radius: 10px;
            overflow: hidden;
        }

        .game-grid {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            grid-template-rows: repeat(5, 1fr);
            gap: 2px;
            width: 250px;
            height: 250px;
            background: rgba(0, 0, 0, 0.8);
            border: 2px solid #00ff88;
            border-radius: 10px;
            padding: 10px;
        }

        .game-square {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid #00ff88;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: bold;
            color: #00ff88;
            cursor: move;
            transition: all 0.3s ease;
            position: relative;
        }

        .game-square:hover {
            background: rgba(0, 255, 136, 0.3);
            transform: scale(1.05);
        }

        .game-square.dragging {
            background: rgba(255, 0, 102, 0.8);
            border-color: #ff0066;
            transform: scale(1.1);
            z-index: 100;
        }

        .game-square.normal {
            background: rgba(0, 255, 136, 0.2);
        }

        .game-square.half-fruit {
            background: rgba(255, 165, 0, 0.2);
        }

        .game-square.stacked-bar {
            background: rgba(255, 215, 0, 0.2);
        }

        .game-square.inner {
            background: rgba(128, 0, 128, 0.2);
        }

        .grid-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                linear-gradient(rgba(0, 255, 136, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 255, 136, 0.1) 1px, transparent 1px);
            background-size: 5% 5%;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .grid-overlay.visible {
            opacity: 1;
        }

        .status-bar {
            position: fixed;
            bottom: 20px;
            left: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.9);
            padding: 10px 20px;
            border-radius: 10px;
            border: 2px solid #00ff88;
            color: #00ff88;
            font-family: monospace;
            font-size: 12px;
            z-index: 1000;
        }

        .code-output {
            background: #000;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            color: #00ff88;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #00ff88;
            margin-top: 10px;
        }

        .device-info {
            background: rgba(0, 0, 0, 0.5);
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            font-size: 12px;
        }

        .square-legend {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 5px;
            margin-bottom: 15px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 11px;
        }

        .legend-color {
            width: 15px;
            height: 15px;
            border-radius: 3px;
            border: 1px solid #666;
        }

        @media (max-width: 1200px) {
            .container {
                grid-template-columns: 1fr;
                grid-template-rows: auto 1fr;
            }
            
            .sidebar {
                max-height: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- الشريط الجانبي -->
        <div class="sidebar">
            <div class="header">
                <h1>🎮 مدير الشبكة</h1>
                <p>Lucky Ocean Game Grid</p>
            </div>

            <!-- اختيار الجهاز -->
            <div class="section">
                <h3>📱 اختيار الجهاز</h3>
                <div class="device-grid">
                    <div class="device-btn" data-device="iphone-se" data-width="375" data-height="667">
                        iPhone SE<br>375×667
                    </div>
                    <div class="device-btn active" data-device="iphone-12" data-width="390" data-height="844">
                        iPhone 12<br>390×844
                    </div>
                    <div class="device-btn" data-device="iphone-14-pro-max" data-width="428" data-height="926">
                        iPhone 14 Pro Max<br>428×926
                    </div>
                    <div class="device-btn" data-device="galaxy" data-width="412" data-height="915">
                        Samsung Galaxy<br>412×915
                    </div>
                    <div class="device-btn" data-device="ipad" data-width="768" data-height="1024">
                        iPad Portrait<br>768×1024
                    </div>
                    <div class="device-btn" data-device="desktop" data-width="1920" data-height="1080">
                        Desktop<br>1920×1080
                    </div>
                </div>
            </div>

            <!-- معلومات الشبكة -->
            <div class="section">
                <h3>📊 معلومات الشبكة</h3>
                <div class="device-info" id="gridInfo">
                    <strong>الشبكة:</strong> 5×5 مربع<br>
                    <strong>المربعات النشطة:</strong> 16 مربع<br>
                    <strong>المربعات الداخلية:</strong> 9 مربع<br>
                    <strong>الجهاز:</strong> iPhone 12/13/14
                </div>
                
                <!-- دليل الألوان -->
                <div class="square-legend">
                    <div class="legend-item">
                        <div class="legend-color" style="background: rgba(0, 255, 136, 0.2);"></div>
                        <span>عادي</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: rgba(255, 165, 0, 0.2);"></div>
                        <span>نصف فاكهة</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: rgba(255, 215, 0, 0.2);"></div>
                        <span>BAR مكدس</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: rgba(128, 0, 128, 0.2);"></div>
                        <span>داخلي</span>
                    </div>
                </div>
            </div>

            <!-- أدوات التحكم -->
            <div class="section">
                <h3>🛠️ أدوات التحكم</h3>
                <!-- إدراج صورة خلفية -->
                <div class="control-group">
                    <label>�️ اختيار صورة خلفية للجهاز:</label>
                    <input type="file" id="bgImageInput" accept="image/*" style="width:100%;padding:8px;" />
                </div>
                <div class="control-group">
                    <label>�📏 حجم الشبكة:</label>
                    <select id="gridSize" onchange="changeGridSize()">
                        <option value="200">صغير (200px)</option>
                        <option value="250" selected>متوسط (250px)</option>
                        <option value="300">كبير (300px)</option>
                        <option value="350">كبير جداً (350px)</option>
                    </select>
                </div>
                <div class="control-group">
                    <label>📍 موقع الشبكة:</label>
                    <select id="gridPosition" onchange="changeGridPosition()">
                        <option value="center" selected>الوسط</option>
                        <option value="top-center">أعلى الوسط</option>
                        <option value="bottom-center">أسفل الوسط</option>
                        <option value="custom">مخصص (اسحب الشبكة بالماوس)</option>
                    </select>
                    <div style="margin-top:8px;color:#aaa;font-size:12px;">يمكنك الآن سحب الشبكة بالفأرة أو اللمس وتعيين الموقع المناسب مباشرة.</div>
                </div>
                <button class="btn btn-primary" onclick="toggleGrid()">
                    📐 تفعيل/إلغاء الشبكة المساعدة
                </button>
                <button class="btn btn-secondary" onclick="resetGrid()">
                    🔄 إعادة تعيين الشبكة
                </button>
                <button class="btn btn-warning" onclick="exportGridCode()">
                    💾 تصدير كود الشبكة
                </button>
            </div>

            <!-- الكود المُولد -->
            <div class="section">
                <h3>💻 كود الشبكة</h3>
                <div class="code-output" id="codeOutput">
// سيظهر كود الشبكة هنا بعد التعديل
                </div>
                <button class="btn btn-primary" onclick="copyGridCode()" style="margin-top: 10px;">
                    📋 نسخ الكود
                </button>
            </div>
        </div>

        <!-- المنطقة الرئيسية -->
        <div class="main-area">
            <div class="preview-area">
                <div class="device-frame" id="deviceFrame">
                    <div class="device-screen" id="deviceScreen">
                        <div class="grid-overlay" id="gridOverlay"></div>
                        
                        <!-- شبكة اللعبة 5×5 -->
                        <div class="game-grid" id="gameGrid">
                            <!-- الصف الأول -->
                            <div class="game-square normal" data-index="0" data-symbol="APPLE">🍎</div>
                            <div class="game-square half-fruit" data-index="1" data-symbol="LEMON">🍋/2</div>
                            <div class="game-square stacked-bar" data-index="2" data-symbol="BAR">BAR</div>
                            <div class="game-square normal" data-index="3" data-symbol="LEMON">🍋</div>
                            <div class="game-square normal" data-index="4" data-symbol="APPLE">🍎</div>
                            
                            <!-- الصف الثاني -->
                            <div class="game-square half-fruit" data-index="5" data-symbol="BANANA">🍌/2</div>
                            <div class="game-square inner" data-index="6" data-symbol="">-</div>
                            <div class="game-square inner" data-index="7" data-symbol="">-</div>
                            <div class="game-square inner" data-index="8" data-symbol="">-</div>
                            <div class="game-square half-fruit" data-index="9" data-symbol="APPLE">🍎/2</div>
                            
                            <!-- الصف الثالث -->
                            <div class="game-square normal" data-index="10" data-symbol="WATERMELON">🍉</div>
                            <div class="game-square inner" data-index="11" data-symbol="">-</div>
                            <div class="game-square inner" data-index="12" data-symbol="">⏱️</div>
                            <div class="game-square inner" data-index="13" data-symbol="">-</div>
                            <div class="game-square normal" data-index="14" data-symbol="BAR">BAR</div>
                            
                            <!-- الصف الرابع -->
                            <div class="game-square half-fruit" data-index="15" data-symbol="WATERMELON">🍉/2</div>
                            <div class="game-square inner" data-index="16" data-symbol="">-</div>
                            <div class="game-square inner" data-index="17" data-symbol="">-</div>
                            <div class="game-square inner" data-index="18" data-symbol="">-</div>
                            <div class="game-square normal" data-index="19" data-symbol="BANANA">🍌</div>
                            
                            <!-- الصف الخامس -->
                            <div class="game-square normal" data-index="20" data-symbol="APPLE">🍎</div>
                            <div class="game-square normal" data-index="21" data-symbol="BANANA">🍌</div>
                            <div class="game-square normal" data-index="22" data-symbol="WATERMELON">🍉</div>
                            <div class="game-square half-fruit" data-index="23" data-symbol="WATERMELON">🍉/2</div>
                            <div class="game-square normal" data-index="24" data-symbol="APPLE">🍎</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- شريط الحالة -->
    <div class="status-bar" id="statusBar">
        🎮 مدير شبكة اللعبة | اسحب المربعات لترتيبها | الجهاز: iPhone 12/13/14 (390×844)
    </div>

    <script>
        // إدراج صورة خلفية للجهاز
        document.addEventListener('DOMContentLoaded', function() {
            var bgInput = document.getElementById('bgImageInput');
            if (bgInput) {
                bgInput.addEventListener('change', function(e) {
                    var file = e.target.files && e.target.files[0];
                    if (file) {
                        var reader = new FileReader();
                        reader.onload = function(ev) {
                            var deviceScreen = document.getElementById('deviceScreen');
                            if (deviceScreen) {
                                deviceScreen.style.backgroundImage = 'url(' + ev.target.result + ')';
                            }
                        };
                        reader.readAsDataURL(file);
                    }
                });
            }
        });
        // البيانات الأساسية
        const devices = {
            'iphone-se': { name: 'iPhone SE', width: 375, height: 667 },
            'iphone-12': { name: 'iPhone 12/13/14', width: 390, height: 844 },
            'iphone-14-pro-max': { name: 'iPhone 14 Pro Max', width: 428, height: 926 },
            'galaxy': { name: 'Samsung Galaxy', width: 412, height: 915 },
            'ipad': { name: 'iPad Portrait', width: 768, height: 1024 },
            'desktop': { name: 'Desktop', width: 1920, height: 1080 }
        };

        let currentDevice = 'iphone-12';
        let isDragging = false;
        let dragElement = null;
        let dragOffset = { x: 0, y: 0 };

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            initializeDeviceSelection();
            initializeDragAndDrop();
            updateDeviceDisplay();
            updateGridCode();
        });

        // تهيئة اختيار الأجهزة
        function initializeDeviceSelection() {
            const deviceButtons = document.querySelectorAll('.device-btn');
            
            deviceButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    deviceButtons.forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    currentDevice = this.dataset.device;
                    
                    updateDeviceDisplay();
                    updateGridCode();
                    updateStatusBar();
                });
            });
        }

        // تحديث عرض الجهاز
        function updateDeviceDisplay() {
            const device = devices[currentDevice];
            const frame = document.getElementById('deviceFrame');
            const screen = document.getElementById('deviceScreen');
            const info = document.getElementById('gridInfo');
            
            const maxWidth = 800;
            const maxHeight = 600;
            const scale = Math.min(maxWidth / device.width, maxHeight / device.height, 1);
            
            const displayWidth = device.width * scale;
            const displayHeight = device.height * scale;
            
            screen.style.width = displayWidth + 'px';
            screen.style.height = displayHeight + 'px';
            
            info.innerHTML = `
                <strong>الشبكة:</strong> 5×5 مربع<br>
                <strong>المربعات النشطة:</strong> 16 مربع<br>
                <strong>المربعات الداخلية:</strong> 9 مربع<br>
                <strong>الجهاز:</strong> ${device.name}
            `;
        }

        // تهيئة السحب والإفلات والتكبير
        function initializeDragAndDrop() {
            const grid = document.getElementById('gameGrid');
            grid.style.cursor = 'grab';
            // سحب الشبكة
            grid.addEventListener('mousedown', function(e) {
                if (document.getElementById('gridPosition').value === 'custom') startGridDrag(e);
            });
            grid.addEventListener('touchstart', function(e) {
                if (document.getElementById('gridPosition').value === 'custom') startGridDrag(e);
            }, { passive: false });
            document.addEventListener('mousemove', dragGrid);
            document.addEventListener('mouseup', endGridDrag);
            document.addEventListener('touchmove', dragGrid, { passive: false });
            document.addEventListener('touchend', endGridDrag);

            // إضافة مقبض تغيير الحجم
            const resizeHandle = document.createElement('div');
            resizeHandle.id = 'resizeHandle';
            resizeHandle.style.position = 'absolute';
            resizeHandle.style.right = '-12px';
            resizeHandle.style.bottom = '-12px';
            resizeHandle.style.width = '24px';
            resizeHandle.style.height = '24px';
            resizeHandle.style.background = 'rgba(255,255,255,0.15)';
            resizeHandle.style.border = '2px solid #FFD700';
            resizeHandle.style.borderRadius = '50%';
            resizeHandle.style.cursor = 'nwse-resize';
            resizeHandle.style.zIndex = '10000';
            resizeHandle.title = 'تغيير حجم الشبكة';
            grid.appendChild(resizeHandle);

            let resizing = false;
            let startW = 0, startH = 0, startX = 0, startY = 0;

            resizeHandle.addEventListener('mousedown', function(e) {
                e.stopPropagation();
                resizing = true;
                startW = grid.offsetWidth;
                startH = grid.offsetHeight;
                startX = e.clientX;
                startY = e.clientY;
                document.body.style.userSelect = 'none';
            });
            document.addEventListener('mousemove', function(e) {
                if (!resizing) return;
                let newW = Math.max(100, startW + (e.clientX - startX));
                let newH = Math.max(100, startH + (e.clientY - startY));
                grid.style.width = newW + 'px';
                grid.style.height = newH + 'px';
                updateStatusBar(`📏 الحجم: ${newW}px × ${newH}px`);
                updateGridCode();
            });
            document.addEventListener('mouseup', function() {
                if (resizing) {
                    resizing = false;
                    document.body.style.userSelect = '';
                    updateStatusBar('✅ تم تغيير حجم الشبكة');
                    setTimeout(() => updateStatusBar(), 2000);
                }
            });
        }

        // بداية سحب الشبكة
        function startGridDrag(e) {
            e.preventDefault();
            isDragging = true;
            dragElement = document.getElementById('gameGrid');
            dragElement.style.cursor = 'grabbing';
            const clientX = e.clientX || (e.touches && e.touches[0].clientX);
            const clientY = e.clientY || (e.touches && e.touches[0].clientY);
            const rect = dragElement.getBoundingClientRect();
            dragOffset.x = clientX - rect.left - rect.width / 2;
            dragOffset.y = clientY - rect.top - rect.height / 2;
            dragElement.style.border = '3px solid #ff0066';
            dragElement.style.boxShadow = '0 0 30px rgba(255, 0, 102, 0.8)';
            updateStatusBar('🚀 اسحب الشبكة لموقعك المفضل');
        }

        // سحب الشبكة
        function dragGrid(e) {
            if (!isDragging || !dragElement) return;
            e.preventDefault();
            const clientX = e.clientX || (e.touches && e.touches[0].clientX);
            const clientY = e.clientY || (e.touches && e.touches[0].clientY);
            const screen = document.getElementById('deviceScreen');
            const screenRect = screen.getBoundingClientRect();
            const x = clientX - screenRect.left - dragOffset.x;
            const y = clientY - screenRect.top - dragOffset.y;
            const leftPercent = Math.max(0, Math.min(100, (x / screenRect.width) * 100));
            const topPercent = Math.max(0, Math.min(100, (y / screenRect.height) * 100));
            dragElement.style.left = leftPercent + '%';
            dragElement.style.top = topPercent + '%';
            updateStatusBar(`📍 الشبكة: ${leftPercent.toFixed(1)}%, ${topPercent.toFixed(1)}%`);
        }

        // إنهاء سحب الشبكة
        function endGridDrag() {
            if (!isDragging) return;
            isDragging = false;
            if (dragElement) {
                dragElement.style.border = '2px solid #00ff88';
                dragElement.style.boxShadow = 'none';
                dragElement.style.cursor = 'grab';
                updateStatusBar('✅ تم وضع الشبكة في موقع جديد');
                dragElement = null;
            }
            updateGridCode();
            setTimeout(() => {
                updateStatusBar();
            }, 2000);
        }

        // تغيير حجم الشبكة
        function changeGridSize() {
            const size = document.getElementById('gridSize').value;
            const grid = document.getElementById('gameGrid');
            
            grid.style.width = size + 'px';
            grid.style.height = size + 'px';
            
            updateGridCode();
            updateStatusBar(`📏 تم تغيير حجم الشبكة إلى ${size}px`);
            
            setTimeout(() => {
                updateStatusBar();
            }, 2000);
        }

        // تغيير موقع الشبكة
        function changeGridPosition() {
            const position = document.getElementById('gridPosition').value;
            const grid = document.getElementById('gameGrid');
            
            switch (position) {
                case 'center':
                    grid.style.left = '50%';
                    grid.style.top = '50%';
                    break;
                case 'top-center':
                    grid.style.left = '50%';
                    grid.style.top = '35%';
                    break;
                case 'bottom-center':
                    grid.style.left = '50%';
                    grid.style.top = '65%';
                    break;
                case 'custom':
                    // يبقى في الموقع الحالي
                    break;
            }
            
            updateGridCode();
            updateStatusBar(`📍 تم تغيير موقع الشبكة إلى: ${position}`);
            
            setTimeout(() => {
                updateStatusBar();
            }, 2000);
        }

        // تفعيل/إلغاء الشبكة المساعدة
        function toggleGrid() {
            const grid = document.getElementById('gridOverlay');
            grid.classList.toggle('visible');
            
            const isVisible = grid.classList.contains('visible');
            updateStatusBar(isVisible ? '📐 تم عرض الشبكة المساعدة' : '📐 تم إخفاء الشبكة المساعدة');
            
            setTimeout(() => {
                updateStatusBar();
            }, 2000);
        }

        // إعادة تعيين الشبكة
        function resetGrid() {
            const grid = document.getElementById('gameGrid');
            
            grid.style.left = '50%';
            grid.style.top = '50%';
            grid.style.width = '250px';
            grid.style.height = '250px';
            
            document.getElementById('gridSize').value = '250';
            document.getElementById('gridPosition').value = 'center';
            
            updateGridCode();
            updateStatusBar('🔄 تم إعادة تعيين الشبكة للموقع الافتراضي');
            
            setTimeout(() => {
                updateStatusBar();
            }, 2000);
        }

        // تحديث كود الشبكة
        function updateGridCode() {
            const device = devices[currentDevice];
            const grid = document.getElementById('gameGrid');
            
            const left = grid.style.left || '50%';
            const top = grid.style.top || '50%';
            const width = grid.style.width || '250px';
            const height = grid.style.height || '250px';
            
            let code = `// Game Grid Layout for ${device.name} (${device.width}x${device.height})\n`;
            code += `const ${currentDevice.replace(/-/g, '')}GridLayout = {\n`;
            code += `  device: {\n`;
            code += `    name: "${device.name}",\n`;
            code += `    width: ${device.width},\n`;
            code += `    height: ${device.height}\n`;
            code += `  },\n`;
            code += `  gameGrid: {\n`;
            code += `    position: {\n`;
            code += `      left: "${left}",\n`;
            code += `      top: "${top}",\n`;
            code += `      width: "${width}",\n`;
            code += `      height: "${height}"\n`;
            code += `    },\n`;
            code += `    squares: [\n`;
            
            const squares = document.querySelectorAll('.game-square');
            squares.forEach((square, index) => {
                const symbol = square.dataset.symbol;
                const type = square.classList.contains('normal') ? 'normal' :
                           square.classList.contains('half-fruit') ? 'halfFruit' :
                           square.classList.contains('stacked-bar') ? 'stackedBar' : 'inner';
                
                code += `      { gridIndex: ${index}, symbol: '${symbol}', type: '${type}' }${index < squares.length - 1 ? ',' : ''}\n`;
            });
            
            code += `    ]\n`;
            code += `  }\n`;
            code += `};\n\n`;
            
            code += `// CSS للتطبيق\n`;
            code += `@media screen and (width: ${device.width}px) and (height: ${device.height}px) {\n`;
            code += `  .game-grid {\n`;
            code += `    left: ${left} !important;\n`;
            code += `    top: ${top} !important;\n`;
            code += `    width: ${width} !important;\n`;
            code += `    height: ${height} !important;\n`;
            code += `  }\n`;
            code += `}`;
            
            document.getElementById('codeOutput').textContent = code;
        }

        // تصدير كود الشبكة
        function exportGridCode() {
            const code = document.getElementById('codeOutput').textContent;
            
            const blob = new Blob([code], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `${currentDevice}-grid-layout.js`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            updateStatusBar('💾 تم تصدير كود الشبكة');
            
            setTimeout(() => {
                updateStatusBar();
            }, 2000);
        }

        // نسخ كود الشبكة
        function copyGridCode() {
            const code = document.getElementById('codeOutput').textContent;
            
            navigator.clipboard.writeText(code).then(() => {
                updateStatusBar('📋 تم نسخ كود الشبكة للحافظة');
                
                setTimeout(() => {
                    updateStatusBar();
                }, 2000);
            });
        }

        // تحديث شريط الحالة
        function updateStatusBar(message) {
            const statusBar = document.getElementById('statusBar');
            const device = devices[currentDevice];
            
            if (message) {
                statusBar.textContent = message;
            } else {
                statusBar.textContent = `🎮 مدير شبكة اللعبة | اسحب الشبكة لترتيبها | الجهاز: ${device.name} (${device.width}×${device.height})`;
            }
        }
    </script>
</body>
</html>
