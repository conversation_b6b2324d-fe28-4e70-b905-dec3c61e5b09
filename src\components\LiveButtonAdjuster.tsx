import React, { useState, useEffect } from 'react';
import { BUTTON_POSITIONS } from '../utils/buttonPositions';

interface LiveButtonAdjusterProps {
  isVisible: boolean;
  onToggle: () => void;
}

const LiveButtonAdjuster: React.FC<LiveButtonAdjusterProps> = ({
  isVisible,
  onToggle
}) => {
  const [selectedButton, setSelectedButton] = useState<string>('');
  const [isDragging, setIsDragging] = useState(false);
  const [showGrid, setShowGrid] = useState(true);
  const [currentDevice, setCurrentDevice] = useState<any>(null);

  useEffect(() => {
    // تحديد الجهاز الحالي
    const width = window.innerWidth;
    const height = window.innerHeight;
    
    let deviceConfig = BUTTON_POSITIONS.find(d => 
      Math.abs(d.device.width - width) < 50 && Math.abs(d.device.height - height) < 50
    );
    
    if (!deviceConfig) {
      if (width <= 768) {
        deviceConfig = BUTTON_POSITIONS.find(d => d.device.width <= 768) || BUTTON_POSITIONS[0];
      } else {
        deviceConfig = BUTTON_POSITIONS.find(d => d.device.width > 768) || BUTTON_POSITIONS[1] || BUTTON_POSITIONS[0];
      }
    }
    
    setCurrentDevice(deviceConfig);
  }, []);

  const handleMouseDown = (e: React.MouseEvent) => {
    if (!isDragging) return;
    
    const rect = document.body.getBoundingClientRect();
    const x = ((e.clientX - rect.left) / window.innerWidth) * 100;
    const y = ((e.clientY - rect.top) / window.innerHeight) * 100;
    

  };

  if (!isVisible) {
    return (
      <button
        onClick={onToggle}
        style={{
          position: 'fixed',
          top: '130px',
          right: '10px',
          zIndex: 9999,
          background: 'rgba(255, 100, 0, 0.8)',
          color: '#fff',
          border: '1px solid #ff6600',
          borderRadius: '4px',
          padding: '8px 12px',
          cursor: 'pointer',
          fontSize: '12px',
        }}
      >
        🎯 Live Adjuster
      </button>
    );
  }

  return (
    <>
      {/* الشبكة المرجعية */}
      {showGrid && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100vw',
          height: '100vh',
          pointerEvents: 'none',
          zIndex: 9997,
        }}>
          {/* خطوط عمودية */}
          {[10, 20, 30, 40, 50, 60, 70, 80, 90].map(percent => (
            <div
              key={`v-${percent}`}
              style={{
                position: 'absolute',
                left: `${percent}%`,
                top: 0,
                width: '1px',
                height: '100%',
                background: 'rgba(255, 255, 255, 0.3)',
              }}
            />
          ))}
          {/* خطوط أفقية */}
          {[10, 20, 30, 40, 50, 60, 70, 80, 90].map(percent => (
            <div
              key={`h-${percent}`}
              style={{
                position: 'absolute',
                top: `${percent}%`,
                left: 0,
                width: '100%',
                height: '1px',
                background: 'rgba(255, 255, 255, 0.3)',
              }}
            />
          ))}
        </div>
      )}

      {/* مؤشرات المواقع الحالية */}
      {currentDevice && Object.entries(currentDevice.symbolButtons).map(([key, pos]: [string, any]) => (
        <div
          key={`indicator-${key}`}
          style={{
            position: 'fixed',
            left: pos.left,
            top: pos.top,
            width: '20px',
            height: '20px',
            background: 'rgba(255, 0, 0, 0.7)',
            border: '2px solid #fff',
            borderRadius: '50%',
            transform: 'translate(-50%, -50%)',
            zIndex: 9998,
            pointerEvents: 'none',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '8px',
            color: '#fff',
            fontWeight: 'bold',
          }}
        >
          {key.substring(0, 1).toUpperCase()}
        </div>
      ))}

      {/* مؤشرات أزرار المبالغ */}
      {currentDevice && Object.entries(currentDevice.amountButtons).map(([key, pos]: [string, any]) => (
        <div
          key={`amount-indicator-${key}`}
          style={{
            position: 'fixed',
            left: pos.left,
            top: pos.top,
            width: '16px',
            height: '16px',
            background: 'rgba(0, 255, 0, 0.7)',
            border: '2px solid #fff',
            borderRadius: '50%',
            transform: 'translate(-50%, -50%)',
            zIndex: 9998,
            pointerEvents: 'none',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '6px',
            color: '#fff',
            fontWeight: 'bold',
          }}
        >
          {key.slice(-1)}
        </div>
      ))}

      {/* لوحة التحكم */}
      <div style={{
        position: 'fixed',
        top: '20px',
        right: '20px',
        width: '280px',
        background: 'rgba(0, 0, 0, 0.9)',
        border: '1px solid #333',
        borderRadius: '8px',
        padding: '16px',
        zIndex: 9999,
        color: '#fff',
        fontSize: '12px',
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '16px' }}>
          <h3 style={{ margin: 0, fontSize: '14px' }}>Live Button Adjuster</h3>
          <button onClick={onToggle} style={{ background: 'none', border: 'none', color: '#ff4444', cursor: 'pointer', fontSize: '16px' }}>✕</button>
        </div>

        {currentDevice && (
          <div style={{ marginBottom: '16px' }}>
            <strong>الجهاز:</strong> {currentDevice.device.name}<br/>
            <strong>الأبعاد:</strong> {window.innerWidth}×{window.innerHeight}
          </div>
        )}

        <div style={{ marginBottom: '16px' }}>
          <label style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <input
              type="checkbox"
              checked={showGrid}
              onChange={(e) => setShowGrid(e.target.checked)}
            />
            إظهار الشبكة المرجعية
          </label>
        </div>

        <div style={{ marginBottom: '16px' }}>
          <strong>المؤشرات:</strong><br/>
          🔴 أزرار الفواكه<br/>
          🟢 أزرار المبالغ
        </div>

        <div style={{ marginBottom: '16px', padding: '8px', background: 'rgba(255, 255, 255, 0.1)', borderRadius: '4px' }}>
          <strong>التعليمات:</strong><br/>
          • انقر في أي مكان لرؤية الإحداثيات<br/>
          • استخدم الشبكة للمحاذاة<br/>
          • المؤشرات تظهر المواقع الحالية
        </div>

        <button
          onClick={() => {
            const positions = [];
            if (currentDevice) {
              positions.push('// المواقع الحالية:');
              Object.entries(currentDevice.symbolButtons).forEach(([key, pos]: [string, any]) => {
                positions.push(`${key}: { left: '${pos.left}', top: '${pos.top}' }`);
              });
            }
    
            navigator.clipboard.writeText(positions.join('\n'));
            alert('تم نسخ المواقع الحالية!');
          }}
          style={{
            width: '100%',
            padding: '8px',
            background: '#2196f3',
            color: '#fff',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '12px',
          }}
        >
          📋 نسخ المواقع الحالية
        </button>
      </div>

      {/* طبقة التفاعل */}
      <div
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100vw',
          height: '100vh',
          zIndex: 9996,
          cursor: 'crosshair',
        }}
        onClick={handleMouseDown}
      />
    </>
  );
};

export default LiveButtonAdjuster;
