/* 🎨 أنماط المكونات المحسنة - نظام تصميم موحد */

/* ===============================
   🎯 المتغيرات العامة
   =============================== */
:root {
  /* الألوان الأساسية */
  --color-primary: #FFD700;
  --color-secondary: #8B4513;
  --color-background: #0F0620;
  --color-surface: #1A0E2E;
  --color-text-primary: #FFFFFF;
  --color-text-secondary: #E0E0E0;
  --color-text-accent: #FFD700;
  
  /* المسافات */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  
  /* الحدود */
  --border-radius-sm: 6px;
  --border-radius-md: 12px;
  --border-radius-lg: 16px;
  --border-radius-full: 50%;
  
  /* الظلال */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.5);
  --shadow-glow: 0 0 20px rgba(255, 215, 0, 0.6);
  
  /* الانتقالات */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
}

/* ===============================
   🎮 أزرار اللعبة المحسنة
   =============================== */

/* الزر الأساسي */
.game-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: var(--border-radius-md);
  cursor: pointer;
  transition: all var(--transition-normal);
  user-select: none;
  font-weight: 600;
  text-align: center;
  overflow: hidden;
  
  /* تحسين الأداء */
  will-change: transform, box-shadow;
  backface-visibility: hidden;
  
  /* منع التحديد */
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
}

.game-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  opacity: 0;
  transition: opacity var(--transition-fast);
  pointer-events: none;
}

.game-button:hover::before {
  opacity: 1;
}

.game-button:active {
  transform: scale(0.95);
}

/* أزرار الفواكه */
.fruit-button {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #F5DEB3 0%, #DEB887 30%, #D2B48C 70%, #BC9A6A 100%);
  border: 3px solid var(--color-primary);
  color: #000000;
  font-size: 12px;
  font-weight: 600;
  box-shadow: var(--shadow-md), inset 0 2px 4px rgba(255, 255, 255, 0.2);
  transform: translate(-50%, -50%);
}

.fruit-button:hover {
  transform: translate(-50%, -50%) scale(1.05);
  box-shadow: var(--shadow-lg), var(--shadow-glow), inset 0 2px 4px rgba(255, 255, 255, 0.3);
}

.fruit-button:active {
  transform: translate(-50%, -50%) scale(0.95);
}

.fruit-button.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  filter: grayscale(0.5);
}

.fruit-button.disabled:hover {
  transform: translate(-50%, -50%) scale(1);
  box-shadow: var(--shadow-md);
}

/* أزرار المبالغ */
.amount-button {
  width: 40px;
  height: 40px;
  border-radius: var(--border-radius-full);
  background: linear-gradient(135deg, #F5DEB3 0%, #DEB887 100%);
  border: 2px solid #8B7355;
  color: var(--color-text-accent);
  font-size: 10px;
  font-weight: 600;
  box-shadow: var(--shadow-sm);
  transform: translate(-50%, -50%);
}

.amount-button:hover {
  transform: translate(-50%, -50%) scale(1.1);
  border-color: var(--color-primary);
  box-shadow: var(--shadow-md), 0 0 15px rgba(255, 215, 0, 0.5);
}

.amount-button.selected {
  background: linear-gradient(135deg, #FFD700, #FFA500);
  border: 3px solid var(--color-primary);
  color: #000000;
  box-shadow: var(--shadow-lg), var(--shadow-glow), inset 0 0 10px rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%) scale(1.05);
}

.amount-button.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  filter: grayscale(0.5);
}

/* ===============================
   📊 عناصر العرض
   =============================== */

/* عرض الرصيد */
.balance-display {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(20, 20, 20, 0.8) 100%);
  border: 2px solid var(--color-primary);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  box-shadow: var(--shadow-md), 0 0 10px rgba(255, 215, 0, 0.3);
  backdrop-filter: blur(10px);
}

.balance-text {
  font-family: 'Roboto Mono', monospace;
  font-size: 18px;
  font-weight: 700;
  color: var(--color-text-accent);
  text-shadow: 0 0 4px rgba(255, 255, 255, 0.8), 0 1px 2px rgba(0, 0, 0, 0.8);
  letter-spacing: 0.5px;
}

/* عرض الرهان الإجمالي */
.total-bet-display {
  background: linear-gradient(135deg, rgba(139, 69, 19, 0.8) 0%, rgba(160, 82, 45, 0.8) 100%);
  border: 2px solid var(--color-secondary);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  box-shadow: var(--shadow-md);
  backdrop-filter: blur(10px);
}

.bet-text {
  font-family: 'Roboto Mono', monospace;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-primary);
  text-shadow: 0 0 4px rgba(255, 255, 255, 0.8);
  letter-spacing: 0.3px;
}

/* عرض مبالغ الرهانات */
.bet-display {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.9) 0%, rgba(20, 20, 20, 0.9) 100%);
  border: 1px solid var(--color-primary);
  border-radius: var(--border-radius-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  min-width: 40px;
  box-shadow: var(--shadow-sm);
  backdrop-filter: blur(5px);
  transform: translate(-50%, -50%);
}

.bet-amount-text {
  font-family: 'Roboto Mono', monospace;
  font-size: 14px;
  font-weight: 700;
  color: #FF0000;
  text-shadow: 0 0 4px rgba(255, 215, 0, 0.8);
  text-align: center;
}

/* ===============================
   ⚠️ التنبيهات والرسائل
   =============================== */

.phase-alert {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
  
  background: linear-gradient(145deg, rgba(0, 0, 0, 0.95), rgba(20, 20, 20, 0.95));
  border: 3px solid var(--color-secondary);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg) var(--spacing-xl);
  
  box-shadow: var(--shadow-lg), 0 0 50px rgba(255, 215, 0, 0.3);
  backdrop-filter: blur(15px);
  
  max-width: 80vw;
  text-align: center;
  
  animation: fadeInScale 0.3s ease-out;
}

.phase-alert-text {
  font-family: 'Playfair Display', serif;
  font-size: 18px;
  font-weight: 700;
  color: var(--color-text-accent);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
  line-height: 1.4;
}

/* ===============================
   🎭 الحركات والتأثيرات
   =============================== */

@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    transform: translate(-50%, -50%) scale(1.05);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: var(--shadow-md);
  }
  50% {
    box-shadow: var(--shadow-md), var(--shadow-glow);
  }
}

/* تأثيرات التفاعل */
.pulse-effect {
  animation: pulse 1.5s ease-in-out infinite;
}

.glow-effect {
  animation: glow 2s ease-in-out infinite;
}

/* ===============================
   📱 التصميم المتجاوب
   =============================== */

/* الشاشات الصغيرة */
@media (max-width: 768px) {
  .fruit-button {
    width: 50px;
    height: 50px;
    font-size: 10px;
  }
  
  .amount-button {
    width: 35px;
    height: 35px;
    font-size: 9px;
  }
  
  .balance-text {
    font-size: 16px;
  }
  
  .bet-text {
    font-size: 14px;
  }
  
  .phase-alert-text {
    font-size: 16px;
  }
}

/* الشاشات الكبيرة */
@media (min-width: 1200px) {
  .fruit-button {
    width: 70px;
    height: 70px;
    font-size: 14px;
  }
  
  .amount-button {
    width: 45px;
    height: 45px;
    font-size: 12px;
  }
  
  .balance-text {
    font-size: 20px;
  }
  
  .bet-text {
    font-size: 18px;
  }
  
  .phase-alert-text {
    font-size: 20px;
  }
}

/* ===============================
   🎯 تحسينات الأداء
   =============================== */

/* تحسين الرسم */
.game-button,
.balance-display,
.total-bet-display,
.bet-display {
  contain: layout style paint;
}

/* تحسين التمرير */
@media (hover: none) and (pointer: coarse) {
  .game-button {
    touch-action: manipulation;
  }
  
  .game-button:active {
    transform: scale(0.95);
  }
}

/* تقليل الحركة للمستخدمين الذين يفضلون ذلك */
@media (prefers-reduced-motion: reduce) {
  .game-button,
  .phase-alert {
    animation: none !important;
    transition: none !important;
  }
}
