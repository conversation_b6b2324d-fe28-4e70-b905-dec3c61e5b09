import { BetsOnTypes } from '../types/game';
import { ACTIVE_GAME_SQUARES, PAYOUT_MULTIPLIERS } from '../constants/gameConfig';

// حساب الأرباح بناءً على المربعات المختارة
export const calculateWin = (selectedIndices: number[], betsOnTypes: BetsOnTypes) => {
  console.log('💰 بدء حساب الأرباح...');
  console.log('🎯 المربعات المختارة:', selectedIndices);
  console.log('💵 الرهانات:', betsOnTypes);

  let totalWinAmount = 0;
  const messages: string[] = [];
  const collectedSymbols: string[] = [];

  // معالجة كل مربع مختار
  selectedIndices.forEach(index => {
    if (index >= 0 && index < ACTIVE_GAME_SQUARES.length) {
      const square = ACTIVE_GAME_SQUARES[index];
      const symbol = square.symbol;
      const type = square.type;
      
      console.log(`🔍 معالجة المربع ${index}: ${symbol} (${type})`);
      
      // إضافة الرمز للمجموعة
      if (type === 'halfFruit') {
        collectedSymbols.push(`${symbol} x2`);
      } else if (type === 'luckyDoubleText') {
        collectedSymbols.push('LUCKY 2');
      } else if (type === 'luckyTripleText') {
        collectedSymbols.push('LUCKY 3');
      } else {
        collectedSymbols.push(symbol);
      }

      // حساب الربح
      const betAmount = betsOnTypes[symbol as keyof BetsOnTypes] || 0;
      
      if (betAmount > 0) {
        let winAmount = 0;
        let multiplier = 0;

        if (type === 'halfFruit') {
          // مربعات النصف فاكهة
          multiplier = PAYOUT_MULTIPLIERS['half-fruit-win'];
          winAmount = betAmount * multiplier;
          messages.push(`🎉 ${symbol} x2 - ربح ${winAmount.toLocaleString()}$ (${betAmount.toLocaleString()}$ × ${multiplier})`);
        } else if (type === 'luckyDoubleText') {
          // Lucky 2
          multiplier = PAYOUT_MULTIPLIERS['lucky-double-text'];
          winAmount = betAmount * multiplier;
          messages.push(`🍀 LUCKY 2 - ربح ${winAmount.toLocaleString()}$ (${betAmount.toLocaleString()}$ × ${multiplier})`);
        } else if (type === 'luckyTripleText') {
          // Lucky 3
          multiplier = PAYOUT_MULTIPLIERS['lucky-triple-text'];
          winAmount = betAmount * multiplier;
          messages.push(`🍀 LUCKY 3 - ربح ${winAmount.toLocaleString()}$ (${betAmount.toLocaleString()}$ × ${multiplier})`);
        } else {
          // الرموز العادية
          multiplier = PAYOUT_MULTIPLIERS[symbol as keyof typeof PAYOUT_MULTIPLIERS];
          if (multiplier) {
            winAmount = betAmount * multiplier;
            messages.push(`🎯 ${symbol} - ربح ${winAmount.toLocaleString()}$ (${betAmount.toLocaleString()}$ × ${multiplier})`);
          }
        }

        totalWinAmount += winAmount;
        console.log(`💰 ربح من ${symbol}: ${winAmount}$ (مضاعف: ${multiplier})`);
      } else {
        console.log(`❌ لا يوجد رهان على ${symbol}`);
      }
    }
  });

  // رسالة إجمالية
  if (totalWinAmount > 0) {
    messages.push(`🏆 إجمالي الربح: ${totalWinAmount.toLocaleString()}$`);
    console.log(`🎊 إجمالي الربح: ${totalWinAmount}$`);
  } else {
    messages.push('😔 لا توجد أرباح هذه المرة');
    console.log('💔 لا توجد أرباح');
  }

  return {
    totalWinAmount,
    messages,
    collectedSymbols
  };
};

// حساب أرباح اللاكي المتعددة
export const calculateLuckyMultiWin = (finalPositions: number[], betsOnTypes: BetsOnTypes) => {
  console.log('🍀 حساب أرباح اللاكي المتعددة...');
  console.log('🎯 المواقع النهائية:', finalPositions);

  let totalWinAmount = 0;
  const messages: string[] = [];
  const collectedSymbols: string[] = [];

  // معالجة كل موقع نهائي
  finalPositions.forEach((position, index) => {
    // البحث عن المربع في المسار النشط
    const squareIndex = ACTIVE_GAME_SQUARES.findIndex(sq => sq.gridIndex === position);
    
    if (squareIndex !== -1) {
      const square = ACTIVE_GAME_SQUARES[squareIndex];
      const symbol = square.symbol;
      const type = square.type;
      
      console.log(`🔍 معالجة الموقع ${index + 1}: ${symbol} (${type}) في الموقع ${position}`);
      
      // إضافة الرمز للمجموعة
      if (type === 'halfFruit') {
        collectedSymbols.push(`${symbol} x2`);
      } else if (type === 'luckyDoubleText') {
        collectedSymbols.push('LUCKY 2');
      } else if (type === 'luckyTripleText') {
        collectedSymbols.push('LUCKY 3');
      } else {
        collectedSymbols.push(symbol);
      }

      // حساب الربح
      const betAmount = betsOnTypes[symbol as keyof BetsOnTypes] || 0;
      
      if (betAmount > 0) {
        let winAmount = 0;
        let multiplier = 0;

        if (type === 'halfFruit') {
          multiplier = PAYOUT_MULTIPLIERS['half-fruit-win'];
          winAmount = betAmount * multiplier;
          messages.push(`🎉 ${symbol} x2 - ربح ${winAmount.toLocaleString()}$ (${betAmount.toLocaleString()}$ × ${multiplier})`);
        } else if (type === 'luckyDoubleText') {
          multiplier = PAYOUT_MULTIPLIERS['lucky-double-text'];
          winAmount = betAmount * multiplier;
          messages.push(`🍀 LUCKY 2 - ربح ${winAmount.toLocaleString()}$ (${betAmount.toLocaleString()}$ × ${multiplier})`);
        } else if (type === 'luckyTripleText') {
          multiplier = PAYOUT_MULTIPLIERS['lucky-triple-text'];
          winAmount = betAmount * multiplier;
          messages.push(`🍀 LUCKY 3 - ربح ${winAmount.toLocaleString()}$ (${betAmount.toLocaleString()}$ × ${multiplier})`);
        } else {
          multiplier = PAYOUT_MULTIPLIERS[symbol as keyof typeof PAYOUT_MULTIPLIERS];
          if (multiplier) {
            winAmount = betAmount * multiplier;
            messages.push(`🎯 ${symbol} - ربح ${winAmount.toLocaleString()}$ (${betAmount.toLocaleString()}$ × ${multiplier})`);
          }
        }

        totalWinAmount += winAmount;
        console.log(`💰 ربح من ${symbol}: ${winAmount}$ (مضاعف: ${multiplier})`);
      }
    }
  });

  // رسالة إجمالية
  if (totalWinAmount > 0) {
    messages.push(`🏆 إجمالي الربح من اللاكي: ${totalWinAmount.toLocaleString()}$`);
    console.log(`🎊 إجمالي ربح اللاكي: ${totalWinAmount}$`);
  } else {
    messages.push('😔 لا توجد أرباح من اللاكي هذه المرة');
    console.log('💔 لا توجد أرباح من اللاكي');
  }

  return {
    totalWinAmount,
    messages,
    collectedSymbols
  };
};
