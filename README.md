# 🎰 Lucky Ocean Game

لعبة رهان بالفواكه مع نظام إضاءة دوارة وتصميم ثلاثي الأبعاد

## 🎮 وصف اللعبة

Lucky Ocean هي لعبة رهان تفاعلية تحاكي ماكينات القمار التقليدية مع لمسة عصرية. تتميز اللعبة بـ:

- **نظام رهان متقدم**: راهن على رموز الفواكه المختلفة (🍎، 🍌، 🍋، 🍉، BAR)
- **إضاءة دوارة**: نظام إضاءة ديناميكي يحدد النتائج
- **مضاعفات متنوعة**: كل رمز له مضاعف مختلف (x3 إلى x30)
- **نظام 24/7**: اللعبة تعمل بشكل مستمر مع جولات منتظمة
- **تصميم ثلاثي الأبعاد**: واجهة مستخدم جذابة مع تأثيرات بصرية

## 🏗️ البنية التقنية

### التقنيات المستخدمة
- **React 18** مع TypeScript
- **Vite** كأداة البناء
- **Tailwind CSS** للتصميم
- **Lucide React** للأيقونات

### هيكل المشروع
```
src/
├── 📁 components/          # مكونات React
│   ├── App.tsx            # المكون الرئيسي
│   ├── GameBoard.tsx      # لوحة اللعب
│   ├── BettingControlsSimple.tsx # أزرار الرهان
│   ├── GameInfo.tsx       # معلومات الرصيد
│   ├── ResultDisplay.tsx  # عرض النتائج
│   └── GameHistory.tsx    # تاريخ الألعاب
├── 📁 constants/          # الثوابت
│   └── gameConfig.ts      # إعدادات اللعبة
├── 📁 types/              # أنواع TypeScript
│   └── game.ts           # أنواع اللعبة
├── 📁 utils/              # دوال مساعدة
│   └── gameLogic.ts      # منطق اللعبة
├── 📁 theme/              # نظام التصميم
│   └── colors.ts         # الألوان المركزية
├── index.css             # CSS والتأثيرات
└── main.tsx             # نقطة الدخول
```

## 🎨 نظام الألوان المركزي

تم تنظيم جميع ألوان اللعبة في ملف مركزي `src/theme/colors.ts`:

### مجموعات الألوان الرئيسية:
- **GOLDEN_FRAME**: الإطار الخارجي الذهبي
- **PURPLE_INNER**: الخلفية الداخلية البنفسجية
- **RED_BETTING**: منطقة الرهان الحمراء
- **CREAM_BEIGE**: أزرار الفواكه الكريمية
- **BLACK_RECTANGLES**: المستطيلات السوداء
- **GOLD_ACCENT**: النصوص والحدود الذهبية

### مميزات النظام:
- **سهولة الصيانة**: تغيير لون واحد في مكان واحد
- **الاتساق**: نفس الألوان في جميع أنحاء التطبيق
- **المرونة**: إضافة themes جديدة بسهولة
- **التأثيرات ثلاثية الأبعاد**: انحناءات وظلال متقدمة

## 🚀 التشغيل

### متطلبات النظام
- Node.js 16+ 
- npm أو yarn

### خطوات التشغيل
```bash
# تثبيت المكتبات
npm install

# تشغيل الخادم المحلي
npm run dev

# بناء المشروع للإنتاج
npm run build

# معاينة البناء
npm run preview
```

## 🎯 قواعد اللعبة

### الرموز والمضاعفات:
- 🍎 **تفاح**: مضاعف x3
- 🍌 **موز**: مضاعف x6  
- 🍋 **ليمون**: مضاعف x8
- 🍉 **بطيخ**: مضاعف x12
- **BAR**: مضاعف x30

### آلية اللعب:
1. **فترة الرهان**: 30 ثانية لوضع الرهانات
2. **الإضاءة الدوارة**: 10 ثواني لتحديد النتيجة
3. **عرض النتائج**: 5 ثواني لإظهار الفائزين
4. **تكرار الدورة**: كل 45 ثانية جولة جديدة

### ميزات خاصة:
- **النصف فاكهة**: مضاعف x2 إضافي
- **LUCKY 2 & 3**: مضاعفات عشوائية إضافية
- **حفظ الرصيد**: يتم حفظ رصيدك محلياً

## 🔧 التطوير والصيانة

### إضافة ألوان جديدة:
```typescript
// في src/theme/colors.ts
export const COLORS = {
  NEW_COLOR_GROUP: {
    GRADIENT: 'linear-gradient(...)',
    LIGHT: '#...',
    DARK: '#...'
  }
};
```

### إضافة تأثيرات جديدة:
```typescript
// في src/theme/colors.ts
export const TRANSFORMS_3D = {
  NEW_EFFECT: {
    transform: 'perspective(...)',
    boxShadow: '...'
  }
};
```

## 📊 الإحصائيات والتتبع

- **تاريخ الألعاب**: آخر 20 نتيجة
- **رصيد اللاعب**: محفوظ محلياً
- **إحصائيات الفوز**: قيد التطوير

## 🎖️ نظام الجوائز

### الجوائز الأسبوعية:
- 🥇 **المركز الأول**: 1000$ + ميدالية ذهبية
- 🥈 **المركز الثاني**: 500$ + ميدالية فضية  
- 🥉 **المركز الثالث**: 250$ + ميدالية برونزية

## 📝 التحديثات الأخيرة

### v2.0.0 - تنظيم شامل للمشروع
- ✅ نظام ألوان مركزي منظم
- ✅ تحديث جميع المكونات لاستخدام النظام الجديد
- ✅ تنظيف CSS وإزالة التكرار
- ✅ تحسين بنية المشروع
- ✅ توثيق شامل للكود

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. Fork المشروع
2. إنشاء branch جديد للميزة
3. Commit التغييرات
4. Push إلى Branch
5. فتح Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT.

---

**تم تطوير هذا المشروع بـ ❤️ باستخدام React و TypeScript**
# appple
# appple
# appple
