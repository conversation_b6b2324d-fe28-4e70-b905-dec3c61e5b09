import { useEffect, useCallback, useRef, useState } from 'react';

// Hook لتحسين الأداء العام
export const usePerformanceOptimization = () => {
  const [isLowPerformance, setIsLowPerformance] = useState(false);
  const frameCount = useRef(0);
  const lastTime = useRef(performance.now());

  useEffect(() => {
    // مراقبة معدل الإطارات
    const checkPerformance = () => {
      const now = performance.now();
      frameCount.current++;
      
      if (now - lastTime.current >= 1000) {
        const fps = frameCount.current;
        setIsLowPerformance(fps < 30); // إذا كان أقل من 30 FPS
        frameCount.current = 0;
        lastTime.current = now;
      }
      
      requestAnimationFrame(checkPerformance);
    };

    const rafId = requestAnimationFrame(checkPerformance);
    
    return () => cancelAnimationFrame(rafId);
  }, []);

  return { isLowPerformance };
};

// Hook لتحسين الذاكرة
export const useMemoryOptimization = () => {
  const cleanupFunctions = useRef<(() => void)[]>([]);

  const addCleanup = useCallback((cleanup: () => void) => {
    cleanupFunctions.current.push(cleanup);
  }, []);

  const cleanup = useCallback(() => {
    cleanupFunctions.current.forEach(fn => fn());
    cleanupFunctions.current = [];
  }, []);

  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  return { addCleanup, cleanup };
};

// Hook لتحسين التحميل
export const useImagePreloader = (imageSources: string[]) => {
  const [loadedImages, setLoadedImages] = useState<Set<string>>(new Set());
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const preloadImages = async () => {
      const imagePromises = imageSources.map((src) => {
        return new Promise<string>((resolve, reject) => {
          const img = new Image();
          img.onload = () => resolve(src);
          img.onerror = () => reject(src);
          img.src = src;
        });
      });

      try {
        const loaded = await Promise.allSettled(imagePromises);
        const successfullyLoaded = loaded
          .filter((result): result is PromiseFulfilledResult<string> => 
            result.status === 'fulfilled'
          )
          .map(result => result.value);
        
        setLoadedImages(new Set(successfullyLoaded));
      } catch (error) {
        console.warn('Some images failed to preload:', error);
      } finally {
        setIsLoading(false);
      }
    };

    if (imageSources.length > 0) {
      preloadImages();
    } else {
      setIsLoading(false);
    }
  }, [imageSources]);

  return { loadedImages, isLoading };
};

// Hook لتحسين الأحداث
export const useThrottledCallback = <T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T => {
  const lastCall = useRef(0);
  const timeoutRef = useRef<NodeJS.Timeout>();

  return useCallback((...args: Parameters<T>) => {
    const now = Date.now();
    
    if (now - lastCall.current >= delay) {
      lastCall.current = now;
      return callback(...args);
    } else {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = setTimeout(() => {
        lastCall.current = Date.now();
        callback(...args);
      }, delay - (now - lastCall.current));
    }
  }, [callback, delay]) as T;
};

// Hook لتحسين الرسم
export const useRenderOptimization = () => {
  const [shouldRender, setShouldRender] = useState(true);
  const renderCount = useRef(0);
  const lastRenderTime = useRef(performance.now());

  const optimizedSetState = useCallback((newState: any) => {
    const now = performance.now();
    renderCount.current++;

    // تقليل معدل الرسم إذا كان مرتفعاً جداً
    if (now - lastRenderTime.current < 16) { // أقل من 60 FPS
      if (renderCount.current > 10) {
        setShouldRender(false);
        setTimeout(() => setShouldRender(true), 16);
        return;
      }
    }

    lastRenderTime.current = now;
    return newState;
  }, []);

  return { shouldRender, optimizedSetState };
};

// Hook لمراقبة استخدام الذاكرة
export const useMemoryMonitor = () => {
  const [memoryInfo, setMemoryInfo] = useState<{
    used: number;
    total: number;
    percentage: number;
  } | null>(null);

  useEffect(() => {
    const checkMemory = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        const used = memory.usedJSHeapSize;
        const total = memory.totalJSHeapSize;
        const percentage = (used / total) * 100;

        setMemoryInfo({ used, total, percentage });
      }
    };

    checkMemory();
    const interval = setInterval(checkMemory, 5000); // كل 5 ثواني

    return () => clearInterval(interval);
  }, []);

  return memoryInfo;
};

// Hook لتحسين الشبكة
export const useNetworkOptimization = () => {
  const [connectionType, setConnectionType] = useState<string>('unknown');
  const [isSlowConnection, setIsSlowConnection] = useState(false);

  useEffect(() => {
    const updateConnectionInfo = () => {
      if ('connection' in navigator) {
        const connection = (navigator as any).connection;
        setConnectionType(connection.effectiveType || 'unknown');
        setIsSlowConnection(
          connection.effectiveType === 'slow-2g' || 
          connection.effectiveType === '2g'
        );
      }
    };

    updateConnectionInfo();

    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      connection.addEventListener('change', updateConnectionInfo);
      
      return () => {
        connection.removeEventListener('change', updateConnectionInfo);
      };
    }
  }, []);

  return { connectionType, isSlowConnection };
};

// Hook لتحسين البطارية
export const useBatteryOptimization = () => {
  const [batteryLevel, setBatteryLevel] = useState<number>(1);
  const [isCharging, setIsCharging] = useState<boolean>(true);
  const [shouldReduceAnimations, setShouldReduceAnimations] = useState(false);

  useEffect(() => {
    const updateBatteryInfo = (battery: any) => {
      setBatteryLevel(battery.level);
      setIsCharging(battery.charging);
      
      // تقليل الحركات إذا كانت البطارية منخفضة
      setShouldReduceAnimations(battery.level < 0.2 && !battery.charging);
    };

    if ('getBattery' in navigator) {
      (navigator as any).getBattery().then((battery: any) => {
        updateBatteryInfo(battery);
        
        battery.addEventListener('levelchange', () => updateBatteryInfo(battery));
        battery.addEventListener('chargingchange', () => updateBatteryInfo(battery));
      });
    }
  }, []);

  return { batteryLevel, isCharging, shouldReduceAnimations };
};

// Hook شامل لتحسين الأداء
export const useComprehensiveOptimization = () => {
  const { isLowPerformance } = usePerformanceOptimization();
  const { addCleanup, cleanup } = useMemoryOptimization();
  const { shouldRender } = useRenderOptimization();
  const memoryInfo = useMemoryMonitor();
  const { isSlowConnection } = useNetworkOptimization();
  const { shouldReduceAnimations } = useBatteryOptimization();

  const optimizationSettings = {
    reduceAnimations: isLowPerformance || shouldReduceAnimations,
    reduceImageQuality: isSlowConnection || (memoryInfo?.percentage || 0) > 80,
    limitRenderRate: isLowPerformance || !shouldRender,
    preloadImages: !isSlowConnection && (memoryInfo?.percentage || 0) < 60,
    enableLazyLoading: isSlowConnection || isLowPerformance
  };

  return {
    optimizationSettings,
    performanceMetrics: {
      isLowPerformance,
      memoryUsage: memoryInfo?.percentage || 0,
      isSlowConnection,
      shouldReduceAnimations
    },
    cleanup,
    addCleanup
  };
};
