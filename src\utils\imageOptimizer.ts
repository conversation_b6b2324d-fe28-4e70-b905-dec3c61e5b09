// نظام تحسين الصور للأجهزة المختلفة
import { getScreenSize } from './responsive';

export interface ImageConfig {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  quality?: 'low' | 'medium' | 'high';
  format?: 'webp' | 'jpg' | 'png';
}

export interface OptimizedImage {
  src: string;
  srcSet?: string;
  sizes?: string;
  loading?: 'lazy' | 'eager';
  decoding?: 'async' | 'sync' | 'auto';
}

// خريطة الصور المحسنة
const IMAGE_VARIANTS = {
  // صور الفواكه
  fruits: {
    'APPLE': {
      mobile: '/images/fruits/apple-mobile.webp',
      tablet: '/images/fruits/apple-tablet.webp',
      desktop: '/images/fruits/apple-desktop.webp',
      fallback: '/images/3.png'
    },
    'BANANA': {
      mobile: '/images/fruits/banana-mobile.webp',
      tablet: '/images/fruits/banana-tablet.webp',
      desktop: '/images/fruits/banana-desktop.webp',
      fallback: '/images/6.png'
    },
    'LEMON': {
      mobile: '/images/fruits/lemon-mobile.webp',
      tablet: '/images/fruits/lemon-tablet.webp',
      desktop: '/images/fruits/lemon-desktop.webp',
      fallback: '/images/8.png'
    },
    'WATERMELON': {
      mobile: '/images/fruits/watermelon-mobile.webp',
      tablet: '/images/fruits/watermelon-tablet.webp',
      desktop: '/images/fruits/watermelon-desktop.webp',
      fallback: '/images/12.png'
    }
  },
  
  // الخلفيات
  backgrounds: {
    main: {
      mobile: '/images/bg/main-mobile.webp',
      tablet: '/images/bg/main-tablet.webp',
      desktop: '/images/bg/main-desktop.webp',
      fallback: '/222.jpg'
    }
  }
};

// تحديد جودة الصورة بناءً على الجهاز والاتصال
export const getOptimalQuality = (): 'low' | 'medium' | 'high' => {
  const screenSize = getScreenSize();
  
  // فحص سرعة الاتصال إذا كانت متاحة
  const connection = (navigator as any).connection;
  if (connection) {
    const effectiveType = connection.effectiveType;
    
    if (effectiveType === 'slow-2g' || effectiveType === '2g') {
      return 'low';
    } else if (effectiveType === '3g') {
      return screenSize.type === 'mobile' ? 'low' : 'medium';
    }
  }
  
  // تحديد الجودة بناءً على نوع الجهاز
  switch (screenSize.type) {
    case 'mobile':
      return 'medium';
    case 'tablet':
      return 'high';
    case 'desktop':
      return 'high';
    default:
      return 'medium';
  }
};

// تحسين صورة واحدة
export const optimizeImage = (config: ImageConfig): OptimizedImage => {
  const screenSize = getScreenSize();
  const quality = getOptimalQuality();
  
  // إنشاء srcSet للصور المتجاوبة
  const createSrcSet = (baseSrc: string) => {
    const variants = [];
    
    // إضافة متغيرات مختلفة للدقة
    if (quality === 'high') {
      variants.push(`${baseSrc} 1x`);
      variants.push(`${baseSrc.replace('.', '@2x.')} 2x`);
    } else {
      variants.push(`${baseSrc} 1x`);
    }
    
    return variants.join(', ');
  };
  
  // إنشاء sizes للعرض المتجاوب
  const createSizes = () => {
    switch (screenSize.type) {
      case 'mobile':
        return '(max-width: 768px) 100vw';
      case 'tablet':
        return '(max-width: 1024px) 80vw';
      case 'desktop':
        return '(min-width: 1025px) 60vw';
      default:
        return '100vw';
    }
  };
  
  return {
    src: config.src,
    srcSet: createSrcSet(config.src),
    sizes: createSizes(),
    loading: 'lazy',
    decoding: 'async'
  };
};

// الحصول على صورة فاكهة محسنة
export const getFruitImage = (symbol: string): OptimizedImage => {
  const screenSize = getScreenSize();
  const variants = IMAGE_VARIANTS.fruits[symbol as keyof typeof IMAGE_VARIANTS.fruits];
  
  if (!variants) {
    return optimizeImage({ src: '/images/3.png', alt: symbol });
  }
  
  let src: string;
  switch (screenSize.type) {
    case 'mobile':
      src = variants.mobile;
      break;
    case 'tablet':
      src = variants.tablet;
      break;
    case 'desktop':
      src = variants.desktop;
      break;
    default:
      src = variants.fallback;
  }
  
  return optimizeImage({ src, alt: symbol });
};

// الحصول على صورة خلفية محسنة
export const getBackgroundImage = (): OptimizedImage => {
  const screenSize = getScreenSize();
  const variants = IMAGE_VARIANTS.backgrounds.main;
  
  let src: string;
  switch (screenSize.type) {
    case 'mobile':
      src = variants.mobile;
      break;
    case 'tablet':
      src = variants.tablet;
      break;
    case 'desktop':
      src = variants.desktop;
      break;
    default:
      src = variants.fallback;
  }
  
  return optimizeImage({ src, alt: 'Game Background' });
};

// دالة لإنشاء خصائص الصورة المحسنة
export const getOptimizedImageProps = (config: ImageConfig) => {
  const optimized = optimizeImage(config);

  return {
    src: optimized.src,
    srcSet: optimized.srcSet,
    sizes: optimized.sizes,
    alt: config.alt,
    width: config.width,
    height: config.height,
    loading: optimized.loading as 'lazy' | 'eager',
    decoding: optimized.decoding as 'async' | 'sync' | 'auto',
    onError: (e: Event) => {
      // التراجع للصورة الاحتياطية عند الخطأ
      const target = e.target as HTMLImageElement;
      if (target.src !== config.src) {
        target.src = config.src;
      }
    }
  };
};

// تحميل مسبق للصور المهمة
export const preloadCriticalImages = () => {
  const screenSize = getScreenSize();
  const criticalImages: string[] = [];
  
  // إضافة صور الفواكه الأساسية
  Object.values(IMAGE_VARIANTS.fruits).forEach(variants => {
    switch (screenSize.type) {
      case 'mobile':
        criticalImages.push(variants.mobile);
        break;
      case 'tablet':
        criticalImages.push(variants.tablet);
        break;
      case 'desktop':
        criticalImages.push(variants.desktop);
        break;
    }
  });
  
  // إضافة الخلفية الرئيسية
  const bgVariants = IMAGE_VARIANTS.backgrounds.main;
  switch (screenSize.type) {
    case 'mobile':
      criticalImages.push(bgVariants.mobile);
      break;
    case 'tablet':
      criticalImages.push(bgVariants.tablet);
      break;
    case 'desktop':
      criticalImages.push(bgVariants.desktop);
      break;
  }
  
  // تحميل مسبق للصور
  criticalImages.forEach(src => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'image';
    link.href = src;
    document.head.appendChild(link);
  });
  
  console.log(`🖼️ تم تحميل ${criticalImages.length} صورة مسبقاً للجهاز ${screenSize.type}`);
};

// تنظيف الذاكرة من الصور غير المستخدمة
export const cleanupUnusedImages = () => {
  // إزالة الصور المحملة مسبقاً التي لم تعد مطلوبة
  const preloadLinks = document.querySelectorAll('link[rel="preload"][as="image"]');
  preloadLinks.forEach(link => {
    if (link.parentNode) {
      link.parentNode.removeChild(link);
    }
  });
  
  console.log('🧹 تم تنظيف الصور غير المستخدمة من الذاكرة');
};
