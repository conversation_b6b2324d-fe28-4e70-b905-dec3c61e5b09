// مدير التكوينات - لحفظ وتحميل إعدادات الأزرار
export interface ButtonConfig {
  device: {
    name: string;
    width: number;
    height: number;
  };
  backgroundImage: string;
  symbolButtons: { [key: string]: any };
  amountButtons: { [key: string]: any };
  betRectangles: { [key: string]: any };
  betNumbers: { [key: string]: any };
  topDisplays: { [key: string]: any };
  gameBoard: any;
}

export class ConfigManager {
  private static readonly STORAGE_PREFIX = 'buttonConfig_';
  private static readonly BACKUP_PREFIX = 'buttonConfigBackup_';

  // حفظ التكوين
  static saveConfig(deviceName: string, config: ButtonConfig): void {
    try {
      // حفظ نسخة احتياطية من التكوين الحالي
      const currentConfig = this.loadConfig(deviceName);
      if (currentConfig) {
        localStorage.setItem(
          `${this.BACKUP_PREFIX}${deviceName}`,
          JSON.stringify({
            config: currentConfig,
            timestamp: new Date().toISOString()
          })
        );
      }

      // حفظ التكوين الجديد
      localStorage.setItem(
        `${this.STORAGE_PREFIX}${deviceName}`,
        JSON.stringify({
          config,
          timestamp: new Date().toISOString(),
          version: '1.0'
        })
      );

      console.log(`✅ تم حفظ التكوين لـ ${deviceName}`);
    } catch (error) {
      console.error('❌ خطأ في حفظ التكوين:', error);
      throw new Error('فشل في حفظ التكوين');
    }
  }

  // تحميل التكوين
  static loadConfig(deviceName: string): ButtonConfig | null {
    try {
      const saved = localStorage.getItem(`${this.STORAGE_PREFIX}${deviceName}`);
      if (!saved) return null;

      const data = JSON.parse(saved);
      return data.config;
    } catch (error) {
      console.error('❌ خطأ في تحميل التكوين:', error);
      return null;
    }
  }

  // استرداد النسخة الاحتياطية
  static restoreBackup(deviceName: string): ButtonConfig | null {
    try {
      const backup = localStorage.getItem(`${this.BACKUP_PREFIX}${deviceName}`);
      if (!backup) return null;

      const data = JSON.parse(backup);
      return data.config;
    } catch (error) {
      console.error('❌ خطأ في استرداد النسخة الاحتياطية:', error);
      return null;
    }
  }

  // حذف التكوين
  static deleteConfig(deviceName: string): void {
    localStorage.removeItem(`${this.STORAGE_PREFIX}${deviceName}`);
    localStorage.removeItem(`${this.BACKUP_PREFIX}${deviceName}`);
    console.log(`🗑️ تم حذف التكوين لـ ${deviceName}`);
  }

  // الحصول على قائمة الأجهزة المحفوظة
  static getSavedDevices(): string[] {
    const devices: string[] = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith(this.STORAGE_PREFIX)) {
        devices.push(key.replace(this.STORAGE_PREFIX, ''));
      }
    }
    return devices;
  }

  // تصدير جميع التكوينات
  static exportAllConfigs(): string {
    const configs: { [deviceName: string]: any } = {};
    const devices = this.getSavedDevices();

    devices.forEach(deviceName => {
      const config = this.loadConfig(deviceName);
      if (config) {
        configs[deviceName] = config;
      }
    });

    return JSON.stringify(configs, null, 2);
  }

  // استيراد التكوينات
  static importConfigs(configsJson: string): boolean {
    try {
      const configs = JSON.parse(configsJson);
      
      Object.entries(configs).forEach(([deviceName, config]) => {
        this.saveConfig(deviceName, config as ButtonConfig);
      });

      console.log('✅ تم استيراد جميع التكوينات بنجاح');
      return true;
    } catch (error) {
      console.error('❌ خطأ في استيراد التكوينات:', error);
      return false;
    }
  }

  // تنظيف التكوينات القديمة
  static cleanupOldConfigs(daysOld: number = 30): void {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);

    for (let i = localStorage.length - 1; i >= 0; i--) {
      const key = localStorage.key(i);
      if (key && (key.startsWith(this.STORAGE_PREFIX) || key.startsWith(this.BACKUP_PREFIX))) {
        try {
          const data = JSON.parse(localStorage.getItem(key) || '{}');
          const timestamp = new Date(data.timestamp);
          
          if (timestamp < cutoffDate) {
            localStorage.removeItem(key);
            console.log(`🧹 تم حذف التكوين القديم: ${key}`);
          }
        } catch (error) {
          // حذف التكوينات التالفة
          localStorage.removeItem(key);
          console.log(`🗑️ تم حذف التكوين التالف: ${key}`);
        }
      }
    }
  }

  // إنشاء كود TypeScript للتكوين
  static generateTypeScriptCode(config: ButtonConfig): string {
    return `
// تكوين ${config.device.name} - تم إنشاؤه تلقائياً
{
  device: {
    name: '${config.device.name}',
    width: ${config.device.width},
    height: ${config.device.height},
  },
  backgroundImage: '${config.backgroundImage}',
  symbolButtons: ${JSON.stringify(config.symbolButtons, null, 4)},
  amountButtons: ${JSON.stringify(config.amountButtons, null, 4)},
  betRectangles: ${JSON.stringify(config.betRectangles, null, 4)},
  betNumbers: ${JSON.stringify(config.betNumbers, null, 4)},
  topDisplays: ${JSON.stringify(config.topDisplays, null, 4)},
  gameBoard: ${JSON.stringify(config.gameBoard, null, 4)},
},`;
  }

  // التحقق من صحة التكوين
  static validateConfig(config: any): boolean {
    const requiredFields = [
      'device', 'backgroundImage', 'symbolButtons', 
      'amountButtons', 'betRectangles', 'topDisplays'
    ];

    return requiredFields.every(field => config.hasOwnProperty(field));
  }

  // إحصائيات التكوين
  static getConfigStats(config: ButtonConfig): {
    symbolButtons: number;
    amountButtons: number;
    betRectangles: number;
    topDisplays: number;
    total: number;
  } {
    return {
      symbolButtons: Object.keys(config.symbolButtons).length,
      amountButtons: Object.keys(config.amountButtons).length,
      betRectangles: Object.keys(config.betRectangles).length,
      topDisplays: Object.keys(config.topDisplays).length,
      total: Object.keys(config.symbolButtons).length + 
             Object.keys(config.amountButtons).length + 
             Object.keys(config.betRectangles).length + 
             Object.keys(config.topDisplays).length
    };
  }

  // مقارنة التكوينات
  static compareConfigs(config1: ButtonConfig, config2: ButtonConfig): {
    identical: boolean;
    differences: string[];
  } {
    const differences: string[] = [];
    
    // مقارنة معلومات الجهاز
    if (config1.device.name !== config2.device.name) {
      differences.push(`اسم الجهاز: ${config1.device.name} ← ${config2.device.name}`);
    }
    
    // مقارنة الأزرار
    const sections = ['symbolButtons', 'amountButtons', 'betRectangles', 'topDisplays'];
    
    sections.forEach(section => {
      const buttons1 = config1[section] || {};
      const buttons2 = config2[section] || {};
      
      Object.keys(buttons1).forEach(key => {
        if (!buttons2[key]) {
          differences.push(`${section}.${key}: موجود في الأول، مفقود في الثاني`);
        } else if (JSON.stringify(buttons1[key]) !== JSON.stringify(buttons2[key])) {
          differences.push(`${section}.${key}: مختلف`);
        }
      });
      
      Object.keys(buttons2).forEach(key => {
        if (!buttons1[key]) {
          differences.push(`${section}.${key}: مفقود في الأول، موجود في الثاني`);
        }
      });
    });

    return {
      identical: differences.length === 0,
      differences
    };
  }
}

// تصدير الدوال للاستخدام المباشر
export const {
  saveConfig,
  loadConfig,
  restoreBackup,
  deleteConfig,
  getSavedDevices,
  exportAllConfigs,
  importConfigs,
  cleanupOldConfigs,
  generateTypeScriptCode,
  validateConfig,
  getConfigStats,
  compareConfigs
} = ConfigManager;
