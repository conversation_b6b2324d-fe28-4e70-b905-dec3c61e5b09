import { BetsOnTypes } from '../types/game';
import { testNewSystem } from './gameLogicNew';

// حالة تسجيل الدخول
let isAdminLoggedIn = false;

// كلمة المرور للمدير
const ADMIN_PASSWORD = 'admin123';

// نظام تسجيل الدخول
export const adminLogin = (password: string): boolean => {
  if (password === ADMIN_PASSWORD) {
    isAdminLoggedIn = true;
    console.log('🔐 تم تسجيل الدخول بنجاح!');
    console.log('📋 الأوامر المتاحة:');
    console.log('  - adminTestSelection(bets) - اختبار نظام الاختيار');
    console.log('  - adminShowHelp() - عرض المساعدة');
    console.log('  - adminLogout() - تسجيل الخروج');
    return true;
  } else {
    console.log('❌ كلمة المرور خاطئة!');
    return false;
  }
};

// تسجيل الخروج
export const adminLogout = (): void => {
  isAdminLoggedIn = false;
  console.log('👋 تم تسجيل الخروج');
};

// عرض المساعدة
export const adminShowHelp = (): void => {
  if (!isAdminLoggedIn) {
    console.log('🔐 يجب تسجيل الدخول أولاً: adminLogin("admin123")');
    return;
  }
  
  console.log('📋 أوامر لوحة المدير:');
  console.log('  adminTestSelection(bets) - اختبار نظام الاختيار مع رهانات معينة');
  console.log('  adminShowHelp() - عرض هذه المساعدة');
  console.log('  adminLogout() - تسجيل الخروج');
  console.log('');
  console.log('💡 مثال: adminTestSelection({🍌: 1000})');
};

// اختبار نظام الاختيار
export const adminTestSelection = (bets: BetsOnTypes): void => {
  if (!isAdminLoggedIn) {
    console.log('🔐 يجب تسجيل الدخول أولاً: adminLogin("admin123")');
    return;
  }
  
  console.log('🧪 بدء اختبار النظام الجديد...');
  console.log('📊 الرهانات:', bets);
  
  try {
    const result = testNewSystem(bets);
    console.log('✅ اختبار مكتمل!');
    console.log(`🎯 الموقع الفائز: ${result.winningPosition}`);
    console.log(`💡 طول تسلسل الإضاءة: ${result.lightSequence.length}`);
  } catch (error) {
    console.log('❌ خطأ في الاختبار:', error);
  }
};

// تهيئة النظام عند تحميل الصفحة
export const initializeAdminAccess = (): void => {
  console.log('🔐 نظام إدارة اللعبة الجديد متاح');
  console.log('💡 للوصول: adminLogin("admin123")');
  console.log('📋 للمساعدة: adminShowHelp()');
  
  // إضافة الأوامر للكائن العام
  (window as any).adminLogin = adminLogin;
  (window as any).adminLogout = adminLogout;
  (window as any).adminShowHelp = adminShowHelp;
  (window as any).adminTestSelection = adminTestSelection;
}; 