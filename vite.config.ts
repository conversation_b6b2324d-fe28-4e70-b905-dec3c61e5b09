import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  logLevel: 'error',
  clearScreen: false,
  server: {
    hmr: {
      overlay: false
    },
    watch: {
      ignored: ['**/node_modules/**', '**/dist/**']
    }
  },
  build: {
    rollupOptions: {
      onwarn(warning, warn) {
        // تجاهل تحذيرات الصور
        if (warning.message.includes('public directory') || 
            warning.message.includes('/public/images/')) {
          return;
        }
        warn(warning);
      }
    }
  }
});
