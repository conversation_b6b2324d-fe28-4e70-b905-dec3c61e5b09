import React, { useState, useEffect } from 'react';
import { gameEconomics } from '../utils/economics';

const EconomicStats: React.FC = () => {
  const [stats, setStats] = useState<any>(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const updateStats = () => {
      const report = gameEconomics.getEconomicReport();
      setStats(report);
    };

    updateStats();
    const interval = setInterval(updateStats, 5000); // تحديث كل 5 ثواني

    return () => clearInterval(interval);
  }, []);

  if (!stats) return null;

  return (
    <div className="fixed bottom-4 right-4 z-50">
      {/* زر إظهار/إخفاء */}
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="bg-blue-600 text-white px-3 py-2 rounded-lg shadow-lg hover:bg-blue-700 transition-colors"
      >
        {isVisible ? '📊 إخفاء الإحصائيات' : '📊 إظهار الإحصائيات'}
      </button>

      {/* لوحة الإحصائيات */}
      {isVisible && (
        <div className="mt-2 bg-white border border-gray-300 rounded-lg shadow-xl p-4 max-w-sm">
          <h3 className="font-bold text-lg mb-3 text-gray-800">📊 الإحصائيات الاقتصادية</h3>
          
          {/* الإحصائيات العامة */}
          <div className="space-y-2 mb-4">
            <div className="flex justify-between text-sm">
              <span>إجمالي الرهانات:</span>
              <span className="font-mono text-blue-600">
                {stats.totalBets.toLocaleString()}$
              </span>
            </div>
            <div className="flex justify-between text-sm">
              <span>إجمالي الأرباح:</span>
              <span className="font-mono text-green-600">
                {stats.totalWinnings.toLocaleString()}$
              </span>
            </div>
            <div className="flex justify-between text-sm border-t pt-1">
              <span className="font-semibold">صافي الربح:</span>
              <span className={`font-mono font-bold ${
                stats.netProfit >= 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                {stats.netProfit >= 0 ? '+' : ''}{stats.netProfit.toLocaleString()}$
              </span>
            </div>
            <div className="flex justify-between text-sm">
              <span>هامش الربح:</span>
              <span className={`font-mono ${
                stats.profitMargin >= 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                {stats.profitMargin.toFixed(2)}%
              </span>
            </div>
          </div>

          {/* إحصائيات اليوم */}
          <div className="space-y-2 mb-4 p-2 bg-gray-50 rounded">
            <h4 className="font-semibold text-sm text-gray-700">📅 اليوم</h4>
            <div className="flex justify-between text-xs">
              <span>رهانات اليوم:</span>
              <span className="font-mono text-blue-600">
                {stats.todayStats.bets.toLocaleString()}$
              </span>
            </div>
            <div className="flex justify-between text-xs">
              <span>أرباح اليوم:</span>
              <span className="font-mono text-green-600">
                {stats.todayStats.winnings.toLocaleString()}$
              </span>
            </div>
            <div className="flex justify-between text-xs border-t pt-1">
              <span className="font-semibold">صافي اليوم:</span>
              <span className={`font-mono font-bold ${
                stats.todayStats.netProfit >= 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                {stats.todayStats.netProfit >= 0 ? '+' : ''}{stats.todayStats.netProfit.toLocaleString()}$
              </span>
            </div>
          </div>

          {/* الحدود */}
          <div className="space-y-1 text-xs text-gray-600">
            <div className="flex justify-between">
              <span>حد الخسارة اليومية:</span>
              <span>{stats.limits.dailyLossLimit.toLocaleString()}$</span>
            </div>
            <div className="flex justify-between">
              <span>حد الخسارة للجولة:</span>
              <span>{stats.limits.maxSingleRoundLoss.toLocaleString()}$</span>
            </div>
            <div className="flex justify-between">
              <span>رسوم الخدمة:</span>
              <span>{(stats.limits.serviceFeeRate * 100).toFixed(1)}%</span>
            </div>
          </div>

          {/* زر إعادة تعيين */}
          <button
            onClick={() => {
              gameEconomics.resetStats();
              setStats(gameEconomics.getEconomicReport());
            }}
            className="w-full mt-3 bg-red-600 text-white px-2 py-1 rounded text-xs hover:bg-red-700 transition-colors"
          >
            🔄 إعادة تعيين الإحصائيات
          </button>
        </div>
      )}
    </div>
  );
};

export default EconomicStats; 