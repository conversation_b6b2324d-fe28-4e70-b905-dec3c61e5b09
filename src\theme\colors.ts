// 🎨 ملف الألوان المركزي - جميع ألوان اللعبة مطابقة للصورة المرجعية
export const COLORS = {
  // 🟤 الألوان الذهبية الدافئة (للإطار الخارجي) - مطابقة للصورة المرجعية
  GOLDEN_FRAME: {
    GRADIENT: 'linear-gradient(135deg, #DAA520 0%, #FFD700 25%, #FFA500 50%, #CD853F 75%, #B8860B 100%)',
    WARM_GOLD: '#DAA520',     // ذهبي دافئ
    BRIGHT_GOLD: '#FFD700',   // ذهبي لامع
    ORANGE_GOLD: '#FFA500',   // ذهبي برتقالي
    BRONZE_GOLD: '#CD853F',   // ذهبي برونزي
    DARK_GOLD: '#B8860B',     // ذهبي داكن
    BORDER: '#8B4513'         // حدود برونزية
  },

  // 💜 الألوان البنفسجية الغامقة (للخلفية الداخلية) - مطابقة للصورة المرجعية
  PURPLE_INNER: {
    GRADIENT: 'linear-gradient(135deg, #4A0E4E 0%, #2D1B3D 30%, #1A0E2E 70%, #0F0620 100%)',
    DARK: '#0F0620',          // بنفسجي داكن جداً
    MEDIUM: '#1A0E2E',        // بنفسجي متوسط
    LIGHT: '#2D1B3D',         // بنفسجي فاتح
    ACCENT: '#4A0E4E',        // بنفسجي للحدود
    SHIMMER: '#6A4C93'        // بنفسجي لامع للتأثيرات
  },

  // 🔴 الألوان الحمراء الداكنة (لمنطقة الرهان) - مطابقة للصورة المرجعية
  RED_BETTING: {
    GRADIENT: 'linear-gradient(180deg, #8B0000 0%, #B22222 50%, #8B0000 100%)',
    DARK_RED: '#660000',      // أحمر داكن جداً
    MEDIUM_RED: '#8B0000',    // أحمر متوسط
    LIGHT_RED: '#B22222',     // أحمر فاتح
    BORDER: '#8B4513',        // حدود برونزية
    SHADOW: 'inset 0 4px 8px rgba(0,0,0,0.4), inset 0 -4px 8px rgba(0,0,0,0.4)'
  },

  // 🟡 الألوان الصفراء والذهبية
  YELLOW: {
    PURE: '#FFFF00',      // أصفر صافي
    LIGHT: '#FFFACD',     // أصفر فاتح (Lemon Chiffon)
    MEDIUM: '#FFFFE0',    // أصفر متوسط (Light Yellow)
    PALE: '#FFFFF0',      // أصفر باهت (Ivory)
    KHAKI: '#F0E68C'      // خاكي للحدود
  },

  // 🟡 الألوان الذهبية اللامعة (للنصوص والحدود) - مطابقة للصورة المرجعية
  GOLD_ACCENT: {
    GRADIENT: 'linear-gradient(135deg, #FFD700 0%, #FFA500 50%, #DAA520 100%)',
    BRIGHT: '#FFD700',        // ذهبي لامع
    MEDIUM: '#FFA500',        // ذهبي متوسط
    WARM: '#DAA520',          // ذهبي دافئ
    TEXT_SHADOW: '0 1px 2px rgba(0,0,0,0.8), 0 0 4px rgba(255,215,0,0.6)',
    BORDER_GLOW: '0 0 8px rgba(255,215,0,0.4)'
  },

  // 🟫 الألوان الكريمية/البيج (لأزرار الفواكه) - مطابقة للصورة المرجعية
  CREAM_BEIGE: {
    GRADIENT: 'linear-gradient(135deg, #F5DEB3 0%, #DEB887 30%, #D2B48C 70%, #BC9A6A 100%)',
    LIGHT: '#F5DEB3',         // كريمي فاتح
    MEDIUM: '#DEB887',        // بيج متوسط
    WARM: '#D2B48C',          // بيج دافئ
    DARK: '#BC9A6A',          // بيج داكن
    BORDER: '#8B7355',        // حدود بنية
    SHADOW: '0 4px 8px rgba(0,0,0,0.3), inset 0 2px 4px rgba(255,255,255,0.2)'
  },

  // ⚫ الألوان السوداء (للمستطيلات) - مطابقة للصورة المرجعية
  BLACK_RECTANGLES: {
    GRADIENT: 'linear-gradient(135deg, #000000 0%, #1a1a1a 50%, #000000 100%)',
    PURE_BLACK: '#000000',    // أسود صافي
    SOFT_BLACK: '#1a1a1a',    // أسود ناعم
    BORDER: '#333333',        // حدود رمادية داكنة
    TEXT_GOLD: '#FFD700',     // نص ذهبي
    SHADOW: '0 2px 4px rgba(0,0,0,0.6), inset 0 1px 2px rgba(255,255,255,0.1)'
  },

  // 🔘 الألوان الرمادية (لأزرار الرهان)
  GRAY_BUTTONS: {
    GRADIENT: 'linear-gradient(135deg, #696969 0%, #808080 50%, #A9A9A9 100%)',
    DARK: '#555555',      // رمادي داكن للحدود
    MEDIUM: '#808080',    // رمادي متوسط
    LIGHT: '#A9A9A9'      // رمادي فاتح
  },

  // 🟠 الألوان البرتقالية (للعناصر المختارة)
  ORANGE: {
    GRADIENT: 'linear-gradient(135deg, #FF4500 0%, #FF6347 50%, #FF8C00 100%)',
    DARK: '#FF4500',      // برتقالي داكن
    MEDIUM: '#FF6347',    // برتقالي متوسط (Tomato)
    LIGHT: '#FF8C00'      // برتقالي فاتح
  },

  // 🟢 ألوان الفواكه
  FRUITS: {
    YELLOW_GRADIENT: 'linear-gradient(135deg, #FFD700 0%, #FFCC00 100%)',
    YELLOW_DISABLED: 'linear-gradient(135deg, #997515 0%, #B8860B 100%)',
    BORDER: '#FFD700',
    MULTIPLIER: {
      background: 'linear-gradient(135deg, #2D1B69 0%, #1A0E3A 100%)',
      color: '#FFD700',
      textShadow: '0 1px 2px rgba(0,0,0,0.8), 0 0 4px rgba(255,215,0,0.6)',
      border: '#FFD700'
    }
  },

  // ⚪ الألوان العامة
  COMMON: {
    WHITE: '#FFFFFF',
    BLACK: '#000000',
    TEXT_DARK: '#333333',
    TEXT_MEDIUM: '#2F4F4F',
    TEXT_LIGHT: '#8B7355',
    TRANSPARENT: 'transparent'
  },

  // 🎨 ألوان محسنة للواجهة الحديثة
  MODERN_UI: {
    // ألوان الخلفية
    BACKGROUND_PRIMARY: '#0F0620',
    BACKGROUND_SECONDARY: '#1A0E2E',
    BACKGROUND_TERTIARY: '#2D1B3D',

    // ألوان النصوص المحسنة
    TEXT_PRIMARY: '#FFFFFF',
    TEXT_SECONDARY: '#E0E0E0',
    TEXT_MUTED: '#B0B0B0',
    TEXT_ACCENT: '#FFD700',

    // ألوان الحدود
    BORDER_PRIMARY: '#FFD700',
    BORDER_SECONDARY: '#8B4513',
    BORDER_SUBTLE: '#444444',

    // ألوان التفاعل
    HOVER_OVERLAY: 'rgba(255, 215, 0, 0.1)',
    ACTIVE_OVERLAY: 'rgba(255, 215, 0, 0.2)',
    FOCUS_RING: 'rgba(255, 215, 0, 0.5)',

    // ألوان الحالة
    SUCCESS: '#10B981',
    WARNING: '#F59E0B',
    ERROR: '#EF4444',
    INFO: '#3B82F6'
  }
};

// 🎨 مجموعات الألوان للاستخدام السريع - محدثة للصورة المرجعية
export const COLOR_SCHEMES = {
  // إطار اللعبة الخارجي
  GAME_FRAME: {
    background: COLORS.GOLDEN_FRAME.GRADIENT,
    border: COLORS.GOLDEN_FRAME.BORDER
  },

  // المحتوى الداخلي
  INNER_CONTENT: {
    background: COLORS.PURPLE_INNER.GRADIENT,
    border: COLORS.PURPLE_INNER.ACCENT
  },

  // منطقة الرهان
  BETTING_AREA: {
    background: COLORS.RED_BETTING.GRADIENT,
    border: COLORS.RED_BETTING.BORDER
  },

  // أزرار الفواكه - كريمي/بيج مطابق للصورة المرجعية
  FRUIT_BUTTONS: {
    active: COLORS.CREAM_BEIGE.GRADIENT,
    disabled: COLORS.CREAM_BEIGE.WARM,
    border: COLORS.GOLDEN_FRAME.BRIGHT_GOLD,
    textColor: '#000000',
    textShadow: '0 1px 1px rgba(255,255,255,0.8)'
  },

  // أزرار مبالغ الرهان
  BET_BUTTONS: {
    normal: COLORS.CREAM_BEIGE.GRADIENT,
    selected: COLORS.ORANGE.GRADIENT,
    border: {
      normal: COLORS.CREAM_BEIGE.BORDER,
      selected: COLORS.ORANGE.DARK
    }
  },

  // زر الرهان الرئيسي - بيج دائماً مطابق للصورة المرجعية
  SPIN_BUTTON: {
    active: COLORS.CREAM_BEIGE.GRADIENT,
    disabled: COLORS.CREAM_BEIGE.WARM,
    border: COLORS.CREAM_BEIGE.BORDER,
    text: '#8B4513'
  }
};

// 🔧 دوال مساعدة للألوان
export const colorHelpers = {
  // إنشاء rgba من hex
  hexToRgba: (hex: string, alpha: number) => {
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);
    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
  },

  // إنشاء box-shadow مع لون معين
  createGlow: (color: string, intensity: number = 0.5) => 
    `0 0 10px ${colorHelpers.hexToRgba(color, intensity)}`,

  // إنشاء inset shadow
  createInsetShadow: (color: string = COLORS.COMMON.BLACK, intensity: number = 0.3) =>
    `inset 0 2px 4px ${colorHelpers.hexToRgba(color, intensity)}`
};

// 🎭 التأثيرات ثلاثية الأبعاد والانحناءات - مطابقة للصورة المرجعية
export const TRANSFORMS_3D = {
  // 🖼️ انحناء الإطار الخارجي
  OUTER_FRAME: {
    transform: 'perspective(1000px) rotateX(-2deg) rotateY(1deg)',
    transformOrigin: 'center center',
    transformStyle: 'preserve-3d' as const,
    boxShadow: '0 8px 16px rgba(0,0,0,0.4), inset 0 2px 4px rgba(255,255,255,0.1)'
  },

  // 🍎 انحناء أزرار الفواكه
  FRUIT_BUTTONS: {
    transform: 'perspective(300px) rotateX(10deg) rotateY(-2deg)',
    transformOrigin: 'center center',
    transformStyle: 'preserve-3d' as const,
    boxShadow: '0 6px 12px rgba(0,0,0,0.4), inset 0 2px 4px rgba(255,255,255,0.2)'
  },

  // 🔴 انحناء منطقة الرهان (طاولة مائلة)
  BETTING_TABLE: {
    transform: 'perspective(800px) rotateX(15deg)',
    transformOrigin: 'center top',
    transformStyle: 'preserve-3d' as const,
    boxShadow: 'inset 0 4px 8px rgba(0,0,0,0.4), inset 0 -4px 8px rgba(0,0,0,0.4)'
  },

  // 🟡 انحناء أزرار المبالغ (دائرية مرفوعة)
  AMOUNT_BUTTONS: {
    transform: 'perspective(200px) rotateX(5deg)',
    transformOrigin: 'center center',
    transformStyle: 'preserve-3d' as const,
    boxShadow: '0 4px 8px rgba(0,0,0,0.3), inset 0 2px 4px rgba(255,255,255,0.2)'
  },

  // 📦 انحناء المستطيلات السوداء
  BLACK_RECTANGLES: {
    transform: 'perspective(400px) rotateX(8deg)',
    transformOrigin: 'center center',
    transformStyle: 'preserve-3d' as const,
    boxShadow: '0 2px 4px rgba(0,0,0,0.6), inset 0 1px 2px rgba(255,255,255,0.1)'
  },

  // ✨ تأثيرات التفاعل
  HOVER_EFFECTS: {
    scale: 'scale(1.05)',
    glow: '0 0 15px rgba(255,215,0,0.6)',
    transition: 'all 0.3s ease'
  },

  ACTIVE_EFFECTS: {
    scale: 'scale(0.95)',
    transition: 'all 0.1s ease'
  }
};
