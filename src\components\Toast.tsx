import React, { useEffect, useState } from 'react';
import { COLORS } from '../theme/colors';

interface ToastProps {
  message: string;
  type?: 'success' | 'error' | 'warning' | 'info';
  duration?: number;
  isVisible: boolean;
  onClose: () => void;
}

/**
 * مكون Toast للإشعارات مع تصميم يتماشى مع ثيم اللعبة
 */
const Toast: React.FC<ToastProps> = ({ 
  message, 
  type = 'info', 
  duration = 3000, 
  isVisible, 
  onClose 
}) => {
  const [isAnimating, setIsAnimating] = useState(false);

  const typeStyles = {
    success: {
      background: COLORS.CREAM_BEIGE.GRADIENT,
      borderColor: COLORS.GOLD_ACCENT.BRIGHT,
      textColor: COLORS.COMMON.TEXT_DARK,
      icon: '✅'
    },
    error: {
      background: COLORS.RED_BETTING.GRADIENT,
      borderColor: COLORS.RED_BETTING.BORDER,
      textColor: COLORS.COMMON.WHITE,
      icon: '❌'
    },
    warning: {
      background: COLORS.ORANGE.GRADIENT,
      borderColor: COLORS.ORANGE.DARK,
      textColor: COLORS.COMMON.WHITE,
      icon: '⚠️'
    },
    info: {
      background: COLORS.PURPLE_INNER.GRADIENT,
      borderColor: COLORS.PURPLE_INNER.ACCENT,
      textColor: COLORS.COMMON.WHITE,
      icon: 'ℹ️'
    }
  };

  useEffect(() => {
    if (isVisible) {
      setIsAnimating(true);
      const timer = setTimeout(() => {
        setIsAnimating(false);
        setTimeout(onClose, 300); // انتظار انتهاء الـ animation
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [isVisible, duration, onClose]);

  if (!isVisible && !isAnimating) return null;

  const currentStyle = typeStyles[type];

  return (
    <div 
      className={`fixed top-4 left-1/2 transform -translate-x-1/2 z-50 transition-all duration-300 ${
        isAnimating ? 'translate-y-0 opacity-100' : '-translate-y-full opacity-0'
      }`}
    >
      <div 
        className="flex items-center gap-3 px-4 py-3 rounded-lg shadow-lg border-2 min-w-[250px] max-w-[400px]"
        style={{
          background: currentStyle.background,
          borderColor: currentStyle.borderColor,
          color: currentStyle.textColor,
          boxShadow: '0 4px 12px rgba(0,0,0,0.3), inset 0 1px 2px rgba(255,255,255,0.2)'
        }}
      >
        <span className="text-lg flex-shrink-0">
          {currentStyle.icon}
        </span>
        <span className="font-semibold text-sm flex-1">
          {message}
        </span>
        <button 
          onClick={onClose}
          className="text-lg font-bold hover:scale-110 transition-transform flex-shrink-0"
          style={{ color: currentStyle.textColor }}
        >
          ×
        </button>
      </div>
    </div>
  );
};

export default Toast;
