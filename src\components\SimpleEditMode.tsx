import React, { useState, useEffect } from 'react';

interface SimpleEditModeProps {
  isVisible: boolean;
  onToggle: () => void;
}

const SimpleEditMode: React.FC<SimpleEditModeProps> = ({
  isVisible,
  onToggle
}) => {
  const [isEditMode, setIsEditMode] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [draggedElements, setDraggedElements] = useState<Map<string, {left: string, top: string}>>(new Map());
  const [devToolsOpen, setDevToolsOpen] = useState(false);
  const [selectedElement, setSelectedElement] = useState<HTMLElement | null>(null);
  const [editableElements, setEditableElements] = useState<HTMLElement[]>([]);
  const [gameBoardSize, setGameBoardSize] = useState({ width: 380, height: 300 });
  const [isMinimized, setIsMinimized] = useState(true);
  const [isDragging, setIsDragging] = useState(false);
  const [toolPosition, setToolPosition] = useState({
    x: window.innerWidth - 80, // في الزاوية اليمنى
    y: 20
  });
  const [backgroundPaused, setBackgroundPaused] = useState(false);

  // دوال مساعدة
  const isEditableElement = (element: HTMLElement): boolean => {
    // تجاهل العناصر المخفية
    if (element.offsetWidth === 0 || element.offsetHeight === 0) return false;

    // تجاهل عناصر النظام
    const tagName = element.tagName.toLowerCase();
    if (['html', 'body', 'head', 'script', 'style'].includes(tagName)) return false;
    if (element.id === 'root') return false;

    const style = element.style;
    const hasPosition = style.position === 'absolute' || style.position === 'fixed';

    // البحث عن الأزرار والعناصر القابلة للتحرير
    if (hasPosition && element.offsetWidth < 200 && element.offsetHeight < 200) {
      return true;
    }

    // البحث عن مساحة اللعبة
    if (element.offsetWidth > 200 && element.offsetHeight > 150 && hasPosition) {
      return true;
    }

    return false;
  };

  const makeElementDraggable = (element: HTMLElement) => {
    // تحديد نوع العنصر
    const content = element.textContent?.trim() || '';
    const isGameBoard = element.offsetWidth > 200 && element.offsetHeight > 150;

    // إضافة إطار ملون حسب نوع العنصر
    if (isGameBoard) {
      element.style.border = '3px dashed rgba(255, 215, 0, 0.8)';
      element.style.background = element.style.background || 'rgba(255, 215, 0, 0.1)';
    } else {
      element.style.border = '2px dashed rgba(0, 255, 136, 0.8)';
    }
    element.style.cursor = 'grab';
    element.style.transition = 'none';
    element.style.pointerEvents = 'auto';

    // إضافة النقر للاختيار
    const handleClick = (e: MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();

      // إزالة التمييز من العنصر السابق
      if (selectedElement) {
        updateElementSelection(selectedElement, false);
      }

      setSelectedElement(element);
      updateElementSelection(element, true);

    };

    element.addEventListener('click', handleClick, {
      passive: false,
      capture: true
    });

    // حفظ دالة التنظيف
    (element as any).__dragCleanup = () => {
      element.removeEventListener('click', handleClick, true);
      element.removeEventListener('click', handleClick);
      element.style.border = '';
      element.style.cursor = '';
      element.style.background = '';
      element.style.boxShadow = '';
    };
  };

  const enableEditMode = () => {


    // إضافة طبقة حماية شفافة لمنع النقر على اللعبة
    const overlay = document.createElement('div');
    overlay.id = 'edit-mode-overlay';
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background: rgba(0, 255, 136, 0.05);
      z-index: 9999;
      pointer-events: none;
      border: 3px dashed rgba(0, 255, 136, 0.3);
    `;
    document.body.appendChild(overlay);

    // البحث عن العناصر القابلة للتحرير
    const allElements = document.querySelectorAll('*');
    const foundElements: HTMLElement[] = [];

    allElements.forEach((element: Element) => {
      const htmlElement = element as HTMLElement;

      // تحقق من أن العنصر قابل للتحرير
      if (isEditableElement(htmlElement)) {
        makeElementDraggable(htmlElement);
        foundElements.push(htmlElement);
      }
    });

    setEditableElements(foundElements);

  };

  const disableEditMode = () => {


    // إزالة طبقة الحماية
    const overlay = document.getElementById('edit-mode-overlay');
    if (overlay) {
      overlay.remove();
    }

    // تنظيف جميع العناصر
    editableElements.forEach(element => {
      if ((element as any).__dragCleanup) {
        (element as any).__dragCleanup();
      }
    });

    setEditableElements([]);
    setSelectedElement(null);
  };

  // تفعيل/إلغاء وضع التعديل
  useEffect(() => {
    if (isEditMode) {
      enableEditMode();
    } else {
      disableEditMode();
    }

    return () => {
      disableEditMode();
    };
  }, [isEditMode]);

  // نظام التحكم بالكيبورد
  useEffect(() => {
    if (!isEditMode) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (!selectedElement) return;

      const step = e.shiftKey ? 0.1 : 1; // خطوة أصغر مع Shift
      let moved = false;

      switch (e.key) {
        case 'ArrowUp':
          e.preventDefault();
          moveElementByKeyboard(selectedElement, 0, -step);
          moved = true;
          break;
        case 'ArrowDown':
          e.preventDefault();
          moveElementByKeyboard(selectedElement, 0, step);
          moved = true;
          break;
        case 'ArrowLeft':
          e.preventDefault();
          moveElementByKeyboard(selectedElement, -step, 0);
          moved = true;
          break;
        case 'ArrowRight':
          e.preventDefault();
          moveElementByKeyboard(selectedElement, step, 0);
          moved = true;
          break;
        case 'Tab':
          e.preventDefault();
          selectNextElement(e.shiftKey ? -1 : 1);
          break;
        case 'Escape':
          e.preventDefault();
          setSelectedElement(null);
          break;
      }

      if (moved) {
        
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isEditMode, selectedElement, editableElements]);

  const moveElementByKeyboard = (element: HTMLElement, deltaX: number, deltaY: number) => {
    const currentLeft = parseFloat(element.style.left) || 0;
    const currentTop = parseFloat(element.style.top) || 0;

    const newLeft = Math.max(0, Math.min(100, currentLeft + deltaX));
    const newTop = Math.max(0, Math.min(100, currentTop + deltaY));

    element.style.left = `${newLeft.toFixed(1)}%`;
    element.style.top = `${newTop.toFixed(1)}%`;

    // حفظ التغيير
    const elementId = getElementId(element);
    if (elementId) {
      setDraggedElements(prev => new Map(prev.set(elementId, {
        left: `${newLeft.toFixed(1)}%`,
        top: `${newTop.toFixed(1)}%`
      })));
      setHasChanges(true);
    }
  };

  const selectNextElement = (direction: number) => {
    if (editableElements.length === 0) return;

    const currentIndex = selectedElement ? editableElements.indexOf(selectedElement) : -1;
    let nextIndex = currentIndex + direction;

    if (nextIndex >= editableElements.length) nextIndex = 0;
    if (nextIndex < 0) nextIndex = editableElements.length - 1;

    // إزالة التمييز من العنصر السابق
    if (selectedElement) {
      updateElementSelection(selectedElement, false);
    }

    setSelectedElement(editableElements[nextIndex]);
    updateElementSelection(editableElements[nextIndex], true);
    
  };

  // إذا لم تكن الأداة مرئية، لا تعرض شيئاً
  if (!isVisible) {
    return (
      <button
        onClick={onToggle}
        style={{
          position: 'fixed',
          top: '20px',
          right: '20px',
          padding: '12px',
          background: 'linear-gradient(135deg, #00ff88, #00cc6a)',
          color: '#000',
          border: 'none',
          borderRadius: '8px',
          cursor: 'pointer',
          fontSize: '14px',
          fontWeight: 'bold',
          zIndex: 10000,
          boxShadow: '0 4px 12px rgba(0, 255, 136, 0.3)',
        }}
      >
        ✏️ وضع التعديل البسيط
      </button>
    );
  }

  // دوال سحب الأداة
  const handleToolDragStart = (e: React.MouseEvent) => {
    setIsDragging(true);
    const startX = e.clientX - toolPosition.x;
    const startY = e.clientY - toolPosition.y;

    const handleMouseMove = (e: MouseEvent) => {
      const newX = Math.max(0, Math.min(window.innerWidth - 300, e.clientX - startX));
      const newY = Math.max(0, Math.min(window.innerHeight - 200, e.clientY - startY));
      setToolPosition({ x: newX, y: newY });
    };

    const handleMouseUp = () => {
      setIsDragging(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };



  const saveChanges = () => {
    if (!hasChanges) {
      alert('❌ لا توجد تغييرات للحفظ');
      return;
    }

    // تطبيق التغييرات مباشرة على العناصر
    let appliedChanges = 0;

    draggedElements.forEach((position, elementId) => {
      // البحث عن العنصر في الصفحة وتطبيق الموقع الجديد
      const elements = editableElements.filter(el => getElementId(el) === elementId);

      elements.forEach(element => {
        element.style.left = position.left;
        element.style.top = position.top;
        element.style.position = 'absolute';
        appliedChanges++;
      });
    });

    // حفظ في التخزين المحلي للاستخدام المستقبلي
    const timestamp = new Date().toISOString();
    const saveData = {
      positions: Object.fromEntries(draggedElements),
      timestamp,
      elementsCount: draggedElements.size,
      deviceInfo: {
        width: window.innerWidth,
        height: window.innerHeight,
        userAgent: navigator.userAgent.substring(0, 100)
      }
    };

    localStorage.setItem('gameButtonPositions_current', JSON.stringify(saveData));

    alert(`✅ تم تطبيق التغييرات فوراً!
🎯 تم تحديث ${appliedChanges} عنصر
💾 تم حفظ المواقع الجديدة
🔄 التغييرات مطبقة الآن في اللعبة

لا حاجة لنسخ أو لصق أي كود!`);

    setHasChanges(false);
    setDraggedElements(new Map());

    // إزالة التمييز من العنصر المحدد
    if (selectedElement) {
      updateElementSelection(selectedElement, false);
      setSelectedElement(null);
    }
  };

  const refreshElements = () => {

    if (isEditMode) {
      disableEditMode();
      setTimeout(() => {
        enableEditMode();
      }, 100);
    }
  };

  const getElementId = (element: HTMLElement): string | null => {
    // محاولة تحديد نوع العنصر بناءً على خصائصه
    const style = element.style;
    const content = element.textContent?.trim() || '';

    // مساحة اللعبة (Game Board)
    if (element.offsetWidth > 200 && element.offsetHeight > 150) {
      const hasCanvas = element.querySelector('canvas');
      const hasGameClass = element.querySelector('[class*="game"]') || element.querySelector('[class*="board"]');
      const isGameZIndex = style.zIndex === '10';

      if (hasCanvas || hasGameClass || isGameZIndex) {
        return 'gameBoard';
      }
    }

    // أزرار المبالغ (دائرية)
    if (style.borderRadius === '50%' || style.borderRadius?.includes('50%')) {
      if (content.includes('5') || content.includes('5k')) return 'amount1';
      if (content.includes('3') || content.includes('3k')) return 'amount2';
      if (content.includes('1') || content.includes('1k')) return 'amount3';
      if (content.includes('500')) return 'amount4';
      if (content.includes('100')) return 'amount5';
    }

    // أزرار الفواكه
    const img = element.querySelector('img');
    if (img) {
      const src = img.src;
      if (src.includes('222.jpg')) return 'bar';
      if (src.includes('12.png')) return 'watermelon';
      if (src.includes('8.png')) return 'lemon';
      if (src.includes('6.png')) return 'banana';
      if (src.includes('3.png')) return 'apple';
    }

    // عروض الرصيد
    if (content.includes(',') && content.length > 3) {
      const rect = element.getBoundingClientRect();
      if (rect.left < window.innerWidth / 2) return 'balanceDisplay';
      else return 'totalBetDisplay';
    }

    // إذا لم نتمكن من التحديد، استخدم موقع العنصر كمعرف
    if (element.offsetWidth > 0 && element.offsetHeight > 0) {
      const rect = element.getBoundingClientRect();
      const leftPercent = Math.round((rect.left / window.innerWidth) * 100);
      const topPercent = Math.round((rect.top / window.innerHeight) * 100);
      return `element-${leftPercent}-${topPercent}`;
    }

    return null;
  };

  const updateElementSelection = (element: HTMLElement, isSelected: boolean) => {
    if (isSelected) {
      element.style.boxShadow = '0 0 15px rgba(255, 255, 0, 0.8)';
      element.style.borderColor = 'rgba(255, 255, 0, 1)';
      element.style.borderWidth = '3px';
    } else {
      element.style.boxShadow = '';
      // استعادة اللون الأصلي حسب نوع العنصر
      const content = element.textContent?.trim() || '';
      const isGameBoard = element.offsetWidth > 200 && element.offsetHeight > 150;

      if (isGameBoard) {
        element.style.borderColor = 'rgba(255, 215, 0, 0.8)';
      } else {
        element.style.borderColor = 'rgba(0, 255, 136, 0.8)';
      }
    }
  };

  const resetToOriginal = () => {
    if (confirm('هل تريد إعادة جميع الأزرار لمواقعها الأصلية؟')) {
      localStorage.removeItem('gameButtonPositions_current');
      window.location.reload();
    }
  };

  const cancelChanges = () => {
    if (hasChanges && !confirm('هل تريد إلغاء جميع التغييرات؟')) {
      return;
    }
    window.location.reload();
  };

  const toggleBackgroundChange = () => {
    setBackgroundPaused(!backgroundPaused);

    // إيقاف/تشغيل تغيير الخلفية عبر إضافة/إزالة كلاس CSS
    const body = document.body;
    if (!backgroundPaused) {
      body.style.setProperty('--background-animation', 'paused');
      body.classList.add('background-paused');
      
    } else {
      body.style.removeProperty('--background-animation');
      body.classList.remove('background-paused');

    }
  };

  return (
    <div style={{
      position: 'fixed',
      left: `${toolPosition.x}px`,
      top: `${toolPosition.y}px`,
      background: 'rgba(0, 0, 0, 0.95)',
      color: '#fff',
      borderRadius: '12px',
      zIndex: 10001,
      fontSize: '12px',
      fontFamily: 'Arial, sans-serif',
      border: '2px solid rgba(0, 255, 136, 0.5)',
      boxShadow: '0 8px 32px rgba(0, 0, 0, 0.5)',
      backdropFilter: 'blur(15px)',
      userSelect: 'none',
      transition: 'all 0.3s ease',
      width: isMinimized ? '60px' : '320px',
      height: isMinimized ? '60px' : 'auto',
      overflow: 'hidden',
      pointerEvents: 'auto', // الأداة نفسها قابلة للنقر
    }}>
      {/* شريط العنوان القابل للسحب */}
      <div 
        style={{ 
          background: 'linear-gradient(135deg, #00ff88, #00cc6a)',
          color: '#000',
          padding: isMinimized ? '18px' : '12px',
          cursor: isDragging ? 'grabbing' : 'grab',
          textAlign: 'center',
          fontWeight: 'bold',
          fontSize: isMinimized ? '20px' : '14px',
          position: 'relative',
        }}
        onMouseDown={handleToolDragStart}
        onClick={() => !isDragging && setIsMinimized(!isMinimized)}
      >
        {isMinimized ? '⚙️' : '🛠️ أدوات التحكم'}
        {!isMinimized && (
          <>
            <button
              onClick={(e) => {
                e.stopPropagation();
                setIsMinimized(true);
              }}
              style={{
                position: 'absolute',
                right: '8px',
                top: '50%',
                transform: 'translateY(-50%)',
                background: 'rgba(0,0,0,0.2)',
                border: 'none',
                color: '#000',
                cursor: 'pointer',
                borderRadius: '4px',
                padding: '4px 8px',
                fontSize: '12px',
              }}
            >
              ➖
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                onToggle();
              }}
              style={{
                position: 'absolute',
                right: '40px',
                top: '50%',
                transform: 'translateY(-50%)',
                background: 'rgba(255,0,0,0.3)',
                border: 'none',
                color: '#000',
                cursor: 'pointer',
                borderRadius: '4px',
                padding: '4px 8px',
                fontSize: '12px',
              }}
            >
              ✕
            </button>
          </>
        )}
      </div>

      {/* المحتوى الرئيسي */}
      {!isMinimized && (
        <div style={{ padding: '15px' }}>
          {/* أزرار التحكم السريع */}
          <div style={{ marginBottom: '15px' }}>
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '8px', marginBottom: '10px' }}>
              <button
                onClick={() => setIsEditMode(!isEditMode)}
                style={{
                  padding: '8px',
                  background: isEditMode ? 'linear-gradient(135deg, #ff4444, #cc3333)' : 'linear-gradient(135deg, #4caf50, #45a049)',
                  color: '#fff',
                  border: 'none',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  fontSize: '11px',
                  fontWeight: 'bold',
                }}
              >
                {isEditMode ? '🔒 إيقاف' : '✏️ تفعيل'}
              </button>

              <button
                onClick={refreshElements}
                style={{
                  padding: '8px',
                  background: 'linear-gradient(135deg, #2196f3, #1976d2)',
                  color: '#fff',
                  border: 'none',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  fontSize: '11px',
                  fontWeight: 'bold',
                }}
              >
                🔄 تحديث
              </button>
            </div>

            {/* زر إيقاف تغيير الخلفية */}
            <button
              onClick={toggleBackgroundChange}
              style={{
                width: '100%',
                padding: '8px',
                background: backgroundPaused ? 'linear-gradient(135deg, #ff9800, #f57c00)' : 'linear-gradient(135deg, #9c27b0, #7b1fa2)',
                color: '#fff',
                border: 'none',
                borderRadius: '6px',
                cursor: 'pointer',
                fontSize: '11px',
                fontWeight: 'bold',
                marginBottom: '10px',
              }}
            >
              {backgroundPaused ? '▶️ تشغيل الخلفية' : '⏸️ إيقاف الخلفية'}
            </button>
            
            {hasChanges && (
              <button
                onClick={saveChanges}
                style={{
                  width: '100%',
                  padding: '10px',
                  background: 'linear-gradient(135deg, #4caf50, #45a049)',
                  color: '#fff',
                  border: 'none',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  fontSize: '12px',
                  fontWeight: 'bold',
                  marginBottom: '8px',
                  boxShadow: '0 4px 12px rgba(76, 175, 80, 0.3)',
                }}
              >
                ✅ تطبيق التغييرات
              </button>
            )}
          </div>

          {/* معلومات الحالة */}
          <div style={{ 
            background: isEditMode ? 'rgba(0, 255, 136, 0.1)' : 'rgba(255, 255, 255, 0.05)', 
            borderRadius: '6px',
            padding: '10px',
            marginBottom: '15px',
            border: `1px solid ${isEditMode ? 'rgba(0, 255, 136, 0.3)' : 'rgba(255, 255, 255, 0.1)'}`
          }}>
            <div style={{ fontSize: '11px', fontWeight: 'bold', marginBottom: '4px' }}>
              {isEditMode ? '🟢 وضع التعديل مفعل' : '⚪ وضع التعديل معطل'}
            </div>
            {editableElements.length > 0 && (
              <div style={{ fontSize: '10px', color: '#aaa' }}>
                📊 {editableElements.length} عنصر قابل للتحرير
              </div>
            )}
            {selectedElement && (
              <div style={{ fontSize: '10px', color: '#ffff00', marginTop: '2px' }}>
                🎯 محدد: {getElementId(selectedElement) || 'عنصر'}
              </div>
            )}
            {devToolsOpen && (
              <div style={{ fontSize: '10px', color: '#ffaa00', marginTop: '2px' }}>
                ⚠️ أدوات المطور مفتوحة
              </div>
            )}
            {backgroundPaused && (
              <div style={{ fontSize: '10px', color: '#ff9800', marginTop: '2px' }}>
                ⏸️ تغيير الخلفية متوقف
              </div>
            )}
          </div>

          {/* أدوات متقدمة */}
          <details style={{ marginBottom: '15px' }}>
            <summary style={{ 
              cursor: 'pointer', 
              fontSize: '11px', 
              fontWeight: 'bold', 
              color: '#00ff88',
              marginBottom: '8px',
              userSelect: 'none'
            }}>
              🔧 أدوات متقدمة
            </summary>
            <div style={{ paddingLeft: '10px', marginTop: '8px' }}>
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '6px', marginBottom: '8px' }}>
                <button
                  onClick={cancelChanges}
                  style={{
                    padding: '6px',
                    background: 'linear-gradient(135deg, #ff9800, #f57c00)',
                    color: '#fff',
                    border: 'none',
                    borderRadius: '4px',
                    cursor: 'pointer',
                    fontSize: '10px',
                  }}
                >
                  🔄 إعادة تحميل
                </button>
                
                <button
                  onClick={resetToOriginal}
                  style={{
                    padding: '6px',
                    background: 'linear-gradient(135deg, #f44336, #d32f2f)',
                    color: '#fff',
                    border: 'none',
                    borderRadius: '4px',
                    cursor: 'pointer',
                    fontSize: '10px',
                  }}
                >
                  🗑️ حذف الكل
                </button>
              </div>
              
              <button
                onClick={() => {
                  const instructions = '⌨️ تعليمات سريعة:\n\n' +
                    '• انقر على أي عنصر لاختياره\n' +
                    '• الأسهم للتحريك ↑ ↓ ← →\n' +
                    '• + و - لتغيير حجم مساحة اللعبة\n' +
                    '• Tab للتنقل بين العناصر\n' +
                    '• Escape لإلغاء الاختيار';
                  alert(instructions);
                }}
                style={{
                  width: '100%',
                  padding: '6px',
                  background: 'linear-gradient(135deg, #9c27b0, #7b1fa2)',
                  color: '#fff',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  fontSize: '10px',
                }}
              >
                💡 تعليمات سريعة
              </button>
            </div>
          </details>
        </div>
      )}
    </div>
  );
};

export default SimpleEditMode;
