// نظام تحسين SEO للعبة
export interface SEOConfig {
  title: string;
  description: string;
  keywords: string[];
  ogImage?: string;
  canonicalUrl?: string;
  structuredData?: any;
}

// إعدادات SEO الافتراضية
const DEFAULT_SEO: SEOConfig = {
  title: 'Lucky Ocean Game - لعبة المحيط المحظوظ | أفضل لعبة رهان بالفواكه',
  description: 'استمتع بلعبة Lucky Ocean المثيرة! لعبة رهان بالفواكه مع نظام إضاءة دوارة وتصميم ثلاثي الأبعاد. العب الآن واربح جوائز كبيرة!',
  keywords: [
    'لعبة رهان',
    'لعبة فواكه',
    'Lucky Ocean',
    'ماكينة قمار',
    'لعبة أونلاين',
    'رهان بالفواكه',
    'لعبة حظ',
    'كازينو أونلاين',
    'لعبة عربية',
    'ربح المال'
  ],
  ogImage: '/images/og-image.jpg',
  canonicalUrl: 'https://lucky-ocean-game.com'
};

// تحديث meta tags
export const updateSEOTags = (config: Partial<SEOConfig> = {}) => {
  const seoConfig = { ...DEFAULT_SEO, ...config };
  
  // تحديث العنوان
  document.title = seoConfig.title;
  
  // تحديث أو إنشاء meta tags
  updateMetaTag('description', seoConfig.description);
  updateMetaTag('keywords', seoConfig.keywords.join(', '));
  
  // Open Graph tags
  updateMetaTag('og:title', seoConfig.title, 'property');
  updateMetaTag('og:description', seoConfig.description, 'property');
  updateMetaTag('og:type', 'website', 'property');
  updateMetaTag('og:site_name', 'Lucky Ocean Game', 'property');
  updateMetaTag('og:locale', 'ar_SA', 'property');
  
  if (seoConfig.ogImage) {
    updateMetaTag('og:image', seoConfig.ogImage, 'property');
    updateMetaTag('og:image:width', '1200', 'property');
    updateMetaTag('og:image:height', '630', 'property');
    updateMetaTag('og:image:type', 'image/jpeg', 'property');
  }
  
  if (seoConfig.canonicalUrl) {
    updateMetaTag('og:url', seoConfig.canonicalUrl, 'property');
    updateCanonicalLink(seoConfig.canonicalUrl);
  }
  
  // Twitter Card tags
  updateMetaTag('twitter:card', 'summary_large_image');
  updateMetaTag('twitter:title', seoConfig.title);
  updateMetaTag('twitter:description', seoConfig.description);
  
  if (seoConfig.ogImage) {
    updateMetaTag('twitter:image', seoConfig.ogImage);
  }
  
  // إضافة structured data
  if (seoConfig.structuredData) {
    updateStructuredData(seoConfig.structuredData);
  } else {
    updateStructuredData(createDefaultStructuredData());
  }
  
  console.log('✅ تم تحديث SEO tags بنجاح');
};

// تحديث meta tag واحد
const updateMetaTag = (name: string, content: string, attribute: 'name' | 'property' = 'name') => {
  let element = document.querySelector(`meta[${attribute}="${name}"]`) as HTMLMetaElement;
  
  if (!element) {
    element = document.createElement('meta');
    element.setAttribute(attribute, name);
    document.head.appendChild(element);
  }
  
  element.content = content;
};

// تحديث canonical link
const updateCanonicalLink = (url: string) => {
  let element = document.querySelector('link[rel="canonical"]') as HTMLLinkElement;
  
  if (!element) {
    element = document.createElement('link');
    element.rel = 'canonical';
    document.head.appendChild(element);
  }
  
  element.href = url;
};

// تحديث structured data
const updateStructuredData = (data: any) => {
  // إزالة structured data القديم
  const oldScript = document.querySelector('script[type="application/ld+json"]');
  if (oldScript) {
    oldScript.remove();
  }
  
  // إضافة structured data جديد
  const script = document.createElement('script');
  script.type = 'application/ld+json';
  script.textContent = JSON.stringify(data);
  document.head.appendChild(script);
};

// إنشاء structured data افتراضي
const createDefaultStructuredData = () => {
  return {
    "@context": "https://schema.org",
    "@type": "Game",
    "name": "Lucky Ocean Game",
    "alternateName": "لعبة المحيط المحظوظ",
    "description": "لعبة رهان بالفواكه مع نظام إضاءة دوارة وتصميم ثلاثي الأبعاد",
    "genre": "Casino Game",
    "gamePlatform": "Web Browser",
    "operatingSystem": "Any",
    "applicationCategory": "Game",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD",
      "availability": "https://schema.org/InStock"
    },
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.8",
      "ratingCount": "1250",
      "bestRating": "5",
      "worstRating": "1"
    },
    "author": {
      "@type": "Organization",
      "name": "Lucky Ocean Games"
    },
    "datePublished": "2024-01-01",
    "dateModified": new Date().toISOString().split('T')[0],
    "inLanguage": "ar",
    "isAccessibleForFree": true,
    "keywords": DEFAULT_SEO.keywords.join(', '),
    "screenshot": "/images/screenshots/game-screenshot.jpg",
    "video": {
      "@type": "VideoObject",
      "name": "Lucky Ocean Game Gameplay",
      "description": "شاهد كيفية لعب Lucky Ocean Game",
      "thumbnailUrl": "/images/video-thumbnail.jpg",
      "uploadDate": "2024-01-01",
      "duration": "PT2M30S"
    }
  };
};

// تحسين الأداء لمحركات البحث
export const optimizeForSearch = () => {
  // إضافة preconnect للموارد الخارجية
  addPreconnectLink('https://fonts.googleapis.com');
  addPreconnectLink('https://fonts.gstatic.com');
  
  // إضافة dns-prefetch
  addDNSPrefetchLink('https://www.google-analytics.com');
  
  // تحسين الصور
  optimizeImages();
  
  // إضافة sitemap
  addSitemapLink('/sitemap.xml');
  
  // إضافة robots meta
  updateMetaTag('robots', 'index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1');
  
  console.log('🔍 تم تحسين الموقع لمحركات البحث');
};

// إضافة preconnect link
const addPreconnectLink = (href: string) => {
  if (!document.querySelector(`link[rel="preconnect"][href="${href}"]`)) {
    const link = document.createElement('link');
    link.rel = 'preconnect';
    link.href = href;
    document.head.appendChild(link);
  }
};

// إضافة dns-prefetch link
const addDNSPrefetchLink = (href: string) => {
  if (!document.querySelector(`link[rel="dns-prefetch"][href="${href}"]`)) {
    const link = document.createElement('link');
    link.rel = 'dns-prefetch';
    link.href = href;
    document.head.appendChild(link);
  }
};

// إضافة sitemap link
const addSitemapLink = (href: string) => {
  if (!document.querySelector(`link[rel="sitemap"][href="${href}"]`)) {
    const link = document.createElement('link');
    link.rel = 'sitemap';
    link.type = 'application/xml';
    link.href = href;
    document.head.appendChild(link);
  }
};

// تحسين الصور للبحث
const optimizeImages = () => {
  const images = document.querySelectorAll('img');
  images.forEach(img => {
    // إضافة alt text إذا لم يكن موجود
    if (!img.alt) {
      img.alt = 'Lucky Ocean Game - لعبة المحيط المحظوظ';
    }
    
    // إضافة loading="lazy" للصور غير الحرجة
    if (!img.loading) {
      img.loading = 'lazy';
    }
    
    // إضافة decoding="async"
    if (!img.decoding) {
      img.decoding = 'async';
    }
  });
};

// تتبع الأحداث لـ SEO
export const trackSEOEvents = () => {
  // تتبع وقت البقاء في الصفحة
  let startTime = Date.now();
  
  window.addEventListener('beforeunload', () => {
    const timeSpent = Date.now() - startTime;
    console.log(`📊 وقت البقاء في الصفحة: ${Math.round(timeSpent / 1000)} ثانية`);
  });
  
  // تتبع التفاعل مع اللعبة
  document.addEventListener('click', (event) => {
    const target = event.target as HTMLElement;
    if (target.closest('.game-board') || target.closest('.betting-controls')) {
      console.log('🎮 تفاعل مع اللعبة');
    }
  });
  
  // تتبع الأخطاء
  window.addEventListener('error', (event) => {
    console.log('❌ خطأ في الصفحة:', event.message);
  });
};

// إنشاء sitemap ديناميكي
export const generateSitemap = () => {
  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>https://lucky-ocean-game.com/</loc>
    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
    <changefreq>daily</changefreq>
    <priority>1.0</priority>
  </url>
  <url>
    <loc>https://lucky-ocean-game.com/game</loc>
    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
    <changefreq>hourly</changefreq>
    <priority>0.9</priority>
  </url>
</urlset>`;
  
  return sitemap;
};

// تهيئة SEO عند تحميل الصفحة
export const initializeSEO = () => {
  updateSEOTags();
  optimizeForSearch();
  trackSEOEvents();
  
  console.log('🚀 تم تهيئة نظام SEO بنجاح');
};
