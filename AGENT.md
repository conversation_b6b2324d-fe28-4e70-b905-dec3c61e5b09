# Lucky Ocean Game - Agent Guide

## Build & Development Commands
- `npm run dev` - Start development server
- `npm run build` - Build for production  
- `npm run lint` - Run ESLint linting
- `npm run preview` - Preview production build
- No test command configured

## Architecture
React 18 + TypeScript + Vite gambling game with:
- **Game Loop**: 24/7 system (30s betting → 10s light animation → 5s results)
- **Components**: `src/components/` (GameBoard, BettingControls, ResultDisplay, etc)
- **State**: Local state management, no Redux/Zustand
- **Styling**: Tailwind CSS + centralized color system in `src/theme/colors.ts`
- **Path Aliases**: `@/` for src, `@/components`, `@/theme`, `@/utils`, `@/types`, `@/constants`

## Code Style
- **TypeScript**: Strict typing, interfaces in `src/types/game.ts`
- **Colors**: Use centralized `COLORS` object from `src/theme/colors.ts` - never hardcode hex values
- **Components**: Functional components with hooks, kebab-case for files
- **Imports**: Use path aliases (`@/components`, `@/theme`, etc)
- **3D Effects**: Use `TRANSFORMS_3D` from colors.ts for consistent perspective/rotation
- **Naming**: camelCase for variables/functions, PascalCase for components
- **No Comments**: Avoid code comments unless complex logic requires explanation
