import React, { useState, useEffect } from 'react';
import { BUTTON_POSITIONS } from '../utils/buttonPositions';

interface PositionTesterProps {
  isVisible: boolean;
  onToggle: () => void;
}

const PositionTester: React.FC<PositionTesterProps> = ({
  isVisible,
  onToggle
}) => {
  const [currentDevice, setCurrentDevice] = useState<any>(null);
  const [showLabels, setShowLabels] = useState(true);
  const [testMode, setTestMode] = useState<'symbols' | 'amounts' | 'all'>('all');

  useEffect(() => {
    const updateDevice = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      
      let deviceConfig = BUTTON_POSITIONS.find(d => 
        Math.abs(d.device.width - width) < 50 && Math.abs(d.device.height - height) < 50
      );
      
      if (!deviceConfig) {
        if (width <= 768) {
          deviceConfig = BUTTON_POSITIONS.find(d => d.device.width <= 768) || BUTTON_POSITIONS[0];
        } else {
          deviceConfig = BUTTON_POSITIONS.find(d => d.device.width > 768) || BUTTON_POSITIONS[1] || BUTTON_POSITIONS[0];
        }
      }
      
      setCurrentDevice(deviceConfig);
    };

    updateDevice();
    window.addEventListener('resize', updateDevice);
    return () => window.removeEventListener('resize', updateDevice);
  }, []);

  if (!isVisible) {
    return (
      <button
        onClick={onToggle}
        style={{
          position: 'fixed',
          top: '170px',
          right: '10px',
          zIndex: 9999,
          background: 'rgba(128, 0, 128, 0.8)',
          color: '#fff',
          border: '1px solid #800080',
          borderRadius: '4px',
          padding: '8px 12px',
          cursor: 'pointer',
          fontSize: '12px',
        }}
      >
        🧪 Position Tester
      </button>
    );
  }

  if (!currentDevice) return null;

  return (
    <>
      {/* أزرار الفواكه للاختبار */}
      {(testMode === 'symbols' || testMode === 'all') && 
        Object.entries(currentDevice.symbolButtons).map(([key, pos]: [string, any]) => (
          <div
            key={`test-symbol-${key}`}
            style={{
              position: 'absolute',
              left: pos.left,
              top: pos.top,
              width: '60px',
              height: '60px',
              background: 'rgba(0, 255, 136, 0.8)',
              border: '3px solid #00ff88',
              borderRadius: '12px',
              transform: 'translate(-50%, -50%)',
              zIndex: 9997,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '10px',
              fontWeight: 'bold',
              color: '#000',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'translate(-50%, -50%) scale(1.1)';
              e.currentTarget.style.boxShadow = '0 0 20px rgba(0, 255, 136, 0.8)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'translate(-50%, -50%) scale(1)';
              e.currentTarget.style.boxShadow = 'none';
            }}
            title={`${pos.name} - ${pos.left}, ${pos.top}`}
          >
            {showLabels ? key.substring(0, 3).toUpperCase() : '🍎'}
          </div>
        ))
      }

      {/* أزرار المبالغ للاختبار */}
      {(testMode === 'amounts' || testMode === 'all') && 
        Object.entries(currentDevice.amountButtons).map(([key, pos]: [string, any]) => (
          <div
            key={`test-amount-${key}`}
            style={{
              position: 'absolute',
              left: pos.left,
              top: pos.top,
              width: '40px',
              height: '40px',
              background: 'rgba(255, 215, 0, 0.8)',
              border: '3px solid #FFD700',
              borderRadius: '50%',
              transform: 'translate(-50%, -50%)',
              zIndex: 9997,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '10px',
              fontWeight: 'bold',
              color: '#000',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'translate(-50%, -50%) scale(1.1)';
              e.currentTarget.style.boxShadow = '0 0 20px rgba(255, 215, 0, 0.8)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'translate(-50%, -50%) scale(1)';
              e.currentTarget.style.boxShadow = 'none';
            }}
            title={`${pos.name} - ${pos.value} - ${pos.left}, ${pos.top}`}
          >
            {showLabels ? (pos.value >= 1000 ? `${pos.value/1000}k` : pos.value) : '💰'}
          </div>
        ))
      }

      {/* مربعات عرض الرهانات للاختبار */}
      {(testMode === 'all') && 
        Object.entries(currentDevice.betRectangles).map(([key, pos]: [string, any]) => (
          <div
            key={`test-bet-${key}`}
            style={{
              position: 'absolute',
              left: pos.left,
              top: pos.top,
              padding: '4px 8px',
              background: 'rgba(255, 0, 0, 0.8)',
              border: '2px solid #ff0000',
              borderRadius: '6px',
              transform: 'translate(-50%, -50%)',
              zIndex: 9997,
              fontSize: '10px',
              fontWeight: 'bold',
              color: '#fff',
              minWidth: '30px',
              textAlign: 'center',
              cursor: 'pointer',
            }}
            title={`Bet ${pos.symbol} - ${pos.left}, ${pos.top}`}
          >
            {showLabels ? '1.5k' : '💸'}
          </div>
        ))
      }

      {/* عروض الرصيد للاختبار */}
      {(testMode === 'all') && 
        Object.entries(currentDevice.topDisplays).map(([key, pos]: [string, any]) => (
          <div
            key={`test-display-${key}`}
            style={{
              position: 'absolute',
              left: pos.left,
              top: pos.top,
              padding: '8px 12px',
              background: 'rgba(0, 0, 255, 0.8)',
              border: '2px solid #0000ff',
              borderRadius: '8px',
              transform: 'translate(-50%, 0)',
              zIndex: 9997,
              fontSize: '12px',
              fontWeight: 'bold',
              color: '#fff',
              cursor: 'pointer',
            }}
            title={`${pos.name} - ${pos.left}, ${pos.top}`}
          >
            {showLabels ? (key === 'balanceDisplay' ? '1,000,000' : '15,000') : '💰'}
          </div>
        ))
      }

      {/* لوحة التحكم */}
      <div style={{
        position: 'fixed',
        bottom: '20px',
        right: '20px',
        width: '280px',
        background: 'rgba(0, 0, 0, 0.9)',
        border: '1px solid #333',
        borderRadius: '8px',
        padding: '16px',
        zIndex: 9999,
        color: '#fff',
        fontSize: '12px',
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '16px' }}>
          <h3 style={{ margin: 0, fontSize: '14px' }}>Position Tester</h3>
          <button onClick={onToggle} style={{ background: 'none', border: 'none', color: '#ff4444', cursor: 'pointer', fontSize: '16px' }}>✕</button>
        </div>

        <div style={{ marginBottom: '16px' }}>
          <strong>الجهاز:</strong> {currentDevice.device.name}<br/>
          <strong>الأبعاد:</strong> {window.innerWidth}×{window.innerHeight}
        </div>

        <div style={{ marginBottom: '16px' }}>
          <strong>وضع الاختبار:</strong>
          <select 
            value={testMode} 
            onChange={(e) => setTestMode(e.target.value as any)}
            style={{ 
              width: '100%', 
              padding: '4px', 
              marginTop: '4px',
              background: '#333',
              color: '#fff',
              border: '1px solid #666',
              borderRadius: '4px'
            }}
          >
            <option value="all">جميع العناصر</option>
            <option value="symbols">أزرار الفواكه فقط</option>
            <option value="amounts">أزرار المبالغ فقط</option>
          </select>
        </div>

        <div style={{ marginBottom: '16px' }}>
          <label style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <input
              type="checkbox"
              checked={showLabels}
              onChange={(e) => setShowLabels(e.target.checked)}
            />
            إظهار النصوص
          </label>
        </div>

        <div style={{ marginBottom: '16px', fontSize: '10px' }}>
          <div><span style={{color: '#00ff88'}}>🟢</span> أزرار الفواكه</div>
          <div><span style={{color: '#FFD700'}}>🟡</span> أزرار المبالغ</div>
          <div><span style={{color: '#ff0000'}}>🔴</span> مربعات الرهانات</div>
          <div><span style={{color: '#0000ff'}}>🔵</span> عروض المعلومات</div>
        </div>

        <button
          onClick={() => {
      
            alert('تم طباعة التكوين في وحدة التحكم!');
          }}
          style={{
            width: '100%',
            padding: '8px',
            background: '#4caf50',
            color: '#fff',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '12px',
          }}
        >
          📊 طباعة التكوين
        </button>
      </div>
    </>
  );
};

export default PositionTester;
