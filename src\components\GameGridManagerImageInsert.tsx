import React, { useRef } from 'react';

export default function GameGridManagerImageInsert({ onImageSelect }: { onImageSelect?: (file: File) => void }) {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      if (onImageSelect) onImageSelect(e.target.files[0]);
    }
  };

  return (
    <div style={{ margin: '20px 0', padding: '12px', border: '2px dashed #FFD700', borderRadius: 8, background: '#222', color: '#FFD700', textAlign: 'center' }}>
      <label style={{ cursor: 'pointer', fontWeight: 'bold' }}>
        إدراج صورة (game-grid-manager):
        <input
          type="file"
          accept="image/*"
          ref={fileInputRef}
          onChange={handleFileChange}
          style={{ display: 'none' }}
        />
        <span
          style={{
            display: 'inline-block',
            margin: '0 10px',
            padding: '6px 18px',
            background: '#FFD700',
            color: '#222',
            borderRadius: 6,
            fontWeight: 'bold',
            fontSize: 15,
          }}
          onClick={() => fileInputRef.current?.click()}
        >
          اختر صورة
        </span>
      </label>
    </div>
  );
}
