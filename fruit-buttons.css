/* تصميم أزرار الفواكه لتغطية المربعات في الخلفية */

.slot-container {
    position: relative;
    width: 100%;
    height: 100vh;
    background: linear-gradient(135deg, #8b4513, #a0522d, #cd853f);
    background-size: cover;
    background-position: center;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* أزرار الفواكه في الجزء السفلي */
.fruit-buttons-container {
    position: absolute;
    bottom: 100px; /* تحريك للأسفل أكثر */
    right: 100px;
    display: flex;
    gap: 15px;
    z-index: 10;
}

.fruit-button {
    width: 60px;
    height: 60px;
    border: 2px solid #ffd700;
    border-radius: 12px;
    background: linear-gradient(145deg, #ffd700, #ffb347, #daa520);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    position: relative;
    box-shadow: 0 6px 15px rgba(255, 215, 0, 0.4), inset 0 2px 4px rgba(255, 255, 255, 0.3);
}

/* إضاءة ذهبية لفترة الرهان */
.fruit-button.betting-glow {
    animation: goldenGlow 2s ease-in-out infinite alternate;
    box-shadow: 
        0 0 20px rgba(255, 215, 0, 0.8),
        0 0 40px rgba(255, 215, 0, 0.6),
        0 0 60px rgba(255, 215, 0, 0.4),
        inset 0 2px 4px rgba(255, 255, 255, 0.3);
}

@keyframes goldenGlow {
    0% {
        box-shadow: 
            0 0 20px rgba(255, 215, 0, 0.8),
            0 0 40px rgba(255, 215, 0, 0.6),
            0 0 60px rgba(255, 215, 0, 0.4),
            inset 0 2px 4px rgba(255, 255, 255, 0.3);
        border-color: #ffd700;
    }
    100% {
        box-shadow: 
            0 0 30px rgba(255, 215, 0, 1),
            0 0 60px rgba(255, 215, 0, 0.8),
            0 0 90px rgba(255, 215, 0, 0.6),
            inset 0 2px 6px rgba(255, 255, 255, 0.5);
        border-color: #ffed4e;
    }
}

.fruit-button:hover {
    transform: scale(1.1);
    background: linear-gradient(145deg, #ffed4e, #ffd700, #b8860b);
    box-shadow: 0 8px 20px rgba(255, 215, 0, 0.6), inset 0 2px 6px rgba(255, 255, 255, 0.4);
    border-color: #ffed4e;
}

.fruit-button:active {
    transform: scale(0.95);
}

/* أزرار الفواكه المحددة مع خلفية ذهبية */
.apple-btn {
    background: linear-gradient(145deg, #ffd700, #ff6b6b, #daa520);
    border: 2px solid #ff6b6b;
}

.banana-btn {
    background: linear-gradient(145deg, #ffd700, #ffd93d, #daa520);
    border: 2px solid #ffd93d;
}

.lemon-btn {
    background: linear-gradient(145deg, #ffd700, #fff700, #daa520);
    border: 2px solid #fff700;
}

.watermelon-btn {
    background: linear-gradient(145deg, #ffd700, #ff6b9d, #daa520);
    border: 2px solid #ff6b9d;
}

.bar-btn {
    background: linear-gradient(145deg, #ffd700, #8b4513, #daa520);
    color: #8b4513;
    font-weight: bold;
    font-size: 12px;
    border: 2px solid #8b4513;
    text-shadow: 1px 1px 2px rgba(255, 215, 0, 0.8);
}

/* مؤشر المضاعف */
.multiplier {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #ffd700;
    color: #8b4513;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 10px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid #8b4513;
}

/* تأثيرات الانتقال */
.fruit-button.selected {
    border: 3px solid #ffed4e;
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.8), inset 0 0 10px rgba(255, 255, 255, 0.5);
    background: linear-gradient(145deg, #ffed4e, #ffd700, #b8860b);
    transform: scale(1.05);
}

/* أزرار الرهان الرقمية */
.bet-buttons-container {
    position: absolute;
    bottom: 200px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 15px;
    z-index: 10;
}

.bet-button {
    width: 80px;
    height: 50px;
    border: 2px solid #8b4513;
    border-radius: 25px;
    background: linear-gradient(145deg, #ffd700, #daa520, #b8860b);
    color: #8b4513;
    font-weight: bold;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 6px 12px rgba(255, 215, 0, 0.4), inset 0 2px 4px rgba(255, 255, 255, 0.3);
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.5);
}

.bet-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(255, 215, 0, 0.6), inset 0 2px 6px rgba(255, 255, 255, 0.4);
    background: linear-gradient(145deg, #ffed4e, #ffd700, #daa520);
    border-color: #daa520;
}

.bet-button.active {
    background: linear-gradient(145deg, #ffed4e, #ffd700, #b8860b);
    color: #8b4513;
    border: 3px solid #8b4513;
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.8), inset 0 0 8px rgba(255, 255, 255, 0.5);
    transform: scale(1.05);
}

/* الأزرار السوداء الصغيرة تحت أزرار الرهان */
.small-buttons-container {
    position: absolute;
    bottom: 100px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 15px;
    z-index: 10;
}

.small-button {
    width: 60px;
    height: 20px;
    background: linear-gradient(145deg, #2c2c2c, #1a1a1a);
    border: 2px solid #ffd700;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(255, 215, 0, 0.3);
}

.small-button:hover {
    background: linear-gradient(145deg, #3c3c3c, #2a2a2a);
    border-color: #ffed4e;
    box-shadow: 0 3px 6px rgba(255, 215, 0, 0.5);
}

.small-button.active {
    background: linear-gradient(145deg, #ffd700, #daa520);
    border-color: #8b4513;
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.7), inset 0 1px 3px rgba(255, 255, 255, 0.4);
}
