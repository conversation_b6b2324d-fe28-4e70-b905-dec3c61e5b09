// JavaScript لتفعيل أزرار الفواكه والرهان

class SlotMachine {
    constructor() {
        this.selectedFruit = null;
        this.selectedBet = 100;
        this.activeLines = [];
        this.init();
    }

    init() {
        this.setupFruitButtons();
        this.setupBetButtons();
        this.setupLineButtons();
    }

    // إعداد أزرار الفواكه
    setupFruitButtons() {
        const fruitButtons = document.querySelectorAll('.fruit-button');
        
        fruitButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                // إزالة التحديد من جميع الأزرار
                fruitButtons.forEach(btn => btn.classList.remove('selected'));
                
                // تحديد الزر المضغوط
                button.classList.add('selected');
                this.selectedFruit = button.dataset.fruit;
                
                // تأثير صوتي (اختياري)
                this.playClickSound();
                
                console.log(`تم اختيار الفاكهة: ${this.selectedFruit}`);
            });

            // تأثير الهوفر
            button.addEventListener('mouseenter', () => {
                button.style.transform = 'scale(1.1)';
            });

            button.addEventListener('mouseleave', () => {
                if (!button.classList.contains('selected')) {
                    button.style.transform = 'scale(1)';
                }
            });
        });
    }

    // إعداد أزرار الرهان
    setupBetButtons() {
        const betButtons = document.querySelectorAll('.bet-button');
        
        betButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                // إزالة التحديد من جميع أزرار الرهان
                betButtons.forEach(btn => btn.classList.remove('active'));
                
                // تحديد الزر المضغوط
                button.classList.add('active');
                this.selectedBet = parseInt(button.dataset.bet);
                
                this.playClickSound();
                console.log(`تم اختيار الرهان: ${this.selectedBet}`);
            });
        });

        // تحديد الرهان الافتراضي
        if (betButtons.length > 0) {
            betButtons[0].classList.add('active');
        }
    }

    // إعداد أزرار الخطوط
    setupLineButtons() {
        const lineButtons = document.querySelectorAll('.small-button');
        
        lineButtons.forEach((button, index) => {
            button.addEventListener('click', (e) => {
                const lineNumber = index + 1;
                
                if (button.classList.contains('active')) {
                    // إلغاء تفعيل الخط
                    button.classList.remove('active');
                    this.activeLines = this.activeLines.filter(line => line !== lineNumber);
                } else {
                    // تفعيل الخط
                    button.classList.add('active');
                    this.activeLines.push(lineNumber);
                }
                
                this.playClickSound();
                console.log(`الخطوط النشطة: ${this.activeLines.join(', ')}`);
            });
        });
    }

    // تشغيل صوت النقر
    playClickSound() {
        // يمكنك إضافة ملف صوتي هنا
        // const audio = new Audio('click-sound.mp3');
        // audio.play();
    }

    // الحصول على حالة اللعبة الحالية
    getGameState() {
        return {
            selectedFruit: this.selectedFruit,
            selectedBet: this.selectedBet,
            activeLines: this.activeLines,
            totalBet: this.selectedBet * this.activeLines.length
        };
    }

    // إعادة تعيين الاختيارات
    reset() {
        // إزالة جميع التحديدات
        document.querySelectorAll('.fruit-button').forEach(btn => {
            btn.classList.remove('selected');
        });
        
        document.querySelectorAll('.small-button').forEach(btn => {
            btn.classList.remove('active');
        });

        this.selectedFruit = null;
        this.activeLines = [];
        
        console.log('تم إعادة تعيين الاختيارات');
    }
}

// تشغيل اللعبة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    const game = new SlotMachine();
    
    // إضافة اللعبة إلى النافذة للوصول إليها من وحدة التحكم
    window.slotGame = game;
    
    console.log('تم تحميل لعبة السلوت بنجاح!');
});

// دالة مساعدة لعرض حالة اللعبة
function showGameState() {
    const state = window.slotGame.getGameState();
    console.log('حالة اللعبة الحالية:', state);
    return state;
}
