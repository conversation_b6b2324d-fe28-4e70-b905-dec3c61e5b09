import { SquareConfig, PayoutMultipliers } from '../types/game';

export const GAME_SYMBOLS = ['APPLE', 'LEMON', 'BANANA', 'WATERMELON', 'BAR'] as const;

// 24/7 Game System - 60 second rounds (كساعة دقيقة)
export const BETTING_PHASE_DURATION = 35; // 35 ثانية لفترة الرهان (توهج ناري)
export const LIGHT_ANIMATION_DURATION = 13; // 13 ثانية لدوران الإضاءة
export const RESULT_DISPLAY_DURATION = 12; // 12 ثانية لعرض النتائج + حفظ البيانات + العد التنازلي
export const TOTAL_ROUND_DURATION = 60; // إجمالي 60 ثانية لكل جولة
export const STARTING_BALANCE = 1000000;

// Light animation settings
export const LIGHT_SPEED = 100; // milliseconds between light movements
export const LIGHT_CYCLES = 3; // Number of complete cycles before selection

export const BOARD_SQUARES_CONFIG: SquareConfig[] = [
  // ===== الصف الأول (الإطار العلوي) =====
  { gridIndex: 0, symbol: 'APPLE', type: 'normal' },
  { gridIndex: 1, symbol: 'LEMON', type: 'halfFruit' }, // x2
  { gridIndex: 2, symbol: 'BAR', type: 'stackedBar' },
  { gridIndex: 3, symbol: 'LEMON', type: 'normal' }, // ليمون عادي x8
  { gridIndex: 4, symbol: 'APPLE', type: 'normal' },

  // ===== الصف الثاني =====
  { gridIndex: 5, symbol: 'BANANA', type: 'halfFruit' }, // x2
  // المربعات المركزية مخفية للتطوير: 6, 7, 8
  { gridIndex: 9, symbol: 'APPLE', type: 'halfFruit' }, // x2

  // ===== الصف الثالث =====
  { gridIndex: 10, symbol: 'LUCKY', type: 'luckyDoubleText', luckyNumber: 2 },
  // المربعات المركزية مخفية للتطوير: 11, 12, 13
  { gridIndex: 14, symbol: 'LUCKY', type: 'luckyTripleText', luckyNumber: 3 },

  // ===== الصف الرابع =====
  { gridIndex: 15, symbol: 'LEMON', type: 'normal' },
  // المربعات المركزية مخفية للتطوير: 16, 17, 18
  { gridIndex: 19, symbol: 'BANANA', type: 'normal' },

  // ===== الصف الخامس (الإطار السفلي) =====
  { gridIndex: 20, symbol: 'APPLE', type: 'normal' },
  { gridIndex: 21, symbol: 'BANANA', type: 'normal' },
  { gridIndex: 22, symbol: 'WATERMELON', type: 'normal' },
  { gridIndex: 23, symbol: 'WATERMELON', type: 'halfFruit' }, // x2
  { gridIndex: 24, symbol: 'APPLE', type: 'normal' }
];

// ===== المربعات المخفية للتطوير =====
// يمكن إعادة إضافتها عند الحاجة لشاشات أخرى:
// { gridIndex: 6, symbol: '', type: 'inner' },
// { gridIndex: 7, symbol: '', type: 'inner' },
// { gridIndex: 8, symbol: '', type: 'inner' },
// { gridIndex: 11, symbol: '', type: 'inner' },
// { gridIndex: 12, symbol: '', type: 'inner' }, // المربع المركزي فارغ
// { gridIndex: 13, symbol: '', type: 'inner' },
// { gridIndex: 16, symbol: '', type: 'inner' },
// { gridIndex: 17, symbol: '', type: 'inner' },
// { gridIndex: 18, symbol: '', type: 'inner' },

export const PAYOUT_MULTIPLIERS: PayoutMultipliers = {
  'half-fruit-win': 2,
  '2-match': 2,
  '4-match': 15,
  '5-match': 50,
  'APPLE': 3,
  'LEMON': 8,
  'BANANA': 6,
  'WATERMELON': 12,
  'BAR': 30,
  'lucky-double-text': 5,
  'lucky-triple-text': 10
};

export const ACTIVE_GAME_SQUARES = BOARD_SQUARES_CONFIG.filter(
  sq => sq.type !== 'inner' && sq.type !== 'centerCountdownNumber'
);

export const BET_AMOUNTS = [10000, 5000, 2000, 1000, 500] as const;

export const SYMBOL_MAX_BETS = {
  'BAR': 20000,
  'WATERMELON': 80000,
  'LEMON': 150000,
  'BANANA': 150000,
  'APPLE': 300000
} as const;