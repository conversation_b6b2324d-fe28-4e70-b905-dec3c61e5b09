# 🍀 الحركة المتعددة للاكي

## ✅ المشكلة السابقة

عند اختيار مربع LUCKY، كانت النتائج تظهر فورًا بدون حركة الشريط، مما يقلل من متعة اللعب.

## 🎯 الحل المطبق

### **السلوك الجديد للاكي:**

1. **LUCKY 2 (luckyDoubleText):**
   - الشريط يتحرك ويختار مربع LUCKY الأول
   - ثم يتحرك مرة أخرى ويختار مربع LUCKY ثاني
   - تظهر النتائج بعد الحركتين

2. **LUCKY 3 (luckyTripleText):**
   - الشريط يتحرك ويختار مربع LUCKY الأول
   - ثم يتحرك ويختار مربع LUCKY ثاني
   - ثم يتحرك ويختار مربع LUCKY ثالث
   - تظهر النتائج بعد الحركات الثلاث

### **في شريط التاريخ:**
- النتائج الثلاثة (أو الاثنتين) تظهر كمجموعة واحدة
- تمثل جولة اللاكي كاملة

## 🔧 التحديثات المطبقة

### **1. دوال جديدة في gameLogicNew.ts:**

```typescript
// دالة خاصة لحركة اللاكي المتعددة
export const generateLuckyAnimation = (luckyType: 'luckyDoubleText' | 'luckyTripleText'): {
  sequences: number[][];
  finalPositions: number[];
} => {
  const luckyCount = luckyType === 'luckyDoubleText' ? 2 : 3;
  // إنشاء تسلسلات حركة متعددة
  // اختيار مواقع عشوائية مختلفة للاكي
};

// دالة حساب الأرباح للاكي المتعدد
export const calculateLuckyMultiWin = (
  finalPositions: number[],
  betsOnTypes: BetsOnTypes
): { totalWinAmount: number; collectedSymbols: string[]; messages: string[] } => {
  // حساب الأرباح من جميع المواقع المختارة
};
```

### **2. تحديث useGameState.ts:**

```typescript
// دالة الحركة المتعددة للاكي
const startLuckyMultiAnimation = useCallback((confirmedBets: BetsOnTypes, luckyType: 'luckyDoubleText' | 'luckyTripleText') => {
  const { sequences, finalPositions } = generateLuckyAnimation(luckyType);
  let currentSequenceIndex = 0;
  let currentStepIndex = 0;
  
  const animateLuckyStep = () => {
    // تنفيذ الحركات المتعددة بالتسلسل
    // تأخير 500ms بين كل حركة
    // حساب النتيجة النهائية بعد انتهاء جميع الحركات
  };
  
  animateLuckyStep();
}, [lightAnimationInterval]);
```

### **3. منطق التحقق:**

```typescript
// التحقق من نوع المربع الفائز
const squareIndex = ACTIVE_GAME_SQUARES.findIndex(sq => sq.gridIndex === targetPosition);
const winningSquare = ACTIVE_GAME_SQUARES[squareIndex];

// إذا كان المربع الفائز لاكي، استخدم الحركة المتعددة
if (winningSquare && (winningSquare.type === 'luckyDoubleText' || winningSquare.type === 'luckyTripleText')) {
  console.log(`🍀 LUCKY detected: ${winningSquare.type}`);
  startLuckyMultiAnimation(confirmedBets, winningSquare.type);
  return;
}

// الحركة العادية للمربعات الأخرى
startNormalAnimation(confirmedBets, targetPosition);
```

## 🎮 النتيجة النهائية

### **السلوك الجديد:**
- ✅ **LUCKY 2:** حركتان متتاليتان + نتائج
- ✅ **LUCKY 3:** 3 حركات متتالية + نتائج
- ✅ **تأخير 500ms** بين كل حركة للوضوح
- ✅ **نتائج مجمعة** في شريط التاريخ
- ✅ **متعة بصرية** أكبر للاعب

### **الملفات المحدثة:**
- ✅ `src/utils/gameLogicNew.ts` - إضافة دوال الحركة المتعددة
- ✅ `src/hooks/useGameState.ts` - تحديث منطق الحركة

الآن اللاكي يوفر تجربة أكثر متعة وإثارة! 🎯✨ 