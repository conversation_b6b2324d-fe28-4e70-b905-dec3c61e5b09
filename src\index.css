/* تحسين الخطوط العامة */
@import url('https://fonts.googleapis.com/css2?family=Cinzel:wght@400;600;700;900&family=Playfair+Display:wght@400;700;900&family=Roboto+Mono:wght@400;500;600;700&family=Inter:wght@400;500;600;700&display=swap');

/* استيراد الأنماط المحسنة - مؤقتاً معطل للاختبار */
/* @import './styles/components.css'; */
/* @import './styles/responsive.css'; */
/* @import './styles/responsive-enhanced.css'; */
/* @import './styles/ux-enhancements.css'; */

@tailwind base;
@tailwind components;
@tailwind utilities;

/* إعدادات عامة محسنة */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;

  /* تحسين عرض النصوص */
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* منع التحديد غير المرغوب */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  /* تحسين الأداء */
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}

#root {
  width: 100vw;
  height: 100vh;
  position: relative;
  background: #0F0620;
}

/* Custom styles for Lucky Fruits theme - مطابق للصورة المرجعية */

/* Animation for rotating dots around the red box */
@keyframes rotateAroundBox {
  0% { opacity: 1; transform: scale(1); }
  25% { opacity: 0.8; transform: scale(1.2); }
  50% { opacity: 1; transform: scale(1); }
  75% { opacity: 0.8; transform: scale(0.8); }
  100% { opacity: 1; transform: scale(1); }
}

/* Shine effect for LUCKY buttons */
@keyframes shine {
  0% { transform: translateX(-100%) skewX(-15deg); }
  100% { transform: translateX(200%) skewX(-15deg); }
}

/* تم نقل جميع الألوان إلى ملف theme/colors.ts */
/* هذا الملف يحتوي على الـ animations والتأثيرات فقط */

body {
  background-image: url('/images/bg-390x844.webp'); /* غيّر المسار حسب الصورة المناسبة */
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center center;
  min-height: 100vh;
  min-width: 100vw;
  background-color: #000; /* لون خلفية مناسب للفراغات */
}

@media (min-width: 769px) and (max-width: 1200px) {
  body {
    background-image: url('/images/bg-768x1024.webp');
  }
}

@media (min-width: 1201px) {
  body {
    background-image: url('/images/bg-1920x1080.webp');
  }
}

html, body {
  height: 100%;
  overflow: auto;
}

.game-button {
  /* border-radius: 50%; */ /* تم إزالة التدوير الدائري */
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

/* 🎭 تأثيرات تفاعلية مطابقة للصورة المرجعية */

/* تأثيرات أزرار الفواكه */
.fruit-button-3d {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-style: preserve-3d;
}

.fruit-button-3d:hover {
  transform: perspective(300px) rotateX(10deg) rotateY(-2deg) scale(1.05);
  box-shadow: 0 8px 16px rgba(0,0,0,0.5),
              inset 0 3px 6px rgba(255,255,255,0.3),
              0 0 15px rgba(255,255,255,0.6);
}

.fruit-button-3d:active {
  transform: perspective(300px) rotateX(10deg) rotateY(-2deg) scale(0.95);
  transition: all 0.1s ease;
}

/* تأثيرات أزرار المبالغ */
.amount-button-3d {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-style: preserve-3d;
}

.amount-button-3d:hover {
  transform: perspective(200px) rotateX(5deg) scale(1.1);
  box-shadow: 0 6px 12px rgba(0,0,0,0.4),
              inset 0 3px 6px rgba(255,255,255,0.3),
              0 0 12px rgba(255,255,255,0.5);
}

.amount-button-3d:active {
  transform: perspective(200px) rotateX(5deg) scale(0.9);
  transition: all 0.1s ease;
}

/* تأثيرات الطاولة المائلة */
.betting-table-3d {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform-style: preserve-3d;
}

.betting-table-3d:hover {
  transform: perspective(800px) rotateX(12deg);
  box-shadow: inset 0 6px 12px rgba(0,0,0,0.5),
              inset 0 -6px 12px rgba(0,0,0,0.5),
              0 4px 8px rgba(0,0,0,0.3);
}

/* لمعة ذهبية متحركة */
@keyframes golden-shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

.golden-shimmer {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  background-size: 200% 100%;
  animation: golden-shimmer 2s infinite;
}

/* تأثير النبض للعناصر النشطة */
@keyframes golden-pulse {
  0%, 100% { box-shadow: 0 0 5px rgba(255,255,255,0.5); }
  50% { box-shadow: 0 0 20px rgba(255,255,255,0.8), 0 0 30px rgba(255,255,255,0.6); }
}

.golden-pulse {
  animation: golden-pulse 2s infinite;
}

/* تم نقل ألوان .lucky-square إلى نظام الألوان المركزي */
.lucky-square {
  border-radius: 12px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0%, 100% { 
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3), 0 0 20px rgba(255, 255, 255, 0.4);
  }
  50% { 
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3), 0 0 30px rgba(255, 255, 255, 0.7);
  }
}

/* تأثيرات التوهج المحسنة */
.gold-glow {
  box-shadow: 0 0 20px rgba(255, 215, 0, 0.6), 0 0 40px rgba(255, 215, 0, 0.4);
  animation: golden-pulse 2s infinite;
}

.purple-glow {
  box-shadow: 0 0 20px rgba(147, 51, 234, 0.6), 0 0 40px rgba(147, 51, 234, 0.4);
  animation: purple-pulse 2s infinite;
}

/* تأثيرات النبض المحسنة */
@keyframes golden-pulse-enhanced {
  0%, 100% {
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.5), 0 0 30px rgba(255, 215, 0, 0.3);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 25px rgba(255, 215, 0, 0.8), 0 0 50px rgba(255, 215, 0, 0.5);
    transform: scale(1.02);
  }
}

@keyframes purple-pulse {
  0%, 100% {
    box-shadow: 0 0 15px rgba(147, 51, 234, 0.5), 0 0 30px rgba(147, 51, 234, 0.3);
  }
  50% {
    box-shadow: 0 0 25px rgba(147, 51, 234, 0.8), 0 0 50px rgba(147, 51, 234, 0.5);
  }
}

/* تأثير التوهج للعناصر التفاعلية */
.interactive-glow {
  transition: all 0.3s ease;
}

.interactive-glow:hover {
  box-shadow: 0 0 30px rgba(255, 255, 255, 0.6), 0 0 60px rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

/* أنيميشن ظهور الرسائل والبطاقات */
@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

/* تأثير النبض للعناصر المهمة */
@keyframes importantPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 30px rgba(255, 215, 0, 0.8);
  }
}

.important-pulse {
  animation: importantPulse 2s infinite;
}

/* تأثير التموج للأزرار */
@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

.ripple-effect {
  position: relative;
  overflow: hidden;
}

.ripple-effect::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  transform: scale(0);
  animation: ripple 0.6s linear;
  pointer-events: none;
}

/* توهج ناري خفيف (لم يعد مستخدم - التوهج الناري فقط للمربع المحدد) */
.fire-glow {
  /* تم إزالة التوهج العام - التوهج الناري فقط للمربع المحدد أثناء الدوران */
}

/* التوهج الناري للمربع المحدد (الذي يدور ويتوقف على الفاكهة الفائزة) */
.fire-glow-selected {
  background: linear-gradient(45deg,
    rgba(255, 69, 0, 0.7),
    rgba(255, 140, 0, 0.9),
    rgba(255, 165, 0, 0.7),
    rgba(255, 69, 0, 0.7)
  );
  box-shadow:
    0 0 35px rgba(255, 69, 0, 1.2),
    0 0 55px rgba(255, 140, 0, 1),
    0 0 75px rgba(255, 165, 0, 0.8),
    0 0 95px rgba(255, 69, 0, 0.6),
    inset 0 0 30px rgba(255, 69, 0, 0.5);
  z-index: 10;
  animation: fireIntensity 1s ease-in-out infinite alternate;
}

.fire-border-selected {
  background: transparent;
  border: 5px solid rgba(255, 69, 0, 1);
  box-shadow:
    0 0 45px rgba(255, 69, 0, 1.2),
    0 0 65px rgba(255, 140, 0, 1),
    0 0 85px rgba(255, 165, 0, 0.8),
    inset 0 0 25px rgba(255, 69, 0, 0.4);
  z-index: 11;
}

@keyframes fireIntensity {
  0% {
    box-shadow:
      0 0 35px rgba(255, 69, 0, 1.2),
      0 0 55px rgba(255, 140, 0, 1),
      0 0 75px rgba(255, 165, 0, 0.8),
      0 0 95px rgba(255, 69, 0, 0.6),
      inset 0 0 30px rgba(255, 69, 0, 0.5);
  }
  100% {
    box-shadow:
      0 0 45px rgba(255, 69, 0, 1.4),
      0 0 65px rgba(255, 140, 0, 1.2),
      0 0 85px rgba(255, 165, 0, 1),
      0 0 105px rgba(255, 69, 0, 0.8),
      inset 0 0 35px rgba(255, 69, 0, 0.6);
  }
}

/* تأثير خاص لـ Infinity Box */
.infinity-box-glow {
  animation: infinityGlow 3s ease-in-out infinite alternate;
  box-shadow:
    0 0 20px rgba(255, 255, 255, 0.6),
    0 0 40px rgba(255, 255, 255, 0.4),
    0 0 60px rgba(255, 255, 255, 0.2),
    inset 0 0 20px rgba(255, 255, 255, 0.1);
}

@keyframes infinityGlow {
  0% {
    box-shadow:
      0 0 20px rgba(255, 255, 255, 0.6),
      0 0 40px rgba(255, 255, 255, 0.4),
      0 0 60px rgba(255, 255, 255, 0.2),
      inset 0 0 20px rgba(255, 255, 255, 0.1);
  }
  100% {
    box-shadow:
      0 0 30px rgba(255, 255, 255, 0.8),
      0 0 50px rgba(255, 255, 255, 0.6),
      0 0 70px rgba(255, 255, 255, 0.4),
      inset 0 0 25px rgba(255, 255, 255, 0.2);
  }
}

@keyframes fireGlow {
  0% {
    box-shadow:
      0 0 15px rgba(255, 69, 0, 0.7),
      0 0 25px rgba(255, 140, 0, 0.5),
      0 0 35px rgba(255, 165, 0, 0.3),
      0 0 45px rgba(255, 69, 0, 0.2),
      inset 0 0 15px rgba(255, 69, 0, 0.1);
  }
  50% {
    box-shadow:
      0 0 25px rgba(255, 69, 0, 0.9),
      0 0 35px rgba(255, 140, 0, 0.7),
      0 0 45px rgba(255, 165, 0, 0.5),
      0 0 55px rgba(255, 69, 0, 0.3),
      inset 0 0 20px rgba(255, 69, 0, 0.2);
  }
  100% {
    box-shadow:
      0 0 20px rgba(255, 69, 0, 0.8),
      0 0 30px rgba(255, 140, 0, 0.6),
      0 0 40px rgba(255, 165, 0, 0.4),
      0 0 50px rgba(255, 69, 0, 0.25),
      inset 0 0 18px rgba(255, 69, 0, 0.15);
  }
}

/* تأثيرات التجسيم البنفسجي للإطار الداخلي */
.purple-3d-frame {
  position: relative;
  overflow: hidden;
}

.purple-3d-frame::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(106, 76, 147, 0.3),
    transparent
  );
  animation: purple-shine 4s infinite;
  z-index: 1;
}

@keyframes purple-shine {
  0% { left: -100%; }
  50% { left: 100%; }
  100% { left: 100%; }
}

/* تأثير التموج البنفسجي */
@keyframes purple-ripple {
  0% {
    box-shadow:
      0 8px 16px rgba(0,0,0,0.6),
      inset 0 3px 6px rgba(106,76,147,0.3),
      inset 0 -3px 6px rgba(0,0,0,0.4);
  }
  50% {
    box-shadow:
      0 12px 24px rgba(0,0,0,0.7),
      inset 0 4px 8px rgba(106,76,147,0.4),
      inset 0 -4px 8px rgba(0,0,0,0.5),
      0 0 20px rgba(106,76,147,0.3);
  }
  100% {
    box-shadow:
      0 8px 16px rgba(0,0,0,0.6),
      inset 0 3px 6px rgba(106,76,147,0.3),
      inset 0 -3px 6px rgba(0,0,0,0.4);
  }
}

.purple-ripple {
  animation: purple-ripple 3s ease-in-out infinite;
}

.fruit-btn {
  position: relative;
  border-radius: 0px !important;
  box-shadow:
    0 8px 32px 0 #000a,
    0 2px 12px rgba(255, 255, 255, 0.3),
    0 1.5px 6px rgba(255, 255, 255, 0.2),
    0 0.5px 2px rgba(255, 255, 255, 0.1),
    inset 0 8px 18px rgba(255, 255, 255, 0.2),
    inset 0 1.5px 2.5px rgba(255, 255, 255, 0.1);
  will-change: box-shadow, transform;
  transition: box-shadow 0.22s cubic-bezier(.4,2,.3,1), transform 0.16s, filter 0.18s;
  transform: scaleY(1.08);
  overflow: visible;
}
.fruit-btn::before {
  content: '';
  position: absolute;
  top: 7px;
  left: 12%;
  width: 76%;
  height: 18%;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7) 60%, transparent 100%);
  border-radius: 0px;
  filter: blur(1.5px);
  opacity: 0.85;
  pointer-events: none;
  z-index: 2;
}
.fruit-btn:active {
  box-shadow: 0 2px 6px #0007, 0 1px 2px rgba(255, 255, 255, 0.2), inset 0 1.5px 3px rgba(255, 255, 255, 0.1);
  filter: brightness(0.97);
}
.fruit-btn:active::before {
  opacity: 0.25;
}
.fruit-btn:active > * {
  transform: translateY(10px);
  transition: transform 0.13s;
}

/* تأثيرات الشريط الضوئي المحسن والسلس */
@keyframes lightPulse {
  0% {
    opacity: 0.8;
    transform: scale(1);
    box-shadow: 0 0 30px rgba(255, 255, 255, 0.7), inset 0 0 15px rgba(255, 255, 255, 0.3);
  }
  100% {
    opacity: 1;
    transform: scale(1.05);
    box-shadow: 0 0 50px rgba(255, 255, 255, 1), inset 0 0 25px rgba(255, 255, 255, 0.5);
  }
}

/* انتقال سلس للشريط الناري بين المربعات */
@keyframes smoothLightTransition {
  0% {
    opacity: 0;
    transform: scale(0.8);
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
    box-shadow: 0 0 40px rgba(255, 215, 0, 0.9), inset 0 0 20px rgba(255, 215, 0, 0.4);
  }
  100% {
    opacity: 1;
    transform: scale(1.05);
    box-shadow: 0 0 35px rgba(255, 215, 0, 0.8), inset 0 0 18px rgba(255, 215, 0, 0.3);
  }
}

/* تأثير موجة الضوء المتحركة */
@keyframes lightWave {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes shimmerMove {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(200%) translateY(200%) rotate(45deg);
  }
}

@keyframes rotateGlow {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes borderPulse {
  0%, 100% {
    border-color: rgba(255, 255, 255, 0.9);
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.6);
  }
  50% {
    border-color: rgba(255, 140, 0, 1);
    box-shadow: 0 0 30px rgba(255, 140, 0, 0.8);
  }
}

@keyframes particleFloat {
  0%, 100% {
    opacity: 0.6;
    transform: translateY(0px) scale(1);
  }
  25% {
    opacity: 0.8;
    transform: translateY(-5px) scale(1.1);
  }
  50% {
    opacity: 1;
    transform: translateY(-10px) scale(1.2);
  }
  75% {
    opacity: 0.8;
    transform: translateY(-5px) scale(1.1);
  }
}

/* تأثير التوهج المتقدم للشريط الضوئي */
@keyframes advancedGlow {
  0% {
    background: radial-gradient(circle at center, rgba(255, 255, 255, 0.9) 0%, rgba(255, 140, 0, 0.7) 40%, rgba(255, 69, 0, 0.5) 70%, transparent 100%);
    box-shadow: 
      0 0 30px rgba(255, 255, 255, 0.8),
      0 0 60px rgba(255, 140, 0, 0.6),
      0 0 90px rgba(255, 69, 0, 0.4),
      inset 0 0 20px rgba(255, 255, 255, 0.3);
  }
  50% {
    background: radial-gradient(circle at center, rgba(255, 255, 255, 0.9) 0%, rgba(255, 215, 0, 0.8) 30%, rgba(255, 140, 0, 0.6) 60%, transparent 100%);
    box-shadow: 
      0 0 40px rgba(255, 255, 255, 0.9),
      0 0 80px rgba(255, 215, 0, 0.7),
      0 0 120px rgba(255, 140, 0, 0.5),
      inset 0 0 30px rgba(255, 255, 255, 0.4);
  }
  100% {
    background: radial-gradient(circle at center, rgba(255, 255, 255, 0.9) 0%, rgba(255, 140, 0, 0.7) 40%, rgba(255, 69, 0, 0.5) 70%, transparent 100%);
    box-shadow: 
      0 0 30px rgba(255, 255, 255, 0.8),
      0 0 60px rgba(255, 140, 0, 0.6),
      0 0 90px rgba(255, 69, 0, 0.4),
      inset 0 0 20px rgba(255, 255, 255, 0.3);
  }
}

/* تأثير الإشعاع الدائري */
@keyframes circularRadiate {
  0% {
    transform: scale(1) rotate(0deg);
    opacity: 0.8;
  }
  25% {
    transform: scale(1.1) rotate(90deg);
    opacity: 1;
  }
  50% {
    transform: scale(1.2) rotate(180deg);
    opacity: 0.9;
  }
  75% {
    transform: scale(1.1) rotate(270deg);
    opacity: 1;
  }
  100% {
    transform: scale(1) rotate(360deg);
    opacity: 0.8;
  }
}

/* تأثير التموج */
@keyframes waveEffect {
  0% {
    border-radius: 8px;
    transform: scale(1);
  }
  25% {
    border-radius: 12px;
    transform: scale(1.02);
  }
  50% {
    border-radius: 16px;
    transform: scale(1.05);
  }
  75% {
    border-radius: 12px;
    transform: scale(1.02);
  }
  100% {
    border-radius: 8px;
    transform: scale(1);
  }
}

/* تأثير التوهج الناري المحسن */
@keyframes fireGlow {
  0% {
    box-shadow:
      0 0 15px rgba(255, 69, 0, 0.7),
      0 0 25px rgba(255, 140, 0, 0.5),
      0 0 35px rgba(255, 165, 0, 0.3),
      0 0 45px rgba(255, 69, 0, 0.2),
      inset 0 0 15px rgba(255, 69, 0, 0.1);
  }
  50% {
    box-shadow:
      0 0 25px rgba(255, 69, 0, 0.9),
      0 0 35px rgba(255, 140, 0, 0.7),
      0 0 45px rgba(255, 165, 0, 0.5),
      0 0 55px rgba(255, 69, 0, 0.3),
      inset 0 0 20px rgba(255, 69, 0, 0.2);
  }
  100% {
    box-shadow:
      0 0 20px rgba(255, 69, 0, 0.8),
      0 0 30px rgba(255, 140, 0, 0.6),
      0 0 40px rgba(255, 165, 0, 0.4),
      0 0 50px rgba(255, 69, 0, 0.25),
      inset 0 0 18px rgba(255, 69, 0, 0.15);
  }
}

/* تم نقل تحسينات الاستجابة إلى responsive-enhanced.css */

/* تم نقل جميع تحسينات الاستجابة إلى responsive-enhanced.css */
