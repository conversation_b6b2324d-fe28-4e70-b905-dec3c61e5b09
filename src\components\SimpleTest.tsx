import React from 'react';

interface SimpleTestProps {
  balance: number;
  isBettingPhase: boolean;
  currentBetValueToApply: number;
}

const SimpleTest: React.FC<SimpleTestProps> = ({
  balance,
  isBettingPhase,
  currentBetValueToApply
}) => {
  return (
    <div style={{
      position: 'fixed',
      top: '50%',
      left: '50%',
      transform: 'translate(-50%, -50%)',
      background: 'rgba(0,0,0,0.9)',
      color: 'white',
      padding: '20px',
      borderRadius: '10px',
      fontSize: '16px',
      zIndex: 10000,
      border: '3px solid #FFD700',
      minWidth: '300px',
      textAlign: 'center'
    }}>
      <h3 style={{ color: '#FFD700', marginBottom: '15px' }}>🔧 اختبار بسيط</h3>
      
      <div style={{ marginBottom: '10px' }}>
        <strong>الرصيد:</strong> {balance.toLocaleString()}
      </div>
      
      <div style={{ marginBottom: '10px' }}>
        <strong>مرحلة الرهان:</strong> {isBettingPhase ? '✅ نعم' : '❌ لا'}
      </div>
      
      <div style={{ marginBottom: '10px' }}>
        <strong>المبلغ المختار:</strong> {currentBetValueToApply.toLocaleString()}
      </div>

      <div style={{ 
        marginTop: '15px', 
        padding: '10px',
        background: isBettingPhase ? 'rgba(0,255,0,0.2)' : 'rgba(255,0,0,0.2)',
        borderRadius: '5px'
      }}>
        <strong>الحالة:</strong> {isBettingPhase ? 'الأزرار يجب أن تعمل' : 'الأزرار معطلة - انتظر مرحلة الرهان'}
      </div>

      <button
        onClick={() => {
          const testDiv = document.querySelector('[data-test="simple-test"]');
          if (testDiv) {
            testDiv.remove();
          }
        }}
        style={{
          marginTop: '15px',
          padding: '8px 16px',
          background: '#FFD700',
          color: '#000',
          border: 'none',
          borderRadius: '5px',
          cursor: 'pointer',
          fontWeight: 'bold'
        }}
      >
        إغلاق
      </button>
    </div>
  );
};

export default SimpleTest; 