/* 📱 نظام الاستجابة المحسن - تصميم متجاوب متقدم */

/* ===============================
   🎯 المتغيرات المتجاوبة
   =============================== */
:root {
  /* نقاط الكسر */
  --breakpoint-xs: 375px;
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
  
  /* أحجام الخطوط المتجاوبة */
  --text-xs: clamp(10px, 2.5vw, 12px);
  --text-sm: clamp(12px, 3vw, 14px);
  --text-base: clamp(14px, 3.5vw, 16px);
  --text-lg: clamp(16px, 4vw, 18px);
  --text-xl: clamp(18px, 4.5vw, 20px);
  --text-2xl: clamp(20px, 5vw, 24px);
  
  /* أحجام العناصر المتجاوبة */
  --button-size-sm: clamp(35px, 8vw, 45px);
  --button-size-md: clamp(45px, 10vw, 60px);
  --button-size-lg: clamp(60px, 12vw, 80px);
  
  /* المسافات المتجاوبة */
  --spacing-responsive-xs: clamp(4px, 1vw, 8px);
  --spacing-responsive-sm: clamp(8px, 2vw, 16px);
  --spacing-responsive-md: clamp(16px, 4vw, 24px);
  --spacing-responsive-lg: clamp(24px, 6vw, 32px);
}

/* ===============================
   📱 الأجهزة المحمولة الصغيرة
   =============================== */
@media screen and (max-width: 375px) {
  :root {
    --game-board-scale: 0.7;
    --fruit-button-size: 35px;
    --amount-button-size: 28px;
    --text-scale: 0.8;
  }
  
  .game-board {
    transform: scale(var(--game-board-scale)) !important;
    top: 25% !important;
  }
  
  .fruit-button {
    width: var(--fruit-button-size) !important;
    height: var(--fruit-button-size) !important;
    font-size: calc(var(--text-xs) * var(--text-scale)) !important;
  }
  
  .amount-button {
    width: var(--amount-button-size) !important;
    height: var(--amount-button-size) !important;
    font-size: calc(var(--text-xs) * var(--text-scale)) !important;
  }
  
  .balance-text,
  .bet-text {
    font-size: calc(var(--text-sm) * var(--text-scale)) !important;
  }
  
  .phase-alert {
    padding: var(--spacing-responsive-sm) var(--spacing-responsive-md) !important;
    max-width: 90vw !important;
  }
  
  .phase-alert-text {
    font-size: calc(var(--text-base) * var(--text-scale)) !important;
  }
}

/* ===============================
   📱 الأجهزة المحمولة المتوسطة
   =============================== */
@media screen and (min-width: 376px) and (max-width: 428px) {
  :root {
    --game-board-scale: 0.8;
    --fruit-button-size: 45px;
    --amount-button-size: 35px;
    --text-scale: 0.9;
  }
  
  .game-board {
    transform: scale(var(--game-board-scale)) !important;
    top: 28% !important;
  }
  
  .fruit-button {
    width: var(--fruit-button-size) !important;
    height: var(--fruit-button-size) !important;
    font-size: calc(var(--text-sm) * var(--text-scale)) !important;
  }
  
  .amount-button {
    width: var(--amount-button-size) !important;
    height: var(--amount-button-size) !important;
    font-size: calc(var(--text-xs) * var(--text-scale)) !important;
  }
  
  .balance-text,
  .bet-text {
    font-size: calc(var(--text-base) * var(--text-scale)) !important;
  }
}

/* ===============================
   📱 الأجهزة المحمولة الكبيرة
   =============================== */
@media screen and (min-width: 429px) and (max-width: 768px) {
  :root {
    --game-board-scale: 0.9;
    --fruit-button-size: 50px;
    --amount-button-size: 40px;
    --text-scale: 1;
  }
  
  .game-board {
    transform: scale(var(--game-board-scale)) !important;
    top: 30% !important;
  }
  
  .fruit-button {
    width: var(--fruit-button-size) !important;
    height: var(--fruit-button-size) !important;
    font-size: var(--text-sm) !important;
  }
  
  .amount-button {
    width: var(--amount-button-size) !important;
    height: var(--amount-button-size) !important;
    font-size: var(--text-xs) !important;
  }
  
  .balance-text,
  .bet-text {
    font-size: var(--text-base) !important;
  }
}

/* ===============================
   💻 الأجهزة اللوحية
   =============================== */
@media screen and (min-width: 769px) and (max-width: 1024px) {
  :root {
    --game-board-scale: 1;
    --fruit-button-size: 55px;
    --amount-button-size: 45px;
    --text-scale: 1.1;
  }
  
  .game-board {
    transform: scale(var(--game-board-scale)) !important;
    top: 32% !important;
  }
  
  .fruit-button {
    width: var(--fruit-button-size) !important;
    height: var(--fruit-button-size) !important;
    font-size: calc(var(--text-sm) * var(--text-scale)) !important;
  }
  
  .amount-button {
    width: var(--amount-button-size) !important;
    height: var(--amount-button-size) !important;
    font-size: calc(var(--text-xs) * var(--text-scale)) !important;
  }
  
  .balance-text,
  .bet-text {
    font-size: calc(var(--text-lg) * var(--text-scale)) !important;
  }
  
  .phase-alert-text {
    font-size: calc(var(--text-xl) * var(--text-scale)) !important;
  }
}

/* ===============================
   🖥️ أجهزة الكمبيوتر
   =============================== */
@media screen and (min-width: 1025px) and (max-width: 1440px) {
  :root {
    --game-board-scale: 1.1;
    --fruit-button-size: 60px;
    --amount-button-size: 50px;
    --text-scale: 1.2;
  }
  
  .game-board {
    transform: scale(var(--game-board-scale)) !important;
    top: 35% !important;
  }
  
  .fruit-button {
    width: var(--fruit-button-size) !important;
    height: var(--fruit-button-size) !important;
    font-size: calc(var(--text-base) * var(--text-scale)) !important;
  }
  
  .amount-button {
    width: var(--amount-button-size) !important;
    height: var(--amount-button-size) !important;
    font-size: calc(var(--text-sm) * var(--text-scale)) !important;
  }
  
  .balance-text,
  .bet-text {
    font-size: calc(var(--text-xl) * var(--text-scale)) !important;
  }
  
  .phase-alert-text {
    font-size: calc(var(--text-2xl) * var(--text-scale)) !important;
  }
}

/* ===============================
   🖥️ الشاشات الكبيرة جداً
   =============================== */
@media screen and (min-width: 1441px) {
  :root {
    --game-board-scale: 1.3;
    --fruit-button-size: 70px;
    --amount-button-size: 55px;
    --text-scale: 1.4;
  }
  
  .game-board {
    transform: scale(var(--game-board-scale)) !important;
    top: 38% !important;
  }
  
  .fruit-button {
    width: var(--fruit-button-size) !important;
    height: var(--fruit-button-size) !important;
    font-size: calc(var(--text-lg) * var(--text-scale)) !important;
  }
  
  .amount-button {
    width: var(--amount-button-size) !important;
    height: var(--amount-button-size) !important;
    font-size: calc(var(--text-base) * var(--text-scale)) !important;
  }
  
  .balance-text,
  .bet-text {
    font-size: calc(var(--text-2xl) * var(--text-scale)) !important;
  }
  
  .phase-alert-text {
    font-size: calc(var(--text-2xl) * 1.2 * var(--text-scale)) !important;
  }
}

/* ===============================
   🔄 الاتجاه الأفقي للجوال
   =============================== */
@media screen and (max-width: 768px) and (orientation: landscape) {
  .game-board {
    transform: scale(0.6) !important;
    top: 15% !important;
    left: 25% !important;
  }
  
  .balance-display {
    top: 5px !important;
    left: 5px !important;
    font-size: 12px !important;
  }
  
  .total-bet-display {
    top: 5px !important;
    right: 5px !important;
    font-size: 12px !important;
  }
  
  .phase-alert {
    padding: var(--spacing-responsive-xs) var(--spacing-responsive-sm) !important;
    max-width: 70vw !important;
  }
  
  .phase-alert-text {
    font-size: var(--text-sm) !important;
  }
}

/* ===============================
   🎯 تحسينات خاصة بالأجهزة
   =============================== */

/* iPhone SE وأجهزة مشابهة */
@media screen and (device-width: 375px) and (device-height: 667px) {
  .responsive-background {
    background-size: cover !important;
    background-position: center center !important;
  }
  
  .game-board {
    transform: scale(0.75) !important;
    top: 22% !important;
  }
}

/* iPhone 12/13/14 */
@media screen and (device-width: 390px) and (device-height: 844px) {
  .responsive-background {
    background-size: cover !important;
    background-position: center center !important;
  }
  
  .game-board {
    transform: scale(0.8) !important;
    top: 25% !important;
  }
}

/* iPhone 14 Pro Max */
@media screen and (device-width: 428px) and (device-height: 926px) {
  .responsive-background {
    background-size: cover !important;
    background-position: center center !important;
  }
  
  .game-board {
    transform: scale(0.85) !important;
    top: 20% !important;
  }
}

/* Samsung Galaxy وأجهزة Android */
@media screen and (device-width: 412px) and (device-height: 915px) {
  .responsive-background {
    background-size: cover !important;
    background-position: center center !important;
  }
  
  .game-board {
    transform: scale(0.82) !important;
    top: 23% !important;
  }
}

/* ===============================
   ⚡ تحسينات الأداء
   =============================== */

/* تحسين الرسم للعناصر المتحركة */
.game-board,
.fruit-button,
.amount-button,
.balance-display,
.total-bet-display {
  will-change: transform;
  contain: layout style paint;
}

/* تحسين التمرير للأجهزة اللمسية */
@media (hover: none) and (pointer: coarse) {
  .fruit-button,
  .amount-button {
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }
}

/* تقليل الحركة للمستخدمين الذين يفضلون ذلك */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
