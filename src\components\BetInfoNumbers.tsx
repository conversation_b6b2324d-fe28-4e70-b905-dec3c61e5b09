import React from 'react';
import { BetsOnTypes } from '../types/game';

interface BetInfoNumbersProps {
  betsOnTypes: BetsOnTypes;
  currentBetValue: number;
  lastWinAmount?: number;
  balance: number;
  isVisible?: boolean;
}

const BetInfoNumbers: React.FC<BetInfoNumbersProps> = ({ 
  betsOnTypes, 
  currentBetValue,
  lastWinAmount = 0,
  balance,
  isVisible = true 
}) => {
  if (!isVisible) return null;

  // مواقع أرقام الرهان - محدثة لـ Galaxy (360x740)
  const betNumbers = {
    totalBets: { left: '88%', top: '2%', info: 'TOTAL_BETS' },
    currentBet: { left: '85%', top: '2%', info: 'CURRENT_BET' },
    lastWin: { left: '78%', top: '82%', info: 'LAST_WIN' }
  };

  const numberStyle = (position: { left: string; top: string; info: string }) => ({
    position: 'absolute' as const,
    left: position.left,
    top: position.top,
    width: '50px',
    height: '18px',
    background: 'rgba(255, 215, 0, 0.8)',
    border: '1px solid #ffd700',
    borderRadius: '6px',
    color: '#000',
    fontSize: '7px',
    fontWeight: 'bold' as const,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    transform: 'translate(-50%, -50%)',
    zIndex: 15,
    backdropFilter: 'blur(3px)',
    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.3)',
  });

  const formatAmount = (amount: number): string => {
    if (amount >= 1000000) {
      return `${(amount / 1000000).toFixed(1)}M`;
    } else if (amount >= 1000) {
      return `${(amount / 1000).toFixed(0)}K`;
    }
    return amount.toString();
  };

  // حساب إجمالي الرهانات
  const totalBets = Object.values(betsOnTypes).reduce((sum, bet) => sum + bet, 0);

  const getDisplayValue = (info: string): string => {
    switch (info) {
      case 'TOTAL_BETS':
        return formatAmount(totalBets);
      case 'CURRENT_BET':
        return formatAmount(currentBetValue);
      case 'LAST_WIN':
        return formatAmount(lastWinAmount);
      default:
        return '0';
    }
  };

  const getDisplayLabel = (info: string): string => {
    switch (info) {
      case 'TOTAL_BETS':
        return 'إجمالي';
      case 'CURRENT_BET':
        return 'الحالي';
      case 'LAST_WIN':
        return 'آخر ربح';
      default:
        return '';
    }
  };

  return (
    <div className="bet-info-numbers">
      {Object.entries(betNumbers).map(([key, position]) => {
        const displayValue = getDisplayValue(position.info);
        const displayLabel = getDisplayLabel(position.info);
        
        return (
          <div
            key={key}
            style={numberStyle(position)}
            title={`${displayLabel}: ${displayValue}`}
          >
            {displayValue}
          </div>
        );
      })}
    </div>
  );
};

export default BetInfoNumbers;
