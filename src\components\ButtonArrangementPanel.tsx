import React, { useState, useEffect } from 'react';

interface ButtonArrangementPanelProps {
  isVisible: boolean;
  onClose: () => void;
}

const ButtonArrangementPanel: React.FC<ButtonArrangementPanelProps> = ({ isVisible, onClose }) => {
  const [isEditMode, setIsEditMode] = useState(false);
  const [showGrid, setShowGrid] = useState(false);
  const [buttonsInfo, setButtonsInfo] = useState<string[]>([]);

  // دالة تفعيل وضع التعديل
  const enableEditMode = () => {
    const buttons = document.querySelectorAll('[data-symbol]') as NodeListOf<HTMLElement>;
    
    if (buttons.length === 0) {
      alert('لم يتم العثور على أزرار. تأكد من أن اللعبة محملة بشكل صحيح.');
      return;
    }

    buttons.forEach(button => {
      makeButtonDraggable(button);
    });
    
    setIsEditMode(true);
    setButtonsInfo([`تم تفعيل السحب لـ ${buttons.length} أزرار`]);
  };

  // دالة إلغاء وضع التعديل
  const disableEditMode = () => {
    const buttons = document.querySelectorAll('[data-symbol]') as NodeListOf<HTMLElement>;
    
    buttons.forEach(button => {
      button.style.cursor = 'pointer';
      button.onmousedown = null;
      button.ontouchstart = null;
    });
    
    setIsEditMode(false);
    setButtonsInfo(['تم إلغاء وضع التعديل']);
  };

  // دالة عرض/إخفاء الشبكة
  const toggleGrid = () => {
    const existingGrid = document.getElementById('position-grid');
    
    if (existingGrid) {
      existingGrid.remove();
      setShowGrid(false);
    } else {
      const grid = document.createElement('div');
      grid.id = 'position-grid';
      grid.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        pointer-events: none;
        z-index: 15;
        background-image: 
          linear-gradient(rgba(0, 255, 136, 0.2) 1px, transparent 1px),
          linear-gradient(90deg, rgba(0, 255, 136, 0.2) 1px, transparent 1px);
        background-size: 10% 10%;
      `;
      document.body.appendChild(grid);
      setShowGrid(true);
    }
  };

  // دالة حفظ المواقع
  const savePositions = () => {
    const buttons = document.querySelectorAll('[data-symbol]') as NodeListOf<HTMLElement>;
    const positions: string[] = [];
    
    positions.push('buttonLayout: {');
    
    buttons.forEach(button => {
      const symbol = button.getAttribute('data-symbol');
      const key = button.getAttribute('data-key') || symbol?.toLowerCase();
      const left = button.style.left;
      const top = button.style.top;
      
      if (symbol && left && top) {
        positions.push(`  ${key}: { left: '${left}', top: '${top}', name: '${symbol}' },`);
      }
    });
    
    positions.push('}');
    
    // عرض النتيجة
    const result = positions.join('\n');
    setButtonsInfo([
      'تم حفظ المواقع! انسخ الكود التالي:',
      '',
      result,
      '',
      'ضعه في ملف: src/utils/multiDeviceBackground.ts',
      'في قسم iPhone 12/13/14'
    ]);
    
    // نسخ للحافظة
    navigator.clipboard.writeText(result).then(() => {
      alert('تم نسخ الكود للحافظة!');
    });
  };

  // دالة إعادة تحميل
  const reloadPage = () => {
    window.location.reload();
  };

  // دالة السحب المحسنة
  const makeButtonDraggable = (button: HTMLElement) => {
    button.style.cursor = 'move';
    button.style.userSelect = 'none';
    
    let isDragging = false;
    let startX = 0;
    let startY = 0;
    let startLeft = 0;
    let startTop = 0;
    
    const startDrag = (clientX: number, clientY: number, e: Event) => {
      e.preventDefault();
      e.stopPropagation();
      
      isDragging = true;
      startX = clientX;
      startY = clientY;
      
      const buttonRect = button.getBoundingClientRect();
      startLeft = ((buttonRect.left + buttonRect.width/2) / window.innerWidth) * 100;
      startTop = ((buttonRect.top + buttonRect.height/2) / window.innerHeight) * 100;
      
      button.style.borderColor = '#ff0066';
      button.style.boxShadow = '0 0 20px rgba(255, 0, 102, 0.8)';
      button.style.transform = 'translate(-50%, -50%) scale(1.1)';
      button.style.zIndex = '9999';
    };
    
    const drag = (clientX: number, clientY: number) => {
      if (!isDragging) return;
      
      const deltaX = clientX - startX;
      const deltaY = clientY - startY;
      
      const deltaLeftPercent = (deltaX / window.innerWidth) * 100;
      const deltaTopPercent = (deltaY / window.innerHeight) * 100;
      
      const newLeft = Math.max(5, Math.min(95, startLeft + deltaLeftPercent));
      const newTop = Math.max(5, Math.min(95, startTop + deltaTopPercent));
      
      button.style.left = `${newLeft}%`;
      button.style.top = `${newTop}%`;
    };
    
    const endDrag = () => {
      if (!isDragging) return;
      
      isDragging = false;
      button.style.borderColor = '#00ff88';
      button.style.boxShadow = 'none';
      button.style.transform = 'translate(-50%, -50%) scale(1)';
      button.style.zIndex = '20';
      
      document.onmousemove = null;
      document.onmouseup = null;
      document.ontouchmove = null;
      document.ontouchend = null;
    };
    
    // Mouse events
    button.onmousedown = (e: MouseEvent) => {
      startDrag(e.clientX, e.clientY, e);
      document.onmousemove = (e: MouseEvent) => drag(e.clientX, e.clientY);
      document.onmouseup = endDrag;
    };
    
    // Touch events
    button.ontouchstart = (e: TouchEvent) => {
      const touch = e.touches[0];
      startDrag(touch.clientX, touch.clientY, e);
      document.ontouchmove = (e: TouchEvent) => {
        const touch = e.touches[0];
        drag(touch.clientX, touch.clientY);
      };
      document.ontouchend = endDrag;
    };
  };

  if (!isVisible) return null;

  return (
    <div style={{
      position: 'fixed',
      top: '10px',
      right: '10px',
      width: '300px',
      background: 'rgba(0, 0, 0, 0.9)',
      border: '2px solid #00ff88',
      borderRadius: '10px',
      padding: '15px',
      color: '#00ff88',
      fontFamily: 'Arial, sans-serif',
      fontSize: '14px',
      zIndex: 10000,
      maxHeight: '80vh',
      overflow: 'auto'
    }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '15px' }}>
        <h3 style={{ margin: 0, color: '#00ffff' }}>🎯 ترتيب الأزرار</h3>
        <button 
          onClick={onClose}
          style={{
            background: '#ff4444',
            border: 'none',
            color: 'white',
            width: '25px',
            height: '25px',
            borderRadius: '50%',
            cursor: 'pointer'
          }}
        >
          ×
        </button>
      </div>

      <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
        <button
          onClick={enableEditMode}
          disabled={isEditMode}
          style={{
            background: isEditMode ? '#666' : '#00ff88',
            color: isEditMode ? '#ccc' : '#000',
            border: 'none',
            padding: '10px',
            borderRadius: '5px',
            cursor: isEditMode ? 'not-allowed' : 'pointer',
            fontWeight: 'bold'
          }}
        >
          🔧 {isEditMode ? 'وضع التعديل مفعل' : 'تفعيل وضع التعديل'}
        </button>

        <button
          onClick={toggleGrid}
          style={{
            background: showGrid ? '#ffaa00' : '#0088ff',
            color: 'white',
            border: 'none',
            padding: '10px',
            borderRadius: '5px',
            cursor: 'pointer',
            fontWeight: 'bold'
          }}
        >
          📐 {showGrid ? 'إخفاء الشبكة' : 'عرض الشبكة'}
        </button>

        <button
          onClick={savePositions}
          disabled={!isEditMode}
          style={{
            background: !isEditMode ? '#666' : '#ff6600',
            color: !isEditMode ? '#ccc' : 'white',
            border: 'none',
            padding: '10px',
            borderRadius: '5px',
            cursor: !isEditMode ? 'not-allowed' : 'pointer',
            fontWeight: 'bold'
          }}
        >
          💾 حفظ المواقع
        </button>

        <button
          onClick={disableEditMode}
          disabled={!isEditMode}
          style={{
            background: !isEditMode ? '#666' : '#ff4444',
            color: !isEditMode ? '#ccc' : 'white',
            border: 'none',
            padding: '10px',
            borderRadius: '5px',
            cursor: !isEditMode ? 'not-allowed' : 'pointer',
            fontWeight: 'bold'
          }}
        >
          ❌ إلغاء وضع التعديل
        </button>

        <button
          onClick={reloadPage}
          style={{
            background: '#9966ff',
            color: 'white',
            border: 'none',
            padding: '10px',
            borderRadius: '5px',
            cursor: 'pointer',
            fontWeight: 'bold'
          }}
        >
          🔄 إعادة تحميل
        </button>
      </div>

      {buttonsInfo.length > 0 && (
        <div style={{
          marginTop: '15px',
          padding: '10px',
          background: 'rgba(0, 0, 0, 0.5)',
          borderRadius: '5px',
          fontSize: '12px',
          maxHeight: '200px',
          overflow: 'auto'
        }}>
          {buttonsInfo.map((info, index) => (
            <div key={index} style={{ marginBottom: '5px', whiteSpace: 'pre-wrap' }}>
              {info}
            </div>
          ))}
        </div>
      )}

      <div style={{ marginTop: '15px', fontSize: '12px', color: '#ccc' }}>
        <strong>📋 التعليمات:</strong>
        <br />1. اضغط "تفعيل وضع التعديل"
        <br />2. اضغط "عرض الشبكة" (اختياري)
        <br />3. اسحب الأزرار الخضراء لترتيبها
        <br />4. اضغط "حفظ المواقع" عند الانتهاء
      </div>
    </div>
  );
};

export default ButtonArrangementPanel;
