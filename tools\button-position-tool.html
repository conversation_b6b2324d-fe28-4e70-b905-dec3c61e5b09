<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎮 أداة تحديد مواقع الأزرار - Lucky Ocean Game</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #00ff88;
            margin-bottom: 10px;
            font-size: 2.5em;
        }

        .header p {
            color: #ccc;
            font-size: 1.2em;
        }

        .controls {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
        }

        .control-group {
            margin-bottom: 20px;
        }

        .control-group label {
            display: block;
            margin-bottom: 8px;
            color: #00ff88;
            font-weight: bold;
        }

        .control-group input, .control-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #00ff88;
            border-radius: 8px;
            background: rgba(0, 0, 0, 0.3);
            color: white;
            font-size: 16px;
        }

        .control-group input:focus, .control-group select:focus {
            outline: none;
            border-color: #00ffff;
            box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
        }

        .device-presets {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .preset-btn {
            padding: 12px;
            background: linear-gradient(45deg, #00ff88, #00ffff);
            color: #1a1a2e;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .preset-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 255, 136, 0.3);
        }

        .workspace {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 30px;
            margin-bottom: 30px;
        }

        .image-container {
            position: relative;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            min-height: 600px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .game-image {
            max-width: 100%;
            max-height: 80vh;
            border-radius: 10px;
            cursor: crosshair;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        }

        .button-marker {
            position: absolute;
            width: 20px;
            height: 20px;
            background: #ff0066;
            border: 3px solid white;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            cursor: pointer;
            z-index: 10;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(255, 0, 102, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(255, 0, 102, 0); }
            100% { box-shadow: 0 0 0 0 rgba(255, 0, 102, 0); }
        }

        .button-marker:hover {
            background: #ff3388;
            transform: translate(-50%, -50%) scale(1.2);
        }

        .sidebar {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
        }

        .button-list {
            margin-bottom: 20px;
        }

        .button-item {
            background: rgba(0, 0, 0, 0.3);
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 8px;
            border-left: 4px solid #00ff88;
        }

        .button-item.active {
            border-left-color: #ff0066;
            background: rgba(255, 0, 102, 0.2);
        }

        .button-item h4 {
            color: #00ff88;
            margin-bottom: 5px;
        }

        .button-item p {
            color: #ccc;
            font-size: 14px;
        }

        .output-section {
            background: rgba(0, 0, 0, 0.5);
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
        }

        .output-section h3 {
            color: #00ffff;
            margin-bottom: 10px;
        }

        .code-output {
            background: #000;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            color: #00ff88;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #00ff88;
        }

        .action-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 15px;
        }

        .btn {
            padding: 12px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(45deg, #00ff88, #00ffff);
            color: #1a1a2e;
        }

        .btn-secondary {
            background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .instructions {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            margin-top: 30px;
        }

        .instructions h3 {
            color: #00ffff;
            margin-bottom: 15px;
        }

        .instructions ol {
            color: #ccc;
            padding-right: 20px;
        }

        .instructions li {
            margin-bottom: 8px;
            line-height: 1.5;
        }

        @media (max-width: 768px) {
            .workspace {
                grid-template-columns: 1fr;
            }
            
            .device-presets {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎮 أداة تحديد مواقع الأزرار</h1>
            <p>Lucky Ocean Game - Button Position Tool</p>
        </div>

        <div class="controls">
            <div class="control-group">
                <label>📱 اختر نوع الجهاز:</label>
                <div class="device-presets">
                    <button class="preset-btn" onclick="setDevice(375, 667, 'iPhone SE')">iPhone SE (375×667)</button>
                    <button class="preset-btn" onclick="setDevice(390, 844, 'iPhone 12/13/14')">iPhone 12/13/14 (390×844)</button>
                    <button class="preset-btn" onclick="setDevice(428, 926, 'iPhone 14 Pro Max')">iPhone 14 Pro Max (428×926)</button>
                    <button class="preset-btn" onclick="setDevice(412, 915, 'Samsung Galaxy')">Samsung Galaxy (412×915)</button>
                </div>
            </div>

            <div class="control-group">
                <label>🖼️ ارفع صورة الخلفية:</label>
                <input type="file" id="imageUpload" accept="image/*" onchange="loadImage(event)">
            </div>

            <div class="control-group">
                <label>📐 أبعاد الجهاز:</label>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                    <input type="number" id="deviceWidth" placeholder="العرض (px)" value="390">
                    <input type="number" id="deviceHeight" placeholder="الارتفاع (px)" value="844">
                </div>
            </div>

            <div class="control-group">
                <label>🏷️ اسم الجهاز:</label>
                <input type="text" id="deviceName" placeholder="مثال: iPhone 12" value="iPhone 12/13/14">
            </div>
        </div>

        <div class="workspace">
            <div class="image-container" id="imageContainer">
                <div style="text-align: center; color: #666;">
                    <h3>📤 ارفع صورة الخلفية لبدء العمل</h3>
                    <p>اختر صورة من جهازك أو اسحبها هنا</p>
                </div>
            </div>

            <div class="sidebar">
                <h3 style="color: #00ffff; margin-bottom: 15px;">🎯 الأزرار المحددة</h3>
                <div class="button-list" id="buttonList">
                    <div class="button-item">
                        <h4>لم يتم تحديد أزرار بعد</h4>
                        <p>اضغط على الصورة لتحديد مواقع الأزرار</p>
                    </div>
                </div>

                <div class="output-section">
                    <h3>💻 الكود المُولد</h3>
                    <div class="code-output" id="codeOutput">// سيظهر الكود هنا بعد تحديد الأزرار</div>
                    <div class="action-buttons">
                        <button class="btn btn-primary" onclick="copyCode()">📋 نسخ الكود</button>
                        <button class="btn btn-secondary" onclick="clearAll()">🗑️ مسح الكل</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="instructions">
            <h3>📋 تعليمات الاستخدام</h3>
            <ol>
                <li><strong>اختر نوع الجهاز</strong> من الأزرار أعلاه أو أدخل الأبعاد يدوياً</li>
                <li><strong>ارفع صورة الخلفية</strong> المصممة لهذا الجهاز</li>
                <li><strong>اضغط على الصورة</strong> في المواقع التي تريد وضع الأزرار فيها</li>
                <li><strong>أدخل اسم الزر</strong> في النافذة المنبثقة</li>
                <li><strong>انسخ الكود المُولد</strong> واستخدمه في مشروعك</li>
                <li><strong>كرر العملية</strong> لكل جهاز وصورة</li>
            </ol>
        </div>
    </div>

    <script>
        let buttons = [];
        let currentDevice = { width: 390, height: 844, name: 'iPhone 12/13/14' };
        let imageElement = null;

        function setDevice(width, height, name) {
            currentDevice = { width, height, name };
            document.getElementById('deviceWidth').value = width;
            document.getElementById('deviceHeight').value = height;
            document.getElementById('deviceName').value = name;
            updateOutput();
        }

        function loadImage(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                const container = document.getElementById('imageContainer');
                container.innerHTML = '';
                
                const img = document.createElement('img');
                img.src = e.target.result;
                img.className = 'game-image';
                img.onclick = addButton;
                
                container.appendChild(img);
                imageElement = img;
                
                // إعادة رسم الأزرار الموجودة
                buttons.forEach(button => {
                    addMarker(button.x, button.y, button.name);
                });
            };
            reader.readAsDataURL(file);
        }

        function addButton(event) {
            if (!imageElement) return;
            
            const rect = imageElement.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;
            
            const buttonName = prompt('🏷️ أدخل اسم الزر:', `Button ${buttons.length + 1}`);
            if (!buttonName) return;
            
            const button = {
                name: buttonName,
                x: x,
                y: y,
                percentX: (x / rect.width * 100).toFixed(2),
                percentY: (y / rect.height * 100).toFixed(2)
            };
            
            buttons.push(button);
            addMarker(x, y, buttonName);
            updateButtonList();
            updateOutput();
        }

        function addMarker(x, y, name) {
            const marker = document.createElement('div');
            marker.className = 'button-marker';
            marker.style.left = x + 'px';
            marker.style.top = y + 'px';
            marker.title = name;
            marker.onclick = (e) => {
                e.stopPropagation();
                removeButton(name);
            };
            
            document.getElementById('imageContainer').appendChild(marker);
        }

        function removeButton(name) {
            buttons = buttons.filter(button => button.name !== name);
            
            // إعادة رسم الصورة والأزرار
            if (imageElement) {
                const container = document.getElementById('imageContainer');
                const markers = container.querySelectorAll('.button-marker');
                markers.forEach(marker => marker.remove());
                
                buttons.forEach(button => {
                    addMarker(button.x, button.y, button.name);
                });
            }
            
            updateButtonList();
            updateOutput();
        }

        function updateButtonList() {
            const list = document.getElementById('buttonList');
            
            if (buttons.length === 0) {
                list.innerHTML = `
                    <div class="button-item">
                        <h4>لم يتم تحديد أزرار بعد</h4>
                        <p>اضغط على الصورة لتحديد مواقع الأزرار</p>
                    </div>
                `;
                return;
            }
            
            list.innerHTML = buttons.map(button => `
                <div class="button-item" onclick="highlightButton('${button.name}')">
                    <h4>${button.name}</h4>
                    <p>الموقع: ${button.percentX}%, ${button.percentY}%</p>
                    <p>البكسل: ${Math.round(button.x)}px, ${Math.round(button.y)}px</p>
                </div>
            `).join('');
        }

        function updateOutput() {
            const output = document.getElementById('codeOutput');
            
            if (buttons.length === 0) {
                output.textContent = '// سيظهر الكود هنا بعد تحديد الأزرار';
                return;
            }
            
            const deviceKey = currentDevice.name.replace(/[^a-zA-Z0-9]/g, '');
            
            let code = `// Layout for ${currentDevice.name} (${currentDevice.width}x${currentDevice.height})\n`;
            code += `const ${deviceKey}Layout = {\n`;
            code += `  device: {\n`;
            code += `    name: "${currentDevice.name}",\n`;
            code += `    width: ${currentDevice.width},\n`;
            code += `    height: ${currentDevice.height}\n`;
            code += `  },\n`;
            code += `  buttons: {\n`;
            
            buttons.forEach((button, index) => {
                const key = button.name.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
                code += `    ${key}: {\n`;
                code += `      left: '${button.percentX}%',\n`;
                code += `      top: '${button.percentY}%',\n`;
                code += `      name: '${button.name}'\n`;
                code += `    }${index < buttons.length - 1 ? ',' : ''}\n`;
            });
            
            code += `  }\n`;
            code += `};\n\n`;
            code += `// CSS Media Query\n`;
            code += `@media screen and (width: ${currentDevice.width}px) and (height: ${currentDevice.height}px) {\n`;
            
            buttons.forEach(button => {
                const key = button.name.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
                code += `  .${key}-button {\n`;
                code += `    left: ${button.percentX}% !important;\n`;
                code += `    top: ${button.percentY}% !important;\n`;
                code += `  }\n`;
            });
            
            code += `}`;
            
            output.textContent = code;
        }

        function copyCode() {
            const output = document.getElementById('codeOutput');
            navigator.clipboard.writeText(output.textContent).then(() => {
                alert('✅ تم نسخ الكود بنجاح!');
            });
        }

        function clearAll() {
            if (confirm('🗑️ هل أنت متأكد من مسح جميع الأزرار؟')) {
                buttons = [];
                const markers = document.querySelectorAll('.button-marker');
                markers.forEach(marker => marker.remove());
                updateButtonList();
                updateOutput();
            }
        }

        function highlightButton(name) {
            // إزالة التمييز السابق
            document.querySelectorAll('.button-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // تمييز الزر المحدد
            event.target.closest('.button-item').classList.add('active');
        }

        // تحديث الجهاز عند تغيير الأبعاد
        document.getElementById('deviceWidth').addEventListener('input', updateDeviceFromInputs);
        document.getElementById('deviceHeight').addEventListener('input', updateDeviceFromInputs);
        document.getElementById('deviceName').addEventListener('input', updateDeviceFromInputs);

        function updateDeviceFromInputs() {
            currentDevice.width = parseInt(document.getElementById('deviceWidth').value) || 390;
            currentDevice.height = parseInt(document.getElementById('deviceHeight').value) || 844;
            currentDevice.name = document.getElementById('deviceName').value || 'Custom Device';
            updateOutput();
        }
    </script>
</body>
</html>
