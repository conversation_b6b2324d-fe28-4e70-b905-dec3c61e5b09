import { BetsOnTypes } from '../types/game';
import { ACTIVE_GAME_SQUARES } from '../constants/gameConfig';
import { SMOOTH_LIGHT_PATH, LUCKY_SETTINGS, SAFETY_SETTINGS } from './gameConstants';

// نظام اختيار الموقع الفائز الجديد - يختار فقط من المسار الصحيح
export const selectWinningPosition = (betsOnTypes: BetsOnTypes): number => {
  console.log('🎯 بدء اختيار الموقع الفائز...');
  console.log('📊 الرهانات الحالية:', betsOnTypes);
  
  // 1. إنشاء قائمة بالمواقع المتاحة (فقط المربعات الموجودة في المسار)
  const availablePositions = ACTIVE_GAME_SQUARES
    .filter(square => SMOOTH_LIGHT_PATH.includes(square.gridIndex)) // فقط المربعات في المسار
    .map((square, index) => ({
      position: square.gridIndex,
      symbol: square.symbol,
      index: index,
      betAmount: betsOnTypes[square.symbol as keyof BetsOnTypes] || 0,
      type: square.type
    }));
  
  console.log('📍 المربعات في المسار:', availablePositions.map(p => `${p.position}(${p.symbol}-${p.type})`));
  
  // 2. تصنيف المواقع إلى آمنة وخطرة
  const safePositions = availablePositions.filter(pos => pos.betAmount === 0);
  const riskyPositions = availablePositions.filter(pos => pos.betAmount > 0);
  
  // 3. تصنيف المواقع حسب النوع
  const luckyPositions = availablePositions.filter(pos => 
    pos.type === 'luckyDoubleText' || pos.type === 'luckyTripleText'
  );
  const normalPositions = availablePositions.filter(pos => 
    pos.type !== 'luckyDoubleText' && pos.type !== 'luckyTripleText'
  );
  
  console.log('✅ المواقع الآمنة:', safePositions.map(p => `${p.position}(${p.symbol})`));
  console.log('⚠️ المواقع الخطرة:', riskyPositions.map(p => `${p.position}(${p.symbol})`));
  console.log('🍀 مواقع اللاكي:', luckyPositions.map(p => `${p.position}(${p.symbol}-${p.type})`));
  
  // 4. اختيار الموقع الفائز
  let selectedPosition: typeof availablePositions[0];
  
  // إعطاء أولوية للاكي (30% من الحالات)
  const shouldChooseLucky = Math.random() < LUCKY_SETTINGS.LUCKY_CHANCE && luckyPositions.length > 0;
  
  if (shouldChooseLucky) {
    // اختيار موقع لاكي عشوائياً
    const randomIndex = Math.floor(Math.random() * luckyPositions.length);
    selectedPosition = luckyPositions[randomIndex];
    console.log('🍀 تم اختيار موقع لاكي عشوائياً');
  } else if (safePositions.length > 0) {
    // إذا كانت هناك مواقع آمنة، اختر منها عشوائياً
    const randomIndex = Math.floor(Math.random() * safePositions.length);
    selectedPosition = safePositions[randomIndex];
    console.log('🎲 تم اختيار موقع آمن عشوائياً');
  } else {
    // إذا لم تكن هناك مواقع آمنة، اختر من الخطرة
    const randomIndex = Math.floor(Math.random() * riskyPositions.length);
    selectedPosition = riskyPositions[randomIndex];
    console.log('⚠️ تم اختيار موقع خطير (لا توجد مواقع آمنة)');
  }
  
  console.log(`🏆 الموقع المختار: ${selectedPosition.position} (${selectedPosition.symbol})`);
  console.log(`💰 الرهان على هذا الرمز: ${selectedPosition.betAmount}`);
  
  return selectedPosition.position;
};

// نظام اختيار محسن مع حماية مالية متقدمة
export const selectWinningPositionAdvanced = (betsOnTypes: BetsOnTypes): number => {
  const availablePositions = ACTIVE_GAME_SQUARES
    .filter(square => SMOOTH_LIGHT_PATH.includes(square.gridIndex))
    .map((square, index) => ({
      position: square.gridIndex,
      symbol: square.symbol,
      index: index,
      betAmount: betsOnTypes[square.symbol as keyof BetsOnTypes] || 0,
      type: square.type,
      riskScore: calculateRiskScore(square.symbol, betsOnTypes),
      safetyScore: calculateSafetyScore(square.symbol, betsOnTypes)
    }));

  // ترتيب المواقع حسب نقاط الأمان (الأعلى أولاً)
  availablePositions.sort((a, b) => b.safetyScore - a.safetyScore);

  // اختيار من أفضل 3 مواقع آمنة
  const topSafePositions = availablePositions.slice(0, 3);
  const randomIndex = Math.floor(Math.random() * topSafePositions.length);
  
  return topSafePositions[randomIndex].position;
};

// حساب نقاط المخاطر
function calculateRiskScore(symbol: string, betsOnTypes: BetsOnTypes): number {
  const betAmount = betsOnTypes[symbol as keyof BetsOnTypes] || 0;
  return betAmount * SAFETY_SETTINGS.RISK_PENALTY_MULTIPLIER;
}

// حساب نقاط الأمان
function calculateSafetyScore(symbol: string, betsOnTypes: BetsOnTypes): number {
  const betAmount = betsOnTypes[symbol as keyof BetsOnTypes] || 0;
  let score = SAFETY_SETTINGS.DIVERSITY_BONUS;
  
  // خصم نقاط للرموز المراهن عليها
  if (betAmount > 0) {
    score -= SAFETY_SETTINGS.BET_PENALTY;
  }
  
  return Math.max(0, score);
}
