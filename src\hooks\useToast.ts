import { useState, useCallback } from 'react';

interface ToastState {
  id: string;
  message: string;
  type: 'success' | 'error' | 'warning' | 'info';
  duration?: number;
}

/**
 * Custom hook لإدارة Toast notifications
 */
export const useToast = () => {
  const [toasts, setToasts] = useState<ToastState[]>([]);

  // إضافة toast جديد
  const showToast = useCallback((
    message: string, 
    type: 'success' | 'error' | 'warning' | 'info' = 'info',
    duration: number = 3000
  ) => {
    const id = Date.now().toString();
    const newToast: ToastState = { id, message, type, duration };
    
    setToasts(prev => [...prev, newToast]);
    
    // إزالة التوست تلقائياً بعد المدة المحددة
    setTimeout(() => {
      removeToast(id);
    }, duration);
  }, []);

  // إزالة toast محدد
  const removeToast = useCallback((id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  }, []);

  // إزالة جميع التوستات
  const clearAllToasts = useCallback(() => {
    setToasts([]);
  }, []);

  // دوال مساعدة لأنواع مختلفة من التوستات
  const showSuccess = useCallback((message: string, duration?: number) => {
    showToast(message, 'success', duration);
  }, [showToast]);

  const showError = useCallback((message: string, duration?: number) => {
    showToast(message, 'error', duration);
  }, [showToast]);

  const showWarning = useCallback((message: string, duration?: number) => {
    showToast(message, 'warning', duration);
  }, [showToast]);

  const showInfo = useCallback((message: string, duration?: number) => {
    showToast(message, 'info', duration);
  }, [showToast]);

  return {
    toasts,
    showToast,
    removeToast,
    clearAllToasts,
    showSuccess,
    showError,
    showWarning,
    showInfo
  };
};
