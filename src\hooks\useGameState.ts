import { useState, useEffect, useCallback } from 'react';
import { GameState, BetsOnTypes } from '../types/game';
import {
  BETTING_PHASE_DURATION,
  LIGHT_ANIMATION_DURATION,
  RESULT_DISPLAY_DURATION,
  TOTAL_ROUND_DURATION,
  STARTING_BALANCE,
  ACTIVE_GAME_SQUARES,
  LIGHT_CYCLES
} from '../constants/gameConfig';

// مسار الضوء الجديد - فقط المربعات النشطة
const LIGHT_PATH = ACTIVE_GAME_SQUARES.map(square => square.gridIndex);
import {
  calculateWin,
  generateLightSequence,
  determineFinalSelection,
  calculateLightSpeed,
  selectWinningPosition,
  getRandomSymbol,
  generateLuckyAnimation,
  calculateLuckyMultiWin
} from '../utils/gameLogicNew';
import { gameEconomics } from '../utils/economics';

/**
 * Custom hook لإدارة حالة اللعبة
 * يفصل منطق اللعبة عن المكون الرئيسي لتحسين الأداء والقابلية للصيانة
 */
export const useGameState = () => {
  const [gameState, setGameState] = useState<GameState>({
    balance: STARTING_BALANCE,
    betsOnTypes: { 'APPLE': 0, 'BANANA': 0, 'LEMON': 0, 'WATERMELON': 0, 'BAR': 0 },
    isSpinning: false,
    countdown: BETTING_PHASE_DURATION,
    selectedSquares: [],
    currentBetValueToApply: 0,
    collectedSymbols: [],
    messages: [],
    totalWinAmount: 0,
    gamePhase: 'betting',
    lightPosition: -1,
    lightPath: LIGHT_PATH,
    isLightAnimating: false,
    roundStartTime: Date.now()
  });

  const [pendingBetsOnTypes, setPendingBetsOnTypes] = useState<BetsOnTypes>({ 
    'APPLE': 0, 'BANANA': 0, 'LEMON': 0, 'WATERMELON': 0, 'BAR': 0 
  });
  
  const [gameHistory, setGameHistory] = useState<string[][]>([]);
  const [lightAnimationInterval, setLightAnimationInterval] = useState<number | null>(null);

  // تحديث مؤقت اللعبة
  const updateGameTimer = useCallback(() => {
    setGameState(prev => {
      const elapsedTime = Date.now() - prev.roundStartTime;
      const remainingTime = TOTAL_ROUND_DURATION * 1000 - elapsedTime;
      
      if (remainingTime <= 0) {
        // بدء جولة جديدة
        setPendingBetsOnTypes({ 'APPLE': 0, 'BANANA': 0, 'LEMON': 0, 'WATERMELON': 0, 'BAR': 0 });
        return {
          ...prev,
          roundStartTime: Date.now(),
          gamePhase: 'betting',
          countdown: BETTING_PHASE_DURATION,
          isLightAnimating: false,
          lightPosition: -1,
          selectedSquares: [],
          collectedSymbols: [],
          messages: [],
          totalWinAmount: 0,
          betsOnTypes: { 'APPLE': 0, 'BANANA': 0, 'LEMON': 0, 'WATERMELON': 0, 'BAR': 0 }
        };
      }
      
      // إدارة انتقالات المراحل
      if (prev.gamePhase === 'betting') {
        const bettingTimeLeft = remainingTime - (LIGHT_ANIMATION_DURATION + RESULT_DISPLAY_DURATION) * 1000;
        
        if (bettingTimeLeft <= 0) {
          // بدء مرحلة الإضاءة - لكن فقط إذا لم تكن بدأت بالفعل من handleSpin
          console.log('⏰ Timer: انتهت فترة المراهنة');
          setPendingBetsOnTypes({ 'APPLE': 0, 'BANANA': 0, 'LEMON': 0, 'WATERMELON': 0, 'BAR': 0 });

          // استدعاء startLightAnimation فقط إذا لم يكن هناك رهانات (لم يضغط اللاعب Spin)
          const totalBets = Object.values(prev.betsOnTypes).reduce((sum, bet) => sum + bet, 0);
          if (totalBets === 0) {
            console.log('⏰ Timer: لا توجد رهانات، بدء لعبة فارغة');
            startLightAnimation({ 'APPLE': 0, 'BANANA': 0, 'LEMON': 0, 'WATERMELON': 0, 'BAR': 0 });
          }
          return {
            ...prev,
            gamePhase: 'light_animation',
            countdown: 0,
            isLightAnimating: true
          };
        }
        
        return {
          ...prev,
          countdown: Math.ceil(bettingTimeLeft / 1000)
        };
      }
      
      if (prev.gamePhase === 'light_animation') {
        if (remainingTime <= RESULT_DISPLAY_DURATION * 1000) {
          return {
            ...prev,
            gamePhase: 'result_display',
            countdown: 0,
            isLightAnimating: false
          };
        }
        
        return {
          ...prev,
          countdown: 0
        };
      }
      
      return {
        ...prev,
        countdown: 0
      };
    });
  }, []);

  // بدء تحريك الإضاءة
  const startLightAnimation = useCallback((confirmedBets: BetsOnTypes) => {
    if (lightAnimationInterval) clearInterval(lightAnimationInterval);

    // اختيار الموقع الفائز بذكاء بناءً على الرهانات المؤكدة
    const targetPosition = selectWinningPosition(confirmedBets);
    
    // التحقق من نوع المربع الفائز
    const squareIndex = ACTIVE_GAME_SQUARES.findIndex(sq => sq.gridIndex === targetPosition);
    const winningSquare = ACTIVE_GAME_SQUARES[squareIndex];
    
    console.log(`🎯 startLightAnimation - targetPosition: ${targetPosition}`);
    console.log(`🎯 startLightAnimation - winningSquare:`, winningSquare);
    
    // إذا كان المربع الفائز لاكي، استخدم الحركة المتعددة
    if (winningSquare && (winningSquare.type === 'luckyDoubleText' || winningSquare.type === 'luckyTripleText')) {
      console.log(`🍀 LUCKY detected: ${winningSquare.type} - starting lucky animation`);
      startLuckyMultiAnimation(confirmedBets, winningSquare.type);
      return;
    }
    
    // الحركة العادية للمربعات الأخرى
    console.log(`🎯 Normal animation for: ${winningSquare?.symbol} (${winningSquare?.type})`);
    startNormalAnimation(confirmedBets, targetPosition);
  }, [lightAnimationInterval]);

  // دالة الحركة العادية
  const startNormalAnimation = useCallback((confirmedBets: BetsOnTypes, targetPosition: number) => {
    const currentLightPosition = gameState.lightPosition >= 0 ? gameState.lightPosition : undefined;
    const lightSequence = generateLightSequence(targetPosition, currentLightPosition);
    let currentIndex = 0;
    
    const animateStep = () => {
      if (currentIndex >= lightSequence.length) {
        // انتهاء التحريك، تحديد النتيجة النهائية
        const finalLightPosition = lightSequence[lightSequence.length - 1];
        const finalSelectedIndices = determineFinalSelection(finalLightPosition);
        // إذا كان الفائز Lucky، لا تعرض رسالة أو تحدث التاريخ هنا، بل انتظر حركة LuckyMulti
        const isLucky = finalSelectedIndices.some(i => {
          const sq = ACTIVE_GAME_SQUARES[i];
          return sq.type === 'luckyDoubleText' || sq.type === 'luckyTripleText';
        });
        if (isLucky) {
          setGameState(prev => ({
            ...prev,
            selectedSquares: finalSelectedIndices.map(i => ACTIVE_GAME_SQUARES[i].gridIndex),
            lightPosition: finalLightPosition,
            isLightAnimating: false
          }));
          setLightAnimationInterval(null);
          return;
        }
        setGameState(prev => {
          const result = calculateWin(finalSelectedIndices, prev.betsOnTypes);
          // تسجيل النتيجة في النظام الاقتصادي
          gameEconomics.recordRoundResult(prev.betsOnTypes, result.totalWinAmount);
          // تحديث تاريخ الألعاب
          setGameHistory(historyPrev => {
            const newHistory = [result.collectedSymbols, ...historyPrev];
            return newHistory.slice(0, 20);
          });
          return {
            ...prev,
            selectedSquares: finalSelectedIndices.map(i => ACTIVE_GAME_SQUARES[i].gridIndex),
            collectedSymbols: result.collectedSymbols,
            messages: result.messages,
            totalWinAmount: result.totalWinAmount,
            balance: prev.balance + result.totalWinAmount,
            lightPosition: finalLightPosition,
            isLightAnimating: false
          };
        });
        setLightAnimationInterval(null);
        return;
      }
      // تحديث موقع الضوء بشكل سلس
      setGameState(prev => ({
        ...prev,
        lightPosition: lightSequence[currentIndex]
      }));
      currentIndex++;
      // حساب السرعة التالية مع تحسين السلسة
      const nextSpeed = calculateLightSpeed(currentIndex, lightSequence.length);
      const smoothDelay = Math.max(nextSpeed, 80);
      const nextTimeout = setTimeout(animateStep, smoothDelay);
      setLightAnimationInterval(nextTimeout);
    };
    animateStep();
  }, [lightAnimationInterval]);

  // دالة الحركة المتعددة للاكي - النظام الجديد
  const startLuckyMultiAnimation = useCallback((confirmedBets: BetsOnTypes, luckyType: 'luckyDoubleText' | 'luckyTripleText') => {
    console.log(`🍀 بدء حركة اللاكي الجديدة: ${luckyType}`);

    setGameState(prev => ({
      ...prev,
      gamePhase: 'light_animation',
      isLightAnimating: true
    }));

    // العثور على موقع اللاكي
    const luckySquare = ACTIVE_GAME_SQUARES.find(sq => sq.type === luckyType);
    if (!luckySquare) {
      console.error('❌ لم يتم العثور على مربع اللاكي');
      return;
    }

    const luckyPosition = luckySquare.gridIndex;
    console.log(`🍀 موقع اللاكي: ${luckyPosition}`);

    // الحصول على المواقع الفائزة (بدون اللاكي)
    const { sequences, finalPositions } = generateLuckyAnimation(luckyType);
    console.log(`🎯 المواقع الفائزة:`, finalPositions);

    let currentSequenceIndex = 0;
    let currentStepIndex = 0;

    const animateLuckyStep = () => {
      if (currentSequenceIndex >= sequences.length) {
        console.log(`🏁 انتهاء جميع الحركات، حساب النتيجة النهائية`);

        // تأخير إضافي قبل عرض النتائج لضمان انتهاء جميع الحركات
        setTimeout(() => {
          setGameState(prev => {
            const result = calculateLuckyMultiWin(finalPositions, prev.betsOnTypes);
            gameEconomics.recordRoundResult(prev.betsOnTypes, result.totalWinAmount);
            setGameHistory(historyPrev => {
              const newHistory = [result.collectedSymbols, ...historyPrev];
              return newHistory.slice(0, 20);
            });
            return {
              ...prev,
              gamePhase: 'result_display',
              selectedSquares: finalPositions, // فقط المواقع الفائزة (بدون اللاكي)
              collectedSymbols: result.collectedSymbols,
              messages: result.messages,
              totalWinAmount: result.totalWinAmount,
              balance: prev.balance + result.totalWinAmount,
              lightPosition: luckyPosition, // اللاكي يبقى مضيء
              lightPositions: finalPositions, // المواقع الفائزة للعرض
              isLightAnimating: false
            };
          });
        }, 1000); // تأخير ثانية واحدة قبل عرض النتائج

        setLightAnimationInterval(null);
        setTimeout(() => {
          setGameState(prev => ({
            ...prev,
            gamePhase: 'betting',
            countdown: Math.ceil(BETTING_PHASE_DURATION / 1000),
            selectedSquares: [],
            collectedSymbols: [],
            messages: [],
            totalWinAmount: 0,
            lightPosition: -1,
            lightPositions: [],
            betsOnTypes: { 'APPLE': 0, 'BANANA': 0, 'LEMON': 0, 'WATERMELON': 0, 'BAR': 0 }
          }));
        }, RESULT_DISPLAY_DURATION + 1000); // إضافة ثانية إضافية للعرض
        return;
      }

      const currentSequence = sequences[currentSequenceIndex];
      if (currentStepIndex >= currentSequence.length) {
        currentSequenceIndex++;
        currentStepIndex = 0;
        // تأخير واضح بين كل حركة (500ms)
        const nextTimeout = setTimeout(animateLuckyStep, 500);
        setLightAnimationInterval(nextTimeout);
        return;
      }

      // تحديث موقع الضوء - يتحرك بين المواقع الفائزة فقط
      const newPosition = currentSequence[currentStepIndex];
      setGameState(prev => ({
        ...prev,
        lightPosition: newPosition,
        lightPositions: [luckyPosition, ...finalPositions.slice(0, currentSequenceIndex + 1)] // اللاكي + المواقع الفائزة حتى الآن
      }));
      currentStepIndex++;
      // سرعة الحركة
      const nextSpeed = calculateLightSpeed(currentStepIndex, currentSequence.length);
      const smoothDelay = Math.max(nextSpeed, 100);
      const nextTimeout = setTimeout(animateLuckyStep, smoothDelay);
      setLightAnimationInterval(nextTimeout);
    };

    // بدء الحركة مع إضاءة اللاكي أولاً
    setGameState(prev => ({
      ...prev,
      lightPosition: luckyPosition,
      lightPositions: [luckyPosition]
    }));

    // إضاءة اللاكي لمدة ثانية واحدة ثم بدء اختيار العناصر الفائزة
    setTimeout(() => {
      console.log(`🎯 بدء اختيار العناصر الفائزة بعد إضاءة اللاكي`);
      animateLuckyStep();
    }, 1000); // إضاءة اللاكي لمدة ثانية كاملة
  }, [lightAnimationInterval]);

  // تحديد قيمة الرهان
  const handleBetValueSelect = useCallback((value: number) => {
    setGameState(prev => ({ ...prev, currentBetValueToApply: value }));
  }, []);

  // الرهان على رمز
  const handleSymbolBet = useCallback((symbol: keyof BetsOnTypes) => {
    if (gameState.currentBetValueToApply === 0 || gameState.balance < gameState.currentBetValueToApply) {
      return;
    }
    
    if (gameState.gamePhase !== 'betting') {
      return;
    }
    
    setPendingBetsOnTypes(prev => ({
      ...prev,
      [symbol]: prev[symbol] + gameState.currentBetValueToApply
    }));
  }, [gameState.currentBetValueToApply, gameState.balance, gameState.gamePhase]);

  // تأكيد الرهان
  const handleSpin = useCallback(() => {
    if (gameState.gamePhase !== 'betting') {
      return;
    }
    
    const totalPendingBet = Object.values(pendingBetsOnTypes).reduce((sum, bet) => sum + bet, 0);
    if (totalPendingBet === 0 || gameState.balance < totalPendingBet) {
      return;
    }
    
    // التحقق من حدود الخسائر
    const lossCheck = gameEconomics.checkLossLimits(pendingBetsOnTypes);
    if (!lossCheck.canProceed) {
      console.log('🚨 تم رفض الرهان:', lossCheck.reason);
      // يمكن إضافة رسالة تنبيه هنا
      return;
    }
    
    console.log('🎮 handleSpin: تحديث betsOnTypes إلى:', pendingBetsOnTypes);
    console.log('💰 تحليل المخاطر:', {
      dailyLoss: lossCheck.dailyLoss,
      maxSingleLoss: lossCheck.maxSingleLoss,
      canProceed: lossCheck.canProceed
    });

    // استدعاء النظام الذكي مباشرة مع الرهانات الجديدة
    console.log('🚀 handleSpin: استدعاء startLightAnimation مع الرهانات الجديدة');
    startLightAnimation(pendingBetsOnTypes);

    setGameState(prev => ({
      ...prev,
      betsOnTypes: { ...pendingBetsOnTypes },
      balance: prev.balance - totalPendingBet
    }));
    setPendingBetsOnTypes({ 'APPLE': 0, 'BANANA': 0, 'LEMON': 0, 'WATERMELON': 0, 'BAR': 0 });
  }, [gameState.gamePhase, gameState.balance, pendingBetsOnTypes]);

  // تحميل الرصيد من localStorage
  useEffect(() => {
    const savedBalance = localStorage.getItem('player_balance');
    if (savedBalance) {
      setGameState(prev => ({ ...prev, balance: parseInt(savedBalance, 10) }));
    }
  }, []);

  // حفظ الرصيد في localStorage
  useEffect(() => {
    localStorage.setItem('player_balance', gameState.balance.toString());
  }, [gameState.balance]);

  // تشغيل مؤقت اللعبة
  useEffect(() => {
    const interval = setInterval(updateGameTimer, 100);
    
    return () => {
      if (interval) clearInterval(interval);
      if (lightAnimationInterval) clearInterval(lightAnimationInterval);
    };
  }, [updateGameTimer, lightAnimationInterval]);

  // بدء تحريك الإضاءة عند التحميل
  useEffect(() => {
    const timer = setTimeout(() => {
      const emptyBets = { 'APPLE': 0, 'BANANA': 0, 'LEMON': 0, 'WATERMELON': 0, 'BAR': 0 };
      startLightAnimation(emptyBets);
    }, 100);

    return () => clearTimeout(timer);
  }, [startLightAnimation]);

  return {
    gameState,
    pendingBetsOnTypes,
    gameHistory,
    handleBetValueSelect,
    handleSymbolBet,
    handleSpin
  };
};
