import React from 'react';
import { SquareConfig } from '../types/game';
import { BOARD_SQUARES_CONFIG, ACTIVE_GAME_SQUARES } from '../constants/gameConfig';
import { COLORS } from '../theme/colors';
import { BUTTON_POSITIONS, GameBoardSettings } from '../utils/buttonPositions';

interface GameBoardProps {
  countdown: number;
  selectedSquares: number[];
  setSelectedSquares?: (fn: (prev: number[]) => number[]) => void;
  isSpinning: boolean;
  animatingSquares?: number[];
  animationSymbols?: { [key: number]: string };
  lightPosition?: number;
  lightPositions?: number[]; // جديد: مصفوفة مواقع الحلقات
  isLightAnimating?: boolean;
  gamePhase: string;
}

const GameBoard: React.FC<GameBoardProps> = ({
  countdown,
  lightPosition = -1,
  lightPositions,
  gamePhase,
  selectedSquares,
  setSelectedSquares,
  isLightAnimating = false
}) => {
  // الحصول على إعدادات الجهاز الحالي
  const getCurrentDeviceSettings = (): GameBoardSettings => {
    const width = typeof window !== 'undefined' ? window.innerWidth : 428;
    const height = typeof window !== 'undefined' ? window.innerHeight : 926;

    // البحث عن أقرب تكوين جهاز
    let deviceConfig = BUTTON_POSITIONS.find(d => {
      const widthMatch = Math.abs(d.device.width - width) < 50;
      const heightMatch = Math.abs(d.device.height - height) < 50;
      return widthMatch && heightMatch;
    });

    // إذا لم نجد تطابق، استخدم التكوين الأول كافتراضي
    if (!deviceConfig) {
      deviceConfig = BUTTON_POSITIONS[0];
    }

    // إرجاع إعدادات مربعات اللعبة أو إعدادات افتراضية
    const settings = deviceConfig.gameBoardSettings || {
      gap: '8px',
      padding: '8px',
      squareSize: {
        minWidth: '70px', // توحيد الحجم الافتراضي
        minHeight: '70px', // توحيد الحجم الافتراضي
      },
      gridAdjustments: {
        firstRowMarginTop: '8px',
        secondRowMarginTop: '6px',
        firstColumnMarginLeft: '-5px', // تحريك أكثر لليسار
      },
    };

    return settings;
  };

  const settings = getCurrentDeviceSettings();
  const fruitSymbolToImage: { [symbol: string]: string } = {
    'APPLE': '3.png',
    'BANANA': '6.png',
    'LEMON': '8.png',
    'WATERMELON': '12.png',
    'BAR': '30.png',
  };

  const renderSquareContent = (config: SquareConfig) => {
    const isLightHighlighted = lightPosition === config.gridIndex;

    if (config.type === 'normal' || config.type === 'halfFruit' || config.type === 'stackedBar') {
      const isHighlighted = lightPosition === config.gridIndex;
      const isSelected = selectedSquares && selectedSquares.includes(config.gridIndex);
      const handleSelect = () => {
        if (!setSelectedSquares) return;
        setSelectedSquares(prev =>
          prev.includes(config.gridIndex)
            ? prev.filter(i => i !== config.gridIndex)
            : [...prev, config.gridIndex]
        );
      };
      return (
        <div
          className="w-full h-full flex items-center justify-center relative transition-all duration-300"
          style={{
            background: 'transparent',
            border: 'none',
            borderRadius: '0',
            display: 'flex',
          }}
        >
          {/* رقم المربع - للضبط */}
          <div
            style={{
              position: 'absolute',
              top: '2px',
              left: '2px',
              fontSize: '8px',
              color: '#FFD700',
              fontWeight: 'bold',
              zIndex: 12,
              textShadow: '0 1px 2px rgba(0,0,0,0.8)',
            }}
          >
            {config.gridIndex}
          </div>
          
          {/* زر شفاف يغطي المربع بالكامل */}
          <button
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              background: 'transparent',
              border: 'none',
              boxShadow: 'none',
              cursor: 'pointer',
              zIndex: 10,
              padding: 0,
              margin: 0,
            }}
            onClick={handleSelect}
          />
          {/* تأثير التوهج عند التحديد فقط */}
          {isSelected && (
            <div
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                border: '4px solid #FFD700',
                borderRadius: '0',
                boxShadow: '0 0 24px 8px #FFD70088',
                pointerEvents: 'none',
                zIndex: 11,
              }}
            />
          )}
          {isHighlighted && gamePhase !== 'result_display' && (
            <div
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                border: '4px solid',
                borderImage: 'linear-gradient(90deg, #FFD700, #FFA500) 1',
                borderRadius: '0',
                boxShadow: '0 0 32px 12px #FFD70088, 0 0 32px 12px #FFA50066',
                pointerEvents: 'none',
                zIndex: 2,
              }}
            />
          )}
          
          {/* تأثيرات احتفالية للمربعات الفائزة */}
          {isHighlighted && (gamePhase === 'result_display' || gamePhase === 'light_animation') && (
            <React.Fragment>
              {/* تأثير توهج هادئ */}
              <div
                className="celebration-glow"
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: '100%',
                  border: '2px solid #FFE066', // لون ذهبي فاتح
                  borderRadius: '0',
                  boxShadow: '0 0 16px 4px #FFE06666, 0 0 24px 8px #FFD70044', // ظل أخف
                  pointerEvents: 'none',
                  zIndex: 15,
                  animation: 'celebrationGlow 2.5s ease-in-out infinite', // أبطأ
                }}
              />
              {/* تأثير نبض هادئ */}
              <div
                className="celebration-pulse"
                style={{
                  position: 'absolute',
                  top: '-5px',
                  left: '-5px',
                  width: 'calc(100% + 10px)',
                  height: 'calc(100% + 10px)',
                  border: '2px solid #FFE066',
                  borderRadius: '4px',
                  pointerEvents: 'none',
                  zIndex: 14,
                  animation: 'celebrationPulse 3s ease-in-out infinite', // أبطأ
                }}
              />
              {/* جسيمات هادئة */}
              <div
                className="celebration-particles"
                style={{
                  position: 'absolute',
                  top: '-10px',
                  left: '-10px',
                  width: 'calc(100% + 20px)',
                  height: 'calc(100% + 20px)',
                  pointerEvents: 'none',
                  zIndex: 16,
                }}
              >
                {[...Array(4)].map((_, i) => (
                  <div
                    key={`particle-${i}`}
                    style={{
                      position: 'absolute',
                      width: '4px',
                      height: '4px',
                      background: ['#FFE066', '#FFD700'][i % 2], // ألوان هادئة
                      borderRadius: '50%',
                      opacity: 0.5,
                      animation: `celebrationParticle 4s ease-in-out infinite`, // أبطأ
                      animationDelay: `${i * 0.4}s`,
                      top: '50%',
                      left: '50%',
                      transform: 'translate(-50%, -50%)',
                    }}
                  />
                ))}
              </div>
              {/* تلألؤ هادئ */}
              <div
                className="celebration-shimmer"
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: '100%',
                  background: 'linear-gradient(45deg, transparent 30%, rgba(255, 224, 102, 0.15) 50%, transparent 70%)',
                  pointerEvents: 'none',
                  zIndex: 17,
                  animation: 'celebrationShimmer 3s ease-in-out infinite', // أبطأ
                }}
              />
            </React.Fragment>
          )}
        </div>
      );
    }

    if (config.type === 'luckyDoubleText' || config.type === 'luckyTripleText') {
      let luckyFontSize = '1.3rem';
      if (config.gridIndex === 10) {
        luckyFontSize = '0.75rem';
      } else if (config.gridIndex === 14) {
        luckyFontSize = '0.85rem';
      } else {
        luckyFontSize = '0.85rem';
      }
      
      const isHighlighted = lightPosition === config.gridIndex;
      return (
        <div
             className="w-full h-full flex items-center justify-center relative"
             style={{
               background: 'transparent',
               border: 'none',
               boxShadow: 'none',
               borderRadius: '0',
               overflow: 'hidden',
             }}>
          {config.gridIndex === 10 ? (
            <>
              {[...Array(8)].map((_, i) => (
                <div key={i} style={{
                  position: 'absolute',
                  left: '50%',
                  top: '50%',
                  width: '2px',
                  height: '60%',
                  background: 'linear-gradient(to top, #fff 60%, transparent 100%)',
                  transform: `translate(-50%, -100%) rotate(${i * 45}deg)`,
                  opacity: 0.18,
                  borderRadius: '1px',
                  zIndex: 1
                }} />
              ))}
            </>
          ) : config.gridIndex === 14 ? (
            <>
              {[...Array(8)].map((_, i) => (
                <div key={i} style={{
                  position: 'absolute',
                  left: '50%',
                  top: '50%',
                  width: '2px',
                  height: '60%',
                  background: 'linear-gradient(to top, #FFD700 60%, transparent 100%)',
                  transform: `translate(-50%, -100%) rotate(${i * 45}deg)`,
                  opacity: 0.18,
                  borderRadius: '1px',
                  zIndex: 1
                }} />
              ))}
            </>
          ) : (
            <div className="absolute inset-0 pointer-events-none" style={{
              background: 'linear-gradient(120deg, rgba(255,255,255,0.10) 0%, rgba(255,255,255,0.03) 60%, rgba(255,255,255,0.10) 100%)',
              zIndex: 1
            }}></div>
          )}
          
          {/* تأثيرات احتفالية للمربعات اللاكي */}
          {isHighlighted && gamePhase === 'result_display' && (
            <>
              {/* تأثير التوهج الاحتفالي للاكي */}
              <div
                className="lucky-celebration-glow"
                style={{
                  position: 'absolute',
                  top: '-5px',
                  left: '-5px',
                  width: 'calc(100% + 10px)',
                  height: 'calc(100% + 10px)',
                  border: '3px solid #FFD700',
                  borderRadius: '4px',
                  boxShadow: '0 0 50px 20px #FFD700BB, 0 0 70px 25px #FFA50099, 0 0 90px 30px #FF880077',
                  pointerEvents: 'none',
                  zIndex: 20,
                  animation: 'luckyCelebrationGlow 2s ease-in-out infinite alternate',
                }}
              />
              
              {/* جسيمات ذهبية للاكي */}
              <div
                className="lucky-golden-particles"
                style={{
                  position: 'absolute',
                  top: '-30px',
                  left: '-30px',
                  width: 'calc(100% + 60px)',
                  height: 'calc(100% + 60px)',
                  pointerEvents: 'none',
                  zIndex: 21,
                }}
              >
                {[...Array(12)].map((_, i) => (
                  <div
                    key={`lucky-particle-${i}`}
                    style={{
                      position: 'absolute',
                      width: '8px',
                      height: '8px',
                      background: '#FFD700',
                      borderRadius: '50%',
                      animation: `luckyParticle 4s ease-in-out infinite`,
                      animationDelay: `${i * 0.3}s`,
                      top: '50%',
                      left: '50%',
                      transform: 'translate(-50%, -50%)',
                      boxShadow: '0 0 10px 2px #FFD700',
                    }}
                  />
                ))}
              </div>
              
              {/* تأثير التلألؤ الذهبي */}
              <div
                className="lucky-golden-shimmer"
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: '100%',
                  background: 'linear-gradient(45deg, transparent 20%, rgba(255, 215, 0, 0.4) 50%, transparent 80%)',
                  pointerEvents: 'none',
                  zIndex: 22,
                  animation: 'luckyGoldenShimmer 3s ease-in-out infinite',
                }}
              />
            </>
          )}
        </div>
      );
    }

    if (config.type === 'centerCountdownNumber') {
      return (
        <div className="w-full h-full border-4 flex items-center justify-center relative overflow-hidden"
             style={{
               background: 'rgba(0, 0, 0, 0.8)',
               border: '4px solid #8B5C2A',
               boxShadow: '0 0 24px #FFD70044, 0 2px 8px #0006',
               borderRadius: '4px',
             }}>
          <div className="absolute inset-1 border-2"
               style={{border: '2px solid #DC2626'}}></div>
          <span className="text-4xl font-bold z-10" style={{color: '#FFD700'}}>{String(countdown).padStart(2, '0')}</span>
          <div className="absolute top-1 left-1 w-2 h-2 rounded-full" style={{backgroundColor: '#DC2626'}}></div>
          <div className="absolute top-1 right-1 w-2 h-2 rounded-full" style={{backgroundColor: '#DC2626'}}></div>
          <div className="absolute bottom-1 left-1 w-2 h-2 rounded-full" style={{backgroundColor: '#DC2626'}}></div>
          <div className="absolute bottom-1 right-1 w-2 h-2 rounded-full" style={{backgroundColor: '#DC2626'}}></div>
          <div className="absolute inset-0 animate-pulse" style={{background: 'linear-gradient(45deg, rgba(255,215,0,0.1), rgba(255,215,0,0.3), rgba(255,215,0,0.1))'}}></div>
        </div>
      );
    }
    
    return (
      <div className={`w-full h-full`}>
      </div>
    );
  };

  // خريطة المربعات حسب index
  const squareMap = BOARD_SQUARES_CONFIG.reduce((acc, sq) => {
    acc[sq.gridIndex] = sq;
    return acc;
  }, {} as { [key: number]: SquareConfig });

  // رسم شبكة 5x5 كاملة
  const gridSquares = [];
  for (let i = 0; i < 25; i++) {
    // التحقق من وجود المربع في التكوين
    if (squareMap[i]) {
      gridSquares.push(
        <div key={`square-${i}`} className="game-square">
          <div className="game-square-inner">
            {renderSquareContent(squareMap[i])}
          </div>
        </div>
      );
    } else {
      // مربع داخلي فارغ (المربعات المركزية)
      gridSquares.push(
        <div key={`empty-${i}`} className="game-square">
          <div className="game-square-inner" style={{background: 'transparent', border: 'none'}}></div>
        </div>
      );
    }
  }

  return (
    <div
      className="w-full h-full grid grid-cols-5 relative board-bg"
      style={{
        gap: settings.gap,
        padding: settings.padding,
      }}
    >
      {/* ستايل ديناميكي للمربعات حسب الجهاز */}
      <style>{`
        .game-square {
          width: 100%;
          height: 100%;
          min-width: ${settings.squareSize.minWidth};
          min-height: ${settings.squareSize.minHeight};
          aspect-ratio: 1/1;
          position: relative;
          box-sizing: border-box;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .game-square:nth-child(-n+5) {
          margin-top: ${settings.gridAdjustments?.firstRowMarginTop || '0px'} !important;
        }
        .game-square:nth-child(n+6):nth-child(-n+10) {
          margin-top: ${settings.gridAdjustments?.secondRowMarginTop || '0px'} !important;
        }
        .game-square:nth-child(5n+1) {
          margin-left: ${settings.gridAdjustments?.firstColumnMarginLeft || '0px'} !important;
          transform: translateX(${settings.gridAdjustments?.firstColumnMarginLeft || '0px'}) !important;
        }
        .game-square-inner {
          width: 100%;
          height: 100%;
          background: rgba(255,215,0,0.15);
          border: 1px solid rgba(255,215,0,0.35);
          border-radius: 0;
          position: relative;
          box-sizing: border-box;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      `}</style>
      {gridSquares}

      {/* مربع مركزي يغطي منطقة المنتصف */}
      <div
        className="absolute z-30 flex flex-col items-center justify-between"
        style={{
          top: '24%',
          left: '22%',
          width: '56%',
          height: '56%',
          // لا يوجد لون خلفية أو حدود
          color: '#fff',
          textAlign: 'center',
          padding: 0,
          pointerEvents: 'none',
        }}
      >

        {/* رمز ∞ والوقت countdown مثبتين في الأعلى */}
        <div style={{position: 'absolute', top: '2px', left: 0, right: 0, textAlign: 'center'}}>
          <div style={{
            fontSize: '1.1rem',
            fontWeight: 'bold',
            color: '#FFD700',
            letterSpacing: '2px',
            textShadow: '0 2px 8px #7C3AED, 0 0 6px #A084CA',
            marginBottom: '0',
            marginTop: '0',
            fontFamily: 'Arial Black, Arial, sans-serif',
          }}>
            INFINITY BOX
          </div>
          <div
            style={{
              fontSize: '3.5rem',
              fontWeight: 'bold',
              color: '#FFD700',
              textShadow: '0 2px 12px #000',
              margin: 0,
              marginBottom: '2px',
              marginTop: '0',
              animation: gamePhase === 'light_animation' ? 'infinity-spin 1.2s linear infinite' : 'none',
              display: 'inline-block',
            }}>
            ∞
          </div>
          <div
            style={{
              fontSize: '2.4rem',
              fontWeight: 'bold',
              color: '#ffe066',
              textShadow: '0 0 8px #FFD700, 0 2px 8px #000',
              margin: 0,
              marginTop: '0',
              fontFamily: 'DS-Digital, Digital-7, monospace',
              letterSpacing: '0.12em',
            }}>{String(countdown).padStart(2, '0')}</div>
        </div>
      </div>
      
            {/* الحلقات النارية فوق المربعات */}
      {Array.isArray(lightPositions) && lightPositions.length > 0
        ? lightPositions.map((pos, idx) => {
            // التحقق من نوع المربع
            const squareConfig = ACTIVE_GAME_SQUARES.find(sq => sq.gridIndex === pos);
            const isLucky = squareConfig?.type === 'luckyDoubleText' || squareConfig?.type === 'luckyTripleText';
            const isWinningSquare = selectedSquares.includes(pos);
            // رسم 4 إطارات شفافة متدرجة اللون بحجم المربع
            const colors = ['#FFD700', '#FFA500', '#FF8800', '#FF3333'];
            return pos >= 0 && (
              <>
                {[0,1,2,3].map((i) => (
                  <div
                    key={`fire-frame-${pos}-${idx}-${i}`}
                    className="absolute z-30 pointer-events-none"
                    style={{
                      left: `calc(${(pos % 5) * 20}% + ${i * 1.5}px)`,
                      top: `calc(${Math.floor(pos / 5) * 20}% + ${i * 1.5}px)`,
                      width: '20%',
                      height: '20%',
                      background: 'transparent',
                      border: `2.5px solid ${colors[i]}`,
                      borderRadius: '7px',
                      opacity: isWinningSquare ? 1 : 0.8 - i*0.12,
                      boxShadow: 'none',
                      transition: 'all 0.18s cubic-bezier(.4,0,.2,1)',
                      animation: isWinningSquare ? 'none' : `fireFrameMove 0.7s ${i*0.15}s linear infinite`,
                    }}
                  />
                ))}
              </>
            );
          })
        : (lightPosition >= 0 && (
            <>
              {[0,1,2,3].map((i) => (
                <div
                  key={`fire-frame-${lightPosition}-main-${i}`}
                  className="absolute z-30 pointer-events-none"
                  style={{
                    left: `calc(${(lightPosition % 5) * 20}% + ${i * 1.5}px)`,
                    top: `calc(${Math.floor(lightPosition / 5) * 20}% + ${i * 1.5}px)`,
                    width: '20%',
                    height: '20%',
                    background: 'transparent',
                    border: `2.5px solid ${['#FFD700', '#FFA500', '#FF8800', '#FF3333'][i]}`,
                    borderRadius: '7px',
                    opacity: 1,
                    boxShadow: 'none',
                    transition: 'all 0.18s cubic-bezier(.4,0,.2,1)',
                    animation: 'none',
                  }}
                />
              ))}
            </>
          ))}

      {/* تأثيرات إضافية للوحة */}
      <div className="absolute inset-0 pointer-events-none rounded-lg"
           style={{
             background: 'radial-gradient(circle at 50% 50%, rgba(255,215,0,0.07) 0%, transparent 80%)',
             animation: 'boardGlow 4s ease-in-out infinite'
           }}></div>
      
      <style>{`
        @keyframes pulse {
          0%, 100% { opacity: 1; }
          50% { opacity: 0.7; }
        }
        /* حركة مستطيلات الشريط الناري بشكل متطارد */
        /* حركة إطارات الشريط الناري بشكل هادئ */
        @keyframes fireFrameMove {
          0% { transform: scale(1) translateY(0); }
          50% { transform: scale(1.07) translateY(-6%); }
          100% { transform: scale(1) translateY(0); }
        }
        
        @keyframes shimmer {
          0% { transform: translateX(-100%) skewX(-15deg); }
          100% { transform: translateX(200%) skewX(-15deg); }
        }
        
        @keyframes backgroundShift {
          0%, 100% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
        }
        
        @keyframes boardGlow {
          0%, 100% { opacity: 0.1; }
          50% { opacity: 0.3; }
        }

        @keyframes spin-infinity {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
        .spin-infinity {
          animation: spin-infinity 1.2s linear infinite;
          display: inline-block;
        }

        @keyframes fireGlow {
          0% {
            box-shadow: 0 0 20px rgba(255, 69, 0, 0.8),
                        inset 0 0 15px rgba(255, 140, 0, 0.3),
                        0 0 40px rgba(255, 69, 0, 0.4);
            transform: scale(1);
          }
          100% {
            box-shadow: 0 0 30px rgba(255, 69, 0, 1),
                        inset 0 0 25px rgba(255, 140, 0, 0.5),
                        0 0 60px rgba(255, 69, 0, 0.6);
            transform: scale(1.02);
          }
        }

        @keyframes fireMove {
          0% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
          100% { background-position: 0% 50%; }
        }

        @keyframes flameFlicker {
          0% {
            opacity: 0.8;
            transform: scaleY(1) scaleX(0.9);
          }
          50% {
            opacity: 1;
            transform: scaleY(1.2) scaleX(1.1);
          }
          100% {
            opacity: 0.9;
            transform: scaleY(0.9) scaleX(1);
          }
        }

        @keyframes fireFlicker {
          0% {
            filter: brightness(1.3) saturate(1.4) hue-rotate(0deg);
          }
          25% {
            filter: brightness(1.5) saturate(1.6) hue-rotate(5deg);
          }
          50% {
            filter: brightness(1.2) saturate(1.3) hue-rotate(-5deg);
          }
          75% {
            filter: brightness(1.4) saturate(1.5) hue-rotate(3deg);
          }
          100% {
            filter: brightness(1.3) saturate(1.4) hue-rotate(0deg);
          }
        }

        @keyframes fireRingBlink {
          0% {
            opacity: 1;
            transform: scale(1);
            boxShadow:
              0 0 25px rgba(255, 69, 0, 1),
              0 0 50px rgba(255, 165, 0, 0.9),
              0 0 75px rgba(255, 255, 0, 0.7),
              inset 0 0 20px rgba(255, 140, 0, 0.5);
          }
          50% {
            opacity: 1;
            transform: scale(1.05);
            boxShadow:
              0 0 35px rgba(255, 69, 0, 1),
              0 0 70px rgba(255, 165, 0, 1),
              0 0 105px rgba(255, 255, 0, 0.9),
              inset 0 0 30px rgba(255, 140, 0, 0.7);
          }
          100% {
            opacity: 1;
            transform: scale(1);
            boxShadow:
              0 0 25px rgba(255, 69, 0, 1),
              0 0 50px rgba(255, 165, 0, 0.9),
              0 0 75px rgba(255, 255, 0, 0.7),
              inset 0 0 20px rgba(255, 140, 0, 0.5);
          }
        }



        @keyframes flameRotate {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        @keyframes infinity-spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        @keyframes fireRingSpin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

                @keyframes fireRingTrailMove {
          0% {
            transform: scale(1) translateY(0);
          }
          50% {
            transform: scale(1.02) translateY(-1%);
          }
          100% {
            transform: scale(1) translateY(0);
          }
        }

        @keyframes unifiedFireGlow {
          0% {
            border-color: #FFD70044;
            border-width: 1px;
            box-shadow: 0 0 8px 2px #FFD70022;
          }
          25% {
            border-color: #FFA50033;
            border-width: 1px;
            box-shadow: 0 0 10px 3px #FFA50022;
          }
          50% {
            border-color: #FF880022;
            border-width: 1px;
            box-shadow: 0 0 12px 4px #FF880022;
          }
          75% {
            border-color: #FF333322;
            border-width: 1px;
            box-shadow: 0 0 10px 3px #FF333322;
          }
          100% {
            border-color: #FFD70044;
            border-width: 1px;
            box-shadow: 0 0 8px 2px #FFD70022;
          }
        }

        /* تأثيرات احتفالية للمربعات الفائزة */
        @keyframes celebrationGlow {
          0% {
            box-shadow: 0 0 40px 15px #FFD700AA, 0 0 60px 20px #FFA50088, 0 0 80px 25px #FF880066;
            transform: scale(1);
          }
          100% {
            box-shadow: 0 0 60px 20px #FFD700CC, 0 0 80px 25px #FFA500AA, 0 0 100px 30px #FF880088;
            transform: scale(1.05);
          }
        }

        @keyframes celebrationPulse {
          0% {
            opacity: 0.8;
            transform: scale(1);
            border-color: #FFD700;
          }
          50% {
            opacity: 1;
            transform: scale(1.1);
            border-color: #FFA500;
          }
          100% {
            opacity: 0.8;
            transform: scale(1);
            border-color: #FFD700;
          }
        }

        @keyframes celebrationParticle {
          0% {
            opacity: 0;
            transform: translate(-50%, -50%) scale(0) rotate(0deg);
          }
          20% {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1) rotate(72deg);
          }
          80% {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1.2) rotate(288deg);
          }
          100% {
            opacity: 0;
            transform: translate(-50%, -50%) scale(0) rotate(360deg);
          }
        }

        @keyframes celebrationShimmer {
          0% {
            opacity: 0;
            transform: translateX(-100%) translateY(-100%) rotate(45deg);
          }
          50% {
            opacity: 0.8;
            transform: translateX(0%) translateY(0%) rotate(45deg);
          }
          100% {
            opacity: 0;
            transform: translateX(100%) translateY(100%) rotate(45deg);
          }
        }

        /* تحسين تأثير الجسيمات */
        .celebration-particles div:nth-child(1) { animation-delay: 0s; }
        .celebration-particles div:nth-child(2) { animation-delay: 0.2s; }
        .celebration-particles div:nth-child(3) { animation-delay: 0.4s; }
        .celebration-particles div:nth-child(4) { animation-delay: 0.6s; }
        .celebration-particles div:nth-child(5) { animation-delay: 0.8s; }
        .celebration-particles div:nth-child(6) { animation-delay: 1s; }
        .celebration-particles div:nth-child(7) { animation-delay: 1.2s; }
        .celebration-particles div:nth-child(8) { animation-delay: 1.4s; }

        /* تأثيرات احتفالية للاكي */
        @keyframes luckyCelebrationGlow {
          0% {
            box-shadow: 0 0 50px 20px #FFD700BB, 0 0 70px 25px #FFA50099, 0 0 90px 30px #FF880077;
            transform: scale(1);
          }
          100% {
            box-shadow: 0 0 70px 25px #FFD700DD, 0 0 90px 30px #FFA500BB, 0 0 110px 35px #FF880099;
            transform: scale(1.08);
          }
        }

        @keyframes luckyParticle {
          0% {
            opacity: 0;
            transform: translate(-50%, -50%) scale(0) rotate(0deg);
          }
          25% {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1.5) rotate(90deg);
          }
          75% {
            opacity: 1;
            transform: translate(-50%, -50%) scale(2) rotate(270deg);
          }
          100% {
            opacity: 0;
            transform: translate(-50%, -50%) scale(0) rotate(360deg);
          }
        }

        @keyframes luckyGoldenShimmer {
          0% {
            opacity: 0;
            transform: translateX(-100%) translateY(-100%) rotate(45deg);
          }
          50% {
            opacity: 1;
            transform: translateX(0%) translateY(0%) rotate(45deg);
          }
          100% {
            opacity: 0;
            transform: translateX(100%) translateY(100%) rotate(45deg);
          }
        }

        @keyframes fireRingParticle {
          0% {
            opacity: 0;
            transform: translate(-50%, -50%) scale(0) rotate(0deg);
          }
          25% {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1.2) rotate(90deg);
          }
          75% {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1.5) rotate(270deg);
          }
          100% {
            opacity: 0;
            transform: translate(-50%, -50%) scale(0) rotate(360deg);
          }
        }
        /* تهدئة توهج الشريط الناري */
        @keyframes unifiedFireGlow {
          0% {
            border-color: #FFD70044;
            border-width: 1px;
            box-shadow: 0 0 8px 2px #FFD70022;
          }
          25% {
            border-color: #FFA50033;
            border-width: 1px;
            box-shadow: 0 0 10px 3px #FFA50022;
          }
          50% {
            border-color: #FF880022;
            border-width: 1px;
            box-shadow: 0 0 12px 4px #FF880022;
          }
          75% {
            border-color: #FF333322;
            border-width: 1px;
            box-shadow: 0 0 10px 3px #FF333322;
          }
          100% {
            border-color: #FFD70044;
            border-width: 1px;
            box-shadow: 0 0 8px 2px #FFD70022;
          }
        }
      `}</style>
    </div>
  );
};

export default GameBoard;