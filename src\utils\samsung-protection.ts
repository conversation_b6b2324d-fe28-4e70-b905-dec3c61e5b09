// نظام حماية إعدادات Samsung Galaxy S8
// منع تأثرها عند تعديل شاشات أخرى

// إعدادات Samsung Galaxy S8 محمية ومجمدة
export const SAMSUNG_GALAXY_S8_LOCKED = {
  device: {
    name: 'Samsung Galaxy S8',
    width: 412,
    height: 915,
  },
  backgroundImage: '/images/bg-412x915.webp',
  
  gameBoard: {
    left: '50%',
    top: '12%',
    width: 'min(600px, 85vw)',
    height: 'min(600px, 60vh)',
    transform: 'translateX(-50%)',
    position: 'absolute',
    zIndex: 10,
  },

  historyBar: {
    width: '60%',
    height: '33px',
    top: 'calc(75% + 55px)',
    left: '50%',
    transform: 'translateX(-50%)',
    position: 'absolute',
    zIndex: 20000,
  },

  symbolButtons: {
    "bar": { "left": "12.8882%", "top": "59.8958%", "name": "BAR" },
    "watermelon": { "left": "32.3427%", "top": "60.099%", "name": "WATERMELON" },
    "lemon": { "left": "50.3607%", "top": "59.9983%", "name": "LEMON" },
    "banana": { "left": "68.375%", "top": "60.0972%", "name": "BANANA" },
    "apple": { "left": "87.4687%", "top": "60.0625%", "name": "APPLE" }
  },

  amountButtons: {
    "amount1": { "left": "8.50588%", "top": "74.6667%", "name": "AMOUNT_1", "value": 5000 },
    "amount2": { "left": "20.3933%", "top": "74.6667%", "name": "AMOUNT_2", "value": 3000 },
    "amount3": { "left": "31.9992%", "top": "74.8333%", "name": "AMOUNT_3", "value": 1000 },
    "amount4": { "left": "43.8095%", "top": "75.1667%", "name": "AMOUNT_4", "value": 500 },
    "amount5": { "left": "55.134%", "top": "74.8333%", "name": "AMOUNT_5", "value": 100 }
  },

  actionButtons: {
    spin: { left: '70%', top: '80%' }
  },

  betRectangles: {
    "bar": { "left": "11.11123%", "top": "70.1667%", "symbol": "BAR" },
    "watermelon": { "left": "31.8103%", "top": "70.1667%", "symbol": "WATERMELON" },
    "lemon": { "left": "51.3399%", "top": "70.3316%", "symbol": "LEMON" },
    "banana": { "left": "67.8438%", "top": "70.1649%", "symbol": "BANANA" },
    "apple": { "left": "90.5764%", "top": "70.3333%", "symbol": "APPLE" }
  },

  betNumbers: {
    "total_bets": { "left": "90.3836%", "top": "12%", "info": "TOTAL_BETS" },
    "current_bet": { "left": "80.5784%", "top": "12%", "info": "CURRENT_BET" },
    "last_win": { "left": "80.8907%", "top": "79.8333%", "info": "LAST_WIN" }
  },

  topDisplays: {
    "balanceDisplay": { "left": "31.0893%", "top": "12%", "name": "BALANCE" },
    "totalBetDisplay": { "left": "89%", "top": "12%", "name": "TOTAL_BET" }
  },
};

// حماية كاملة من التعديل
Object.freeze(SAMSUNG_GALAXY_S8_LOCKED);
Object.freeze(SAMSUNG_GALAXY_S8_LOCKED.device);
Object.freeze(SAMSUNG_GALAXY_S8_LOCKED.symbolButtons);
Object.freeze(SAMSUNG_GALAXY_S8_LOCKED.amountButtons);
Object.freeze(SAMSUNG_GALAXY_S8_LOCKED.betRectangles);
Object.freeze(SAMSUNG_GALAXY_S8_LOCKED.topDisplays);

// دالة للحصول على نسخة آمنة من إعدادات Samsung
export function getSamsungConfig() {
  return JSON.parse(JSON.stringify(SAMSUNG_GALAXY_S8_LOCKED));
}

// دالة للتحقق من عدم تأثر Samsung
export function validateNoSamsungImpact(changes: any) {
  const samsungConfig = getSamsungConfig();
  
  // التحقق من عدم تغيير إعدادات Samsung
  if (changes.device && changes.device.name === 'Samsung Galaxy S8') {
    throw new Error('Samsung Galaxy S8 configuration is protected and cannot be modified');
  }
  
  return true;
}

// دالة لإنشاء نسخة احتياطية من Samsung
export function backupSamsungConfig() {
  const backup = {
    deviceName: 'Samsung Galaxy S8',
    timestamp: new Date().toISOString(),
    config: getSamsungConfig()
  };
  
  localStorage.setItem('samsung_galaxy_s8_backup', JSON.stringify(backup));
  return backup;
}

// دالة لاستعادة نسخة احتياطية من Samsung
export function restoreSamsungConfig() {
  const backup = localStorage.getItem('samsung_galaxy_s8_backup');
  if (!backup) {
    throw new Error('No Samsung Galaxy S8 backup found');
  }
  
  return JSON.parse(backup);
}

// دالة للتحقق من سلامة إعدادات Samsung
export function validateSamsungConfig() {
  const config = getSamsungConfig();
  const requiredFields = ['device', 'symbolButtons', 'amountButtons', 'betRectangles'];
  
  for (const field of requiredFields) {
    if (!config[field]) {
      throw new Error(`Missing required field: ${field} in Samsung Galaxy S8 config`);
    }
  }
  
  return true;
}

// دالة للحصول على إعدادات Samsung مع الحماية
export function getProtectedSamsungConfig() {
  try {
    validateSamsungConfig();
    return getSamsungConfig();
  } catch (error) {
    console.error('Samsung Galaxy S8 config validation failed:', error);
    // إرجاع الإعدادات الأصلية المحمية
    return getSamsungConfig();
  }
} 