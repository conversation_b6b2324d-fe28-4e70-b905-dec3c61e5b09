import React, { useState, useEffect } from 'react';
import { BACKGROUND_LAYOUTS, BackgroundLayoutConfig } from '../config/backgroundLayouts';

interface MultiDeviceBackgroundProps {
  onLayoutChange?: (layout: BackgroundLayoutConfig) => void;
  className?: string;
}

const MultiDeviceBackground: React.FC<MultiDeviceBackgroundProps> = ({
  onLayoutChange,
  className = ''
}) => {
  const [backgroundImage, setBackgroundImage] = useState<string>('/222.jpg');
  const [buttonLayout, setButtonLayout] = useState<{ [key: string]: { left: string; top: string; name: string } }>({});
  const [isLoading, setIsLoading] = useState(true);
  const [deviceInfo, setDeviceInfo] = useState<string>('');

  // دالة لاكتشاف الجهاز الحالي بناءً على العرض والارتفاع
  function detectCurrentDevice(): BackgroundLayoutConfig {
    const width = window.innerWidth;
    const height = window.innerHeight;
    return (
      BACKGROUND_LAYOUTS.find(
        config => Math.abs(config.width - width) < 50 && Math.abs(config.height - height) < 50
      ) || BACKGROUND_LAYOUTS[0]
    );
  }

  useEffect(() => {
    const updateBackground = () => {
      setIsLoading(true);
      try {
        const config = detectCurrentDevice();
        setBackgroundImage(config.backgroundImage);
        setButtonLayout(config.symbolButtons);
        setDeviceInfo(`${config.name} (${config.width}×${config.height})`);
        if (onLayoutChange) {
          onLayoutChange(config);
        }
      } catch {
        setBackgroundImage('/222.jpg');
        setButtonLayout({});
      } finally {
        setIsLoading(false);
      }
    };
    updateBackground();
    window.addEventListener('resize', updateBackground);
    window.addEventListener('orientationchange', updateBackground);
    return () => {
      window.removeEventListener('resize', updateBackground);
      window.removeEventListener('orientationchange', updateBackground);
    };
  }, []);

  const backgroundStyle: React.CSSProperties = {
    position: 'fixed',
    top: 0,
    left: 0,
    width: '100vw',
    height: '100vh',
    backgroundImage: `url(${backgroundImage})`,
    backgroundSize: 'contain', // تعديل ليظهر كل أجزاء الصورة
    backgroundPosition: 'center center',
    backgroundRepeat: 'no-repeat',
    backgroundAttachment: 'scroll',
    zIndex: 1,
    transition: 'background-image 0.3s ease',
    backgroundColor: '#000', // إضافة خلفية سوداء في حال وجود فراغات
  };

  return (
    <>
      <div 
        className={`multi-device-background ${className}`}
        style={backgroundStyle}
        data-device={deviceInfo}
      />
      {isLoading && (
        <div style={{
          position: 'fixed',
          top: '10px',
          right: '10px',
          background: 'rgba(0, 0, 0, 0.8)',
          color: '#00ff88',
          padding: '8px 12px',
          borderRadius: '4px',
          fontSize: '12px',
          zIndex: 9999,
          fontFamily: 'monospace'
        }}>
          🔄 Loading background...
        </div>
      )}
    </>
  );
};

export default MultiDeviceBackground;
