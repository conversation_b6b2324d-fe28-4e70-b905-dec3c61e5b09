import React from 'react';
import { BET_AMOUNTS, SYMBOL_MAX_BETS } from '../constants/gameConfig';
import { BetsOnTypes } from '../types/game';

interface DetailedDebuggerProps {
  balance: number;
  betsOnTypes: BetsOnTypes;
  pendingBetsOnTypes: BetsOnTypes;
  currentBetValueToApply: number;
  isBettingPhase: boolean;
}

const DetailedDebugger: React.FC<DetailedDebuggerProps> = ({
  balance,
  betsOnTypes,
  pendingBetsOnTypes,
  currentBetValueToApply,
  isBettingPhase
}) => {
  const symbols = ['BAR', 'WATERMELON', 'LEMON', 'BANANA', 'APPLE'] as const;

  const canBetOnSymbol = (symbol: keyof BetsOnTypes) => {
    // التحقق من أن اللعبة في مرحلة الرهان
    if (!isBettingPhase) {
      return { canBet: false, reason: 'ليس في مرحلة الرهان' };
    }

    // التحقق من أن هناك مبلغ مختار
    if (currentBetValueToApply <= 0) {
      return { canBet: false, reason: 'لم يتم اختيار مبلغ' };
    }

    // التحقق من الرصيد
    const totalPendingBets = Object.values(pendingBetsOnTypes || {}).reduce((sum, bet) => sum + bet, 0);
    if (balance < totalPendingBets + currentBetValueToApply) {
      return { canBet: false, reason: `رصيد غير كافي (${balance} < ${totalPendingBets + currentBetValueToApply})` };
    }

    // التحقق من الحد الأقصى للرهان
    const confirmedBet = (betsOnTypes && betsOnTypes[symbol]) || 0;
    const pendingBet = (pendingBetsOnTypes && pendingBetsOnTypes[symbol]) || 0;
    const totalBet = confirmedBet + pendingBet;
    const maxBet = SYMBOL_MAX_BETS[symbol];

    if (totalBet + currentBetValueToApply > maxBet) {
      return { canBet: false, reason: `تجاوز الحد الأقصى (${totalBet + currentBetValueToApply} > ${maxBet})` };
    }

    return { canBet: true, reason: 'يمكن الرهان' };
  };

  const canSelectAmount = (amount: number) => {
    if (!isBettingPhase) {
      return { canSelect: false, reason: 'ليس في مرحلة الرهان' };
    }
    if (balance < amount) {
      return { canSelect: false, reason: `رصيد غير كافي (${balance} < ${amount})` };
    }
    return { canSelect: true, reason: 'يمكن الاختيار' };
  };

  return (
    <div style={{
      position: 'fixed',
      top: '10px',
      right: '10px',
      background: 'rgba(0,0,0,0.95)',
      color: 'white',
      padding: '15px',
      borderRadius: '8px',
      fontSize: '11px',
      zIndex: 9999,
      border: '2px solid #FFD700',
      minWidth: '280px',
      maxHeight: '80vh',
      overflow: 'auto'
    }}>
      <h4 style={{ margin: '0 0 10px 0', color: '#FFD700', fontSize: '14px' }}>🔧 تشخيص مفصل</h4>
      
      <div style={{ marginBottom: '8px' }}>
        <strong>مرحلة اللعبة:</strong> {isBettingPhase ? '✅ رهان' : '❌ غير رهان'}
      </div>
      
      <div style={{ marginBottom: '8px' }}>
        <strong>الرصيد:</strong> {balance.toLocaleString()}
      </div>
      
      <div style={{ marginBottom: '8px' }}>
        <strong>المبلغ المختار:</strong> {currentBetValueToApply.toLocaleString()}
      </div>

      <hr style={{ border: '1px solid #444', margin: '10px 0' }} />

      <div style={{ marginBottom: '10px' }}>
        <strong style={{ color: '#FFD700' }}>أزرار المبالغ:</strong>
        {BET_AMOUNTS.map(amount => {
          const status = canSelectAmount(amount);
          return (
            <div key={amount} style={{ 
              marginBottom: '3px', 
              padding: '2px 4px',
              background: status.canSelect ? 'rgba(0,255,0,0.1)' : 'rgba(255,0,0,0.1)',
              borderRadius: '3px',
              fontSize: '10px'
            }}>
              {amount.toLocaleString()}: {status.canSelect ? '✅' : '❌'} {status.reason}
            </div>
          );
        })}
      </div>

      <hr style={{ border: '1px solid #444', margin: '10px 0' }} />

      <div style={{ marginBottom: '10px' }}>
        <strong style={{ color: '#FFD700' }}>أزرار الفواكه:</strong>
        {symbols.map(symbol => {
          const status = canBetOnSymbol(symbol);
          const confirmedBet = (betsOnTypes && betsOnTypes[symbol]) || 0;
          const pendingBet = (pendingBetsOnTypes && pendingBetsOnTypes[symbol]) || 0;
          const totalBet = confirmedBet + pendingBet;
          const maxBet = SYMBOL_MAX_BETS[symbol];
          
          return (
            <div key={symbol} style={{ 
              marginBottom: '3px', 
              padding: '2px 4px',
              background: status.canBet ? 'rgba(0,255,0,0.1)' : 'rgba(255,0,0,0.1)',
              borderRadius: '3px',
              fontSize: '10px'
            }}>
              {symbol}: {status.canBet ? '✅' : '❌'} {status.reason}
              <br />
              <span style={{ fontSize: '9px', color: '#ccc' }}>
                رهان: {totalBet.toLocaleString()} / {maxBet.toLocaleString()}
              </span>
            </div>
          );
        })}
      </div>

      <hr style={{ border: '1px solid #444', margin: '10px 0' }} />

      <div style={{ marginBottom: '8px' }}>
        <strong style={{ color: '#FFD700' }}>الرهانات المؤقتة:</strong>
        {Object.entries(pendingBetsOnTypes).map(([symbol, amount]) => (
          <div key={symbol} style={{ fontSize: '10px', color: '#ccc' }}>
            {symbol}: {amount.toLocaleString()}
          </div>
        ))}
      </div>

      <div style={{ marginBottom: '8px' }}>
        <strong style={{ color: '#FFD700' }}>الرهانات المؤكدة:</strong>
        {Object.entries(betsOnTypes).map(([symbol, amount]) => (
          <div key={symbol} style={{ fontSize: '10px', color: '#ccc' }}>
            {symbol}: {amount.toLocaleString()}
          </div>
        ))}
      </div>
    </div>
  );
};

export default DetailedDebugger; 