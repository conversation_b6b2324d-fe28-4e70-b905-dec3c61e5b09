import React, { useEffect } from 'react';
import { ButtonLayout } from '../utils/multiDeviceBackground';

interface ResponsiveGameButtonsProps {
  layout: ButtonLayout;
  onBetClick: (symbol: string) => void;
  betsOnTypes: { [key: string]: number };
  isGameActive: boolean;
  gamePhase?: 'betting' | 'light_animation' | 'result_display';
}

const ResponsiveGameButtons: React.FC<ResponsiveGameButtonsProps> = ({
  layout,
  onBetClick,
  betsOnTypes,
  isGameActive,
  gamePhase = 'betting'
}) => {
  // إضافة CSS animation للإضاءة الذهبية
  useEffect(() => {
    const styleElement = document.getElementById('golden-glow-keyframes');
    if (!styleElement) {
      const style = document.createElement('style');
      style.id = 'golden-glow-keyframes';
      style.textContent = `
        @keyframes goldenGlow {
          0% {
            box-shadow: 
              0 0 20px rgba(255, 215, 0, 0.8),
              0 0 40px rgba(255, 215, 0, 0.6),
              0 0 60px rgba(255, 215, 0, 0.4) !important;
            border-color: #ffd700 !important;
          }
          100% {
            box-shadow: 
              0 0 30px rgba(255, 215, 0, 1),
              0 0 60px rgba(255, 215, 0, 0.8),
              0 0 90px rgba(255, 215, 0, 0.6) !important;
            border-color: #ffed4e !important;
          }
        }
      `;
      document.head.appendChild(style);
    }
  }, []);

  // إذا لم يكن هناك تخطيط، لا تظهر الأزرار
  if (!layout || Object.keys(layout).length === 0) {
    return null;
  }

  const buttonStyle = (position: { left: string; top: string; name: string }) => {
    const isBettingPhase = gamePhase === 'betting';
    return {
      position: 'absolute' as const,
      left: position.left,
      top: position.top,
      width: '60px',
      height: '60px',
      borderRadius: '50%',
      border: isBettingPhase ? '3px solid #ffd700' : '3px solid #00ff88',
      background: isBettingPhase ? 'linear-gradient(145deg, #ffd700, #ffb347, #daa520)' : 'rgba(0, 0, 0, 0.8)',
      color: isBettingPhase ? '#8b4513' : '#00ff88',
      fontSize: '12px',
      fontWeight: 'bold',
      cursor: isGameActive ? 'pointer' : 'not-allowed',
      transition: 'all 0.3s ease',
      display: 'flex',
      flexDirection: 'column' as const,
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 20,
      backdropFilter: 'blur(5px)',
      opacity: isGameActive ? 1 : 0.6,
      transform: 'translate(-50%, -50%)',
      boxShadow: isBettingPhase ? 
        '0 0 20px rgba(255, 215, 0, 0.8), 0 0 40px rgba(255, 215, 0, 0.6), 0 0 60px rgba(255, 215, 0, 0.4)' :
        'none',
      animation: isBettingPhase ? 'goldenGlow 2s ease-in-out infinite alternate' : 'none'
    };
  };

  const getButtonContent = (symbolName: string) => {
    const betAmount = betsOnTypes[symbolName] || 0;
    return (
      <>
        <div style={{ fontSize: '10px', lineHeight: '1' }}>
          {symbolName}
        </div>
        {betAmount > 0 && (
          <div style={{ fontSize: '8px', color: '#ffff00', marginTop: '2px' }}>
            {betAmount}
          </div>
        )}
      </>
    );
  };

  const handleButtonClick = (symbolName: string) => {
    if (isGameActive) {
      onBetClick(symbolName);
    }
  };

  return (
    <div className="responsive-game-buttons">
      {Object.entries(layout).map(([key, position]) => (
        <button
          key={key}
          style={buttonStyle(position)}
          onClick={() => handleButtonClick(position.name)}
          onMouseEnter={(e) => {
            if (isGameActive) {
              const isBettingPhase = gamePhase === 'betting';
              e.currentTarget.style.transform = 'translate(-50%, -50%) scale(1.1)';
              e.currentTarget.style.borderColor = isBettingPhase ? '#ffed4e' : '#00ffff';
              e.currentTarget.style.boxShadow = isBettingPhase ? 
                '0 0 30px rgba(255, 215, 0, 1), 0 0 60px rgba(255, 215, 0, 0.8), 0 0 90px rgba(255, 215, 0, 0.6)' :
                '0 0 20px rgba(0, 255, 255, 0.5)';
            }

            // عرض معلومات الموقع في وضع التطوير
            if (process.env.NODE_ENV === 'development') {
      
            }
          }}
          onMouseLeave={(e) => {
            const isBettingPhase = gamePhase === 'betting';
            e.currentTarget.style.transform = 'translate(-50%, -50%) scale(1)';
            e.currentTarget.style.borderColor = isBettingPhase ? '#ffd700' : '#00ff88';
            e.currentTarget.style.boxShadow = isBettingPhase ? 
              '0 0 20px rgba(255, 215, 0, 0.8), 0 0 40px rgba(255, 215, 0, 0.6), 0 0 60px rgba(255, 215, 0, 0.4)' :
              'none';
          }}
          disabled={!isGameActive}
          data-symbol={position.name}
          data-key={key}
          title={`${position.name} (${position.left}, ${position.top})`}
        >
          {getButtonContent(position.name)}
        </button>
      ))}

      {/* مؤشر عدد الأزرار في وضع التطوير */}
      {process.env.NODE_ENV === 'development' && (
        <div style={{
          position: 'fixed',
          top: '50px',
          left: '10px',
          background: 'rgba(0, 0, 0, 0.8)',
          color: '#00ff88',
          padding: '8px 12px',
          borderRadius: '4px',
          fontSize: '11px',
          zIndex: 9999,
          fontFamily: 'monospace'
        }}>
          🎯 Buttons: {Object.keys(layout).length}
          <br />
          📍 Hover for positions
        </div>
      )}
    </div>
  );
};

export default ResponsiveGameButtons;
