// أدوات اختبار للتأكد من صحة التحسينات
import { BetsOnTypes } from '../types/game';
import { selectWinningPosition } from './winningPositionSelector';
import { generateLightSequence, calculateLightSpeed } from './lightAnimation';
import { calculateWin } from './winCalculator';
import { getScreenSize, getResponsiveLayout } from './responsive';

// اختبار منطق اختيار الموقع الفائز
export const testWinningPositionSelector = () => {
  console.log('🧪 بدء اختبار منطق اختيار الموقع الفائز...');
  
  // اختبار 1: رهانات فارغة
  const emptyBets: BetsOnTypes = { 'APPLE': 0, 'BANANA': 0, 'LEMON': 0, 'WATERMELON': 0, 'BAR': 0 };
  const position1 = selectWinningPosition(emptyBets);
  console.log('✅ اختبار الرهانات الفارغة:', position1);
  
  // اختبار 2: رهان واحد
  const singleBet: BetsOnTypes = { 'APPLE': 1000, 'BANANA': 0, 'LEMON': 0, 'WATERMELON': 0, 'BAR': 0 };
  const position2 = selectWinningPosition(singleBet);
  console.log('✅ اختبار رهان واحد:', position2);
  
  // اختبار 3: رهانات متعددة
  const multipleBets: BetsOnTypes = { 'APPLE': 500, 'BANANA': 1000, 'LEMON': 750, 'WATERMELON': 0, 'BAR': 0 };
  const position3 = selectWinningPosition(multipleBets);
  console.log('✅ اختبار رهانات متعددة:', position3);
  
  console.log('🎉 اكتمل اختبار منطق اختيار الموقع الفائز');
};

// اختبار منطق الإضاءة
export const testLightAnimation = () => {
  console.log('🧪 بدء اختبار منطق الإضاءة...');
  
  // اختبار 1: تسلسل الإضاءة
  const sequence1 = generateLightSequence(10);
  console.log('✅ تسلسل الإضاءة للموقع 10:', sequence1.length, 'خطوة');
  
  // اختبار 2: تسلسل مع نقطة بداية
  const sequence2 = generateLightSequence(15, 5);
  console.log('✅ تسلسل الإضاءة من 5 إلى 15:', sequence2.length, 'خطوة');
  
  // اختبار 3: حساب السرعة
  const speed1 = calculateLightSpeed(10, 100);
  const speed2 = calculateLightSpeed(50, 100);
  const speed3 = calculateLightSpeed(90, 100);
  console.log('✅ سرعات الإضاءة:', { بداية: speed1, وسط: speed2, نهاية: speed3 });
  
  console.log('🎉 اكتمل اختبار منطق الإضاءة');
};

// اختبار حساب الأرباح
export const testWinCalculator = () => {
  console.log('🧪 بدء اختبار حساب الأرباح...');
  
  const testBets: BetsOnTypes = { 'APPLE': 1000, 'BANANA': 500, 'LEMON': 0, 'WATERMELON': 0, 'BAR': 0 };
  
  // اختبار 1: فوز بالتفاح
  const result1 = calculateWin([0], testBets); // افتراض أن الفهرس 0 هو تفاح
  console.log('✅ اختبار فوز التفاح:', result1.totalWinAmount);
  
  // اختبار 2: فوز بالموز
  const result2 = calculateWin([1], testBets); // افتراض أن الفهرس 1 هو موز
  console.log('✅ اختبار فوز الموز:', result2.totalWinAmount);
  
  // اختبار 3: لا فوز
  const result3 = calculateWin([2], testBets); // افتراض أن الفهرس 2 هو ليمون (لا رهان)
  console.log('✅ اختبار عدم الفوز:', result3.totalWinAmount);
  
  console.log('🎉 اكتمل اختبار حساب الأرباح');
};

// اختبار النظام المتجاوب
export const testResponsiveSystem = () => {
  console.log('🧪 بدء اختبار النظام المتجاوب...');
  
  // محاكاة أحجام شاشات مختلفة
  const mockScreenSizes = [
    { width: 375, height: 667 }, // iPhone SE
    { width: 390, height: 844 }, // iPhone 12 Pro
    { width: 768, height: 1024 }, // iPad
    { width: 1920, height: 1080 }, // Desktop
  ];
  
  mockScreenSizes.forEach(size => {
    // محاكاة تغيير حجم النافذة
    Object.defineProperty(window, 'innerWidth', { value: size.width, writable: true });
    Object.defineProperty(window, 'innerHeight', { value: size.height, writable: true });
    
    const screenSize = getScreenSize();
    const layout = getResponsiveLayout(screenSize);
    
    console.log(`✅ ${size.width}x${size.height} - النوع: ${screenSize.type}, الاتجاه: ${screenSize.orientation}`);
    console.log(`   حجم لوحة اللعب: ${layout.gameBoard.width} x ${layout.gameBoard.height}`);
    console.log(`   حجم أزرار الفواكه: ${layout.bettingControls.buttonSize}`);
  });
  
  console.log('🎉 اكتمل اختبار النظام المتجاوب');
};

// اختبار شامل لجميع الأنظمة
export const runAllTests = () => {
  console.log('🚀 بدء الاختبارات الشاملة...');
  console.log('=====================================');
  
  try {
    testWinningPositionSelector();
    console.log('');
    
    testLightAnimation();
    console.log('');
    
    testWinCalculator();
    console.log('');
    
    testResponsiveSystem();
    console.log('');
    
    console.log('🎉 اكتملت جميع الاختبارات بنجاح!');
    console.log('=====================================');
    
    return true;
  } catch (error) {
    console.error('❌ فشل في الاختبارات:', error);
    console.log('=====================================');
    
    return false;
  }
};

// دالة لاختبار الأداء
export const performanceTest = () => {
  console.log('⚡ بدء اختبار الأداء...');
  
  const startTime = performance.now();
  
  // تشغيل عدة عمليات لقياس الأداء
  for (let i = 0; i < 100; i++) {
    const randomBets: BetsOnTypes = {
      'APPLE': Math.floor(Math.random() * 1000),
      'BANANA': Math.floor(Math.random() * 1000),
      'LEMON': Math.floor(Math.random() * 1000),
      'WATERMELON': Math.floor(Math.random() * 1000),
      'BAR': Math.floor(Math.random() * 1000),
    };
    
    selectWinningPosition(randomBets);
    generateLightSequence(Math.floor(Math.random() * 25));
  }
  
  const endTime = performance.now();
  const duration = endTime - startTime;
  
  console.log(`⚡ اكتمل اختبار الأداء في ${duration.toFixed(2)} مللي ثانية`);
  console.log(`📊 متوسط الوقت لكل عملية: ${(duration / 100).toFixed(2)} مللي ثانية`);
  
  return duration;
};

// تصدير دالة للوصول من وحدة التحكم
declare global {
  interface Window {
    testGame: () => boolean;
    testPerformance: () => number;
  }
}

// إتاحة الاختبارات من وحدة التحكم
if (typeof window !== 'undefined') {
  window.testGame = runAllTests;
  window.testPerformance = performanceTest;
}
