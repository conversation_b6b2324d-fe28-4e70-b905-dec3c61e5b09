import React from 'react';
import { BetsOnTypes } from '../types/game';
import { BUTTON_POSITIONS } from '../utils/buttonPositions';

const BET_AMOUNTS = [5000, 3000, 1000, 500, 100];

// ألوان مختلفة لكل مبلغ رهان
const BET_COLORS = {
  5000: { bg: 'linear-gradient(135deg, #FF6B35, #F7931E)', border: '#FF6B35', color: '#fff' }, // برتقالي محمر - 5k
  3000: { bg: 'linear-gradient(135deg, #4ECDC4, #44A08D)', border: '#4ECDC4', color: '#fff' }, // تركوازي - 3k
  1000: { bg: 'linear-gradient(135deg, #9B59B6, #8E44AD)', border: '#9B59B6', color: '#fff' }, // بنفسجي - 1k
  500: { bg: 'linear-gradient(135deg, #A8E6CF, #3D9970)', border: '#A8E6CF', color: '#fff' }, // أخضر فاتح - 500
  100: { bg: 'linear-gradient(135deg, #FA7268, #C73E1D)', border: '#FA7268', color: '#fff' }  // أحمر وردي - 100
};

interface BettingAmountControlsProps {
  currentBetValueToApply: number;
  isBettingPhase: boolean;
  onBetValueSelect: (value: number) => void;
  onSpin: () => void;
  balance: number;
  pendingBetsOnTypes: BetsOnTypes;
}

const BettingAmountControls: React.FC<BettingAmountControlsProps> = ({
  currentBetValueToApply,
  isBettingPhase,
  onBetValueSelect,
  onSpin,
  balance,
  pendingBetsOnTypes
}) => {
  const totalBet = Object.values(pendingBetsOnTypes || {}).reduce((sum, bet) => sum + bet, 0);
  const canSpin = totalBet > 0 && balance >= totalBet && isBettingPhase;

  // تحديد الجهاز الحالي ديناميكيًا بناءً على أبعاد الشاشة
  const width = typeof window !== 'undefined' ? window.innerWidth : 428;
  const height = typeof window !== 'undefined' ? window.innerHeight : 926;

  // البحث عن التكوين المناسب للجهاز الحالي
  let deviceConfig = BUTTON_POSITIONS.find(d => {
    const widthMatch = Math.abs(d.device.width - width) <= 10;
    const heightMatch = Math.abs(d.device.height - height) <= 10;
    return widthMatch && heightMatch;
  });

  // إذا لم نجد تطابق دقيق، استخدم التكوين الأنسب حسب نوع الجهاز
  if (!deviceConfig) {
    if (width <= 768) {
      // الهواتف المحمولة
      deviceConfig = BUTTON_POSITIONS.find(d => d.device.width <= 768) || BUTTON_POSITIONS[0];
    } else {
      // الشاشات الكبيرة
      deviceConfig = BUTTON_POSITIONS.find(d => d.device.width > 768) || BUTTON_POSITIONS[1] || BUTTON_POSITIONS[0];
    }
  }

  const amountPositions = deviceConfig.amountButtons;

  return (
    <>
{/* المنطقة الذهبية تم إزالتها - الأزرار ستكون مخفية ولكن قابلة للتفاعل */}
      
      {BET_AMOUNTS.map((amount, idx) => {
        const isSelected = currentBetValueToApply === amount;
        const key = `amount${idx + 1}`;
        const pos = amountPositions[key];

        if (!pos) {
          return null;
        }

        const selectedColor = BET_COLORS[amount as keyof typeof BET_COLORS];
        
        return (
          <div
            key={amount}
            style={{
              position: 'absolute',
              left: pos.left,
              top: `calc(${pos.top} + 1px)`, // إنزال زر المبلغ للأسفل بمقدار 1 بكسل
              width: '32px',
              height: '32px',
              borderRadius: '50%',
              background: '#888', // فضي غامق
              border: isSelected
                ? `3px solid ${selectedColor.border}`
                : 'none', // بدون حدود عندما غير محدد
              boxShadow: isSelected
                ? `0 0 15px ${selectedColor.border}80, 0 0 30px ${selectedColor.border}60`
                : 'none', // بدون ظل عندما غير محدد
              zIndex: 30,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: isBettingPhase ? 'pointer' : 'not-allowed',
              transition: 'all 0.3s ease',
              fontWeight: 'bold',
              color: '#fff', // نص أبيض دائماً
              fontSize: '8px',
              opacity: 0, // إخفاء أزرار الفاكهة
              userSelect: 'none',
              transform: 'translate(-50%, -50%)',
              backdropFilter: 'none',
              pointerEvents: 'auto', // يبقى قابل للنقر
            }}
            onClick={() => {
              if (isBettingPhase) onBetValueSelect(amount);
            }}
            onMouseEnter={(e) => {
              if (isBettingPhase && !isSelected) {
                // عند التمرير فوق الزر، يضيء قليلاً
                e.currentTarget.style.opacity = '0.9';
                e.currentTarget.style.background = selectedColor.bg;
                e.currentTarget.style.border = `2px solid ${selectedColor.border}`;
                e.currentTarget.style.boxShadow = `0 0 10px ${selectedColor.border}40`;
                e.currentTarget.style.transform = 'translate(-50%, -50%) scale(1.1)';
              }
            }}
            onMouseLeave={(e) => {
              if (isBettingPhase && !isSelected) {
                // العودة للحالة الطبيعية
                e.currentTarget.style.opacity = '0.7';
                e.currentTarget.style.background = 'rgba(128, 128, 128, 0.3)';
                e.currentTarget.style.border = 'none';
                e.currentTarget.style.boxShadow = 'none';
                e.currentTarget.style.transform = 'translate(-50%, -50%) scale(1)';
              }
            }}
            title={`${amount >= 1000 ? `${amount / 1000}k` : amount} coins`}
          >
            <span style={{
              fontSize: '9px',
              lineHeight: '1',
              letterSpacing: '0.5px',
              color: '#fff', // نص أبيض
              fontWeight: 700
            }}>
              {pos.value}
            </span>
          </div>
        );
      })}

      {/* زر التأكيد - بجانب أزرار المبالغ */}
      <div style={{
        position: 'absolute',
        left: '80%', // تحريك لليمين أكثر
        top: '76%', // رفع قليلاً جداً
        transform: 'translate(-50%, -50%)',
        zIndex: 999
      }}>
        {/* الإطار الضوئي (يظهر فقط عند وجود رهانات) */}
        {canSpin && totalBet > 0 && (
          <>
            {/* إطار ناري متدرج حول زر الرهان، بدون لون داخلي */}
            <div
              style={{
                position: 'absolute',
                top: '-8px',
                left: '-8px',
                right: '-8px',
                bottom: '-8px',
                borderRadius: '14px',
                background: 'conic-gradient(from 0deg, #ff0000, #ff9900, #ff0000 100%)',
                zIndex: -2,
                pointerEvents: 'none',
                maskImage: 'linear-gradient(#fff 0 0)',
                WebkitMaskImage: 'linear-gradient(#fff 0 0)',
              }}
            />
          </>
        )}

        <div
          onClick={() => {
            if (onSpin && typeof onSpin === 'function' && canSpin && totalBet > 0) {
              onSpin();
            }
          }}
          style={{
            position: 'relative',
            padding: '8px 16px',
            height: '28px',
            width: '120px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            background: 'transparent',
            border: 'none',
            color: '#FFFF99',
            fontWeight: 'bold',
            fontSize: '14px',
            cursor: canSpin && totalBet > 0 ? 'pointer' : 'not-allowed',
            opacity: canSpin && totalBet > 0 ? 1 : 0.7,
            transition: 'all 0.3s ease',
            zIndex: 1000,
            textShadow: '2px 2px 4px #FF8C00, 0 0 10px rgba(255, 140, 0, 0.6)',
            boxShadow: 'none',
            outline: 'none',
          }}
        >
          {totalBet > 0 ? 'تأكيد' : 'الرهان'}
        </div>
      </div>

      {/* CSS للتأثيرات الضوئية */}
      <style>{`
        @keyframes lightFrame {
          0% {
            background-position: 0% 0%;
            opacity: 0.9;
            transform: scale(1);
          }
          25% {
            background-position: 100% 0%;
            opacity: 1;
            transform: scale(1.02);
          }
          50% {
            background-position: 100% 100%;
            opacity: 0.9;
            transform: scale(1);
          }
          75% {
            background-position: 0% 100%;
            opacity: 1;
            transform: scale(1.02);
          }
          100% {
            background-position: 0% 0%;
            opacity: 0.9;
            transform: scale(1);
          }
        }

        @keyframes lightPulse {
          0% {
            background-position: 0% 50%;
            opacity: 0.8;
            box-shadow: 0 0 20px rgba(255, 0, 0, 0.5);
          }
          50% {
            background-position: 100% 50%;
            opacity: 1;
            box-shadow: 0 0 30px rgba(255, 215, 0, 0.8);
          }
          100% {
            background-position: 0% 50%;
            opacity: 0.8;
            box-shadow: 0 0 20px rgba(255, 0, 0, 0.5);
          }
        }

        @keyframes buttonGlow {
          0% {
            transform: scale(1);
            box-shadow: 0 4px 12px rgba(255, 215, 0, 0.6), inset 0 2px 4px rgba(255,255,255,0.3);
          }
          50% {
            transform: scale(1.05);
            box-shadow: 0 4px 20px rgba(255, 215, 0, 0.8), inset 0 2px 6px rgba(255,255,255,0.5);
          }
          100% {
            transform: scale(1);
            box-shadow: 0 4px 12px rgba(255, 215, 0, 0.6), inset 0 2px 4px rgba(255,255,255,0.3);
          }
        }

        @keyframes goldenAreaPulse {
          0% {
            opacity: 0.6;
            transform: translate(-50%, -50%) scale(1);
            box-shadow: 0 0 30px rgba(255, 215, 0, 0.6), 0 0 60px rgba(255, 215, 0, 0.4);
          }
          100% {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1.1);
            box-shadow: 0 0 50px rgba(255, 215, 0, 0.8), 0 0 100px rgba(255, 215, 0, 0.6);
          }
        }
      `}</style>
    </>
  );
};

export default BettingAmountControls;
