// Type definitions for layout files

export interface Position {
  left: string;
  top: string;
  bottom?: string;
  name?: string;
  value?: number;
  action?: string;
  type?: string;
  info?: string;
  symbol?: string;
}

export interface DeviceInfo {
  name: string;
  width: number;
  height: number;
  type?: string;
  dpr?: number;
  scale?: number;
}

export interface Layout {
  device: DeviceInfo;
  backgroundImage: string;
  symbolButtons: {
    [key: string]: Position;
  };
  betDisplays?: Position[];
  amountButtons?: {
    [key: string]: Position;
  };
  actionButtons?: {
    [key: string]: Position;
  };
  displayElements?: {
    [key: string]: Position;
  };
  betRectangles?: {
    [key: string]: Position;
  };
  betNumbers?: {
    [key: string]: Position;
  };
}
