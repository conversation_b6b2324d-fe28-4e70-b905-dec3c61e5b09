import React, { useState } from 'react';

interface EasyButtonMoverProps {
  isVisible: boolean;
  onToggle: () => void;
}

const EasyButtonMover: React.FC<EasyButtonMoverProps> = ({ isVisible, onToggle }) => {
  const [isActive, setIsActive] = useState(false);

  if (!isVisible) {
    return (
      <button
        onClick={onToggle}
        style={{
          position: 'fixed',
          top: '10px',
          right: '10px',
          padding: '10px',
          background: '#4CAF50',
          color: 'white',
          border: 'none',
          borderRadius: '5px',
          cursor: 'pointer',
          fontSize: '12px',
          zIndex: 10000,
        }}
      >
        🎮 تحريك الأزرار
      </button>
    );
  }

  const startMoving = () => {
    setIsActive(true);
    
    // إضافة أسلوب CSS مباشر للصفحة
    const style = document.createElement('style');
    style.id = 'button-mover-style';
    style.textContent = `
      .movable-button {
        border: 2px solid #4CAF50 !important;
        cursor: move !important;
        position: relative !important;
      }
      .movable-button:hover {
        border-color: #45a049 !important;
        box-shadow: 0 0 10px rgba(76, 175, 80, 0.5) !important;
      }
    `;
    document.head.appendChild(style);

    // البحث عن الأزرار وجعلها قابلة للتحريك
    const buttons = document.querySelectorAll('img, [style*="position"], button, div[onclick]');
    buttons.forEach((button: any) => {
      if (button.offsetWidth > 0 && button.offsetHeight > 0 && 
          button.offsetWidth < 200 && button.offsetHeight < 200) {
        
        button.classList.add('movable-button');
        button.draggable = true;
        
        button.addEventListener('dragstart', (e: any) => {
          e.dataTransfer.setData('text/plain', '');
        });
        
        button.addEventListener('dragend', (e: any) => {
          const rect = button.getBoundingClientRect();
          const leftPercent = (rect.left / window.innerWidth) * 100;
          const topPercent = (rect.top / window.innerHeight) * 100;
          
          button.style.position = 'absolute';
          button.style.left = leftPercent + '%';
          button.style.top = topPercent + '%';
          
    
        });
      }
    });

    alert('✅ تم تفعيل وضع التحريك!\n\n🖱️ اسحب أي زر لتحريكه\n🟢 الأزرار ذات الإطار الأخضر قابلة للتحريك');
  };

  const stopMoving = () => {
    setIsActive(false);
    
    // إزالة الأسلوب
    const style = document.getElementById('button-mover-style');
    if (style) style.remove();
    
    // إزالة الكلاسات
    document.querySelectorAll('.movable-button').forEach(button => {
      button.classList.remove('movable-button');
      (button as any).draggable = false;
    });

    alert('🔒 تم إيقاف وضع التحريك');
  };

  return (
    <div style={{
      position: 'fixed',
      top: '10px',
      right: '10px',
      background: 'white',
      border: '2px solid #4CAF50',
      borderRadius: '10px',
      padding: '15px',
      zIndex: 10000,
      boxShadow: '0 4px 8px rgba(0,0,0,0.2)',
    }}>
      <div style={{ marginBottom: '10px', fontWeight: 'bold', textAlign: 'center' }}>
        🎮 تحريك الأزرار
      </div>
      
      <div style={{ marginBottom: '10px', fontSize: '12px', color: '#666' }}>
        {isActive ? '🟢 وضع التحريك مفعل' : '⚪ وضع التحريك معطل'}
      </div>

      <div style={{ display: 'flex', gap: '10px', marginBottom: '10px' }}>
        {!isActive ? (
          <button
            onClick={startMoving}
            style={{
              padding: '8px 12px',
              background: '#4CAF50',
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              cursor: 'pointer',
              fontSize: '12px',
            }}
          >
            ✅ تفعيل
          </button>
        ) : (
          <button
            onClick={stopMoving}
            style={{
              padding: '8px 12px',
              background: '#f44336',
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              cursor: 'pointer',
              fontSize: '12px',
            }}
          >
            🔒 إيقاف
          </button>
        )}
        
        <button
          onClick={onToggle}
          style={{
            padding: '8px 12px',
            background: '#2196F3',
            color: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer',
            fontSize: '12px',
          }}
        >
          ✕ إغلاق
        </button>
      </div>

      <div style={{ fontSize: '11px', color: '#888', lineHeight: '1.4' }}>
        💡 <strong>كيفية الاستخدام:</strong><br/>
        1. اضغط "تفعيل"<br/>
        2. اسحب أي زر أخضر<br/>
        3. اضغط "إيقاف" عند الانتهاء
      </div>
    </div>
  );
};

export default EasyButtonMover;
