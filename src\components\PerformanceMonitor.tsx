import React, { useState, useEffect, useRef, useMemo } from 'react';

interface PerformanceMetrics {
  fps: number;
  memoryUsage: number;
  renderTime: number;
  componentCount: number;
  lastUpdate: number;
}

interface PerformanceMonitorProps {
  isVisible?: boolean;
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
}

const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({
  isVisible = false,
  position = 'top-right'
}) => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    fps: 0,
    memoryUsage: 0,
    renderTime: 0,
    componentCount: 0,
    lastUpdate: Date.now()
  });

  const frameCountRef = useRef(0);
  const lastTimeRef = useRef(performance.now());
  const renderStartRef = useRef(0);
  const intervalsRef = useRef<{ [key: string]: NodeJS.Timeout | number }>({});

  // useEffect واحد لجميع العمليات
  useEffect(() => {
    if (!isVisible) {
      return;
    }

    let animationId: number;
    let memoryInterval: NodeJS.Timeout;
    let componentInterval: NodeJS.Timeout;
    let renderTimeout: NodeJS.Timeout;

    // حساب FPS
    const animate = () => {
      const now = performance.now();
      frameCountRef.current++;

      if (now - lastTimeRef.current >= 1000) {
        const fps = Math.round((frameCountRef.current * 1000) / (now - lastTimeRef.current));

        setMetrics(prev => ({
          ...prev,
          fps,
          lastUpdate: Date.now()
        }));

        frameCountRef.current = 0;
        lastTimeRef.current = now;
      }

      animationId = requestAnimationFrame(animate);
    };

    // حساب استخدام الذاكرة
    const updateMemory = () => {
      try {
        if ('memory' in performance) {
          const memory = (performance as any).memory;
          const memoryUsage = Math.round(memory.usedJSHeapSize / 1024 / 1024);

          setMetrics(prev => ({
            ...prev,
            memoryUsage
          }));
        }
      } catch (error) {
        console.warn('Error reading memory usage:', error);
      }
    };

    // حساب عدد المكونات
    const countComponents = () => {
      try {
        const allElements = document.querySelectorAll('*');
        const reactElements = Array.from(allElements).filter(el =>
          el.hasAttribute('data-reactroot') ||
          el.className.includes('react') ||
          el.tagName.toLowerCase().includes('react')
        );

        setMetrics(prev => ({
          ...prev,
          componentCount: reactElements.length || Math.floor(allElements.length / 10)
        }));
      } catch (error) {
        console.warn('Error counting components:', error);
      }
    };

    // بدء جميع العمليات
    animate();
    updateMemory();
    countComponents();

    // إعداد الـ intervals
    memoryInterval = setInterval(updateMemory, 2000);
    componentInterval = setInterval(countComponents, 5000);

    // حساب وقت الرندر
    renderStartRef.current = performance.now();
    renderTimeout = setTimeout(() => {
      const renderTime = performance.now() - renderStartRef.current;
      setMetrics(prev => ({
        ...prev,
        renderTime: Math.round(renderTime * 100) / 100
      }));
    }, 16);

    return () => {
      if (animationId) cancelAnimationFrame(animationId);
      if (memoryInterval) clearInterval(memoryInterval);
      if (componentInterval) clearInterval(componentInterval);
      if (renderTimeout) clearTimeout(renderTimeout);
    };
  }, [isVisible]);

  const positionStyles = useMemo(() => {
    const baseStyles = {
      position: 'fixed' as const,
      zIndex: 9999,
      background: 'rgba(0, 0, 0, 0.8)',
      color: '#00ff00',
      padding: '8px 12px',
      borderRadius: '4px',
      fontFamily: 'monospace',
      fontSize: '11px',
      lineHeight: '1.2',
      minWidth: '150px',
      backdropFilter: 'blur(4px)',
      border: '1px solid rgba(0, 255, 0, 0.3)'
    };

    switch (position) {
      case 'top-left':
        return { ...baseStyles, top: '10px', left: '10px' };
      case 'top-right':
        return { ...baseStyles, top: '10px', right: '10px' };
      case 'bottom-left':
        return { ...baseStyles, bottom: '10px', left: '10px' };
      case 'bottom-right':
        return { ...baseStyles, bottom: '10px', right: '10px' };
      default:
        return { ...baseStyles, top: '10px', right: '10px' };
    }
  }, [position]);

  if (!isVisible) return null;

  const getFPSColor = useCallback((fps: number) => {
    if (fps >= 55) return '#00ff00'; // أخضر
    if (fps >= 30) return '#ffff00'; // أصفر
    return '#ff0000'; // أحمر
  }, []);

  const getMemoryColor = useCallback((memory: number) => {
    if (memory < 50) return '#00ff00'; // أخضر
    if (memory < 100) return '#ffff00'; // أصفر
    return '#ff0000'; // أحمر
  }, []);

  const getRenderTimeColor = useCallback((renderTime: number) => {
    return renderTime > 16 ? '#ff0000' : '#00ff00';
  }, []);

  return (
    <div style={positionStyles}>
      <div style={{ marginBottom: '4px', color: '#ffffff', fontWeight: 'bold' }}>
        ⚡ مراقب الأداء
      </div>
      
      <div style={{ color: getFPSColor(metrics.fps) }}>
        FPS: {metrics.fps}
      </div>
      
      <div style={{ color: getMemoryColor(metrics.memoryUsage) }}>
        الذاكرة: {metrics.memoryUsage} MB
      </div>
      
      <div style={{ color: getRenderTimeColor(metrics.renderTime) }}>
        الرندر: {metrics.renderTime}ms
      </div>
      
      <div style={{ color: '#00ffff' }}>
        المكونات: {metrics.componentCount}
      </div>
      
      <div style={{ color: '#888888', fontSize: '9px', marginTop: '4px' }}>
        آخر تحديث: {new Date(metrics.lastUpdate).toLocaleTimeString()}
      </div>
    </div>
  );
};

export default PerformanceMonitor;
