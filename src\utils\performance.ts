/**
 * دوال مساعدة لتحسين الأداء
 */

// دالة debounce لتقليل عدد استدعاءات الدوال
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

// دالة throttle لتحديد معدل استدعاء الدوال
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

// دالة لتحسين الـ animations باستخدام requestAnimationFrame
export const animateWithRAF = (callback: () => void) => {
  return requestAnimationFrame(callback);
};

// دالة لإلغاء الـ animation
export const cancelRAF = (id: number) => {
  cancelAnimationFrame(id);
};

// دالة لتحسين الـ scroll performance
export const optimizeScroll = (element: HTMLElement, callback: () => void) => {
  let ticking = false;
  
  const handleScroll = () => {
    if (!ticking) {
      requestAnimationFrame(() => {
        callback();
        ticking = false;
      });
      ticking = true;
    }
  };
  
  element.addEventListener('scroll', handleScroll, { passive: true });
  
  return () => {
    element.removeEventListener('scroll', handleScroll);
  };
};

// دالة لتحسين الـ resize performance
export const optimizeResize = (callback: () => void) => {
  let ticking = false;
  
  const handleResize = () => {
    if (!ticking) {
      requestAnimationFrame(() => {
        callback();
        ticking = false;
      });
      ticking = true;
    }
  };
  
  window.addEventListener('resize', handleResize, { passive: true });
  
  return () => {
    window.removeEventListener('resize', handleResize);
  };
};

// دالة لحفظ البيانات في localStorage مع error handling
export const saveToLocalStorage = (key: string, value: any): boolean => {
  try {
    localStorage.setItem(key, JSON.stringify(value));
    return true;
  } catch (error) {
    console.error('Error saving to localStorage:', error);
    return false;
  }
};

// دالة لقراءة البيانات من localStorage مع error handling
export const loadFromLocalStorage = <T>(key: string, defaultValue: T): T => {
  try {
    const item = localStorage.getItem(key);
    return item ? JSON.parse(item) : defaultValue;
  } catch (error) {
    console.error('Error loading from localStorage:', error);
    return defaultValue;
  }
};

// دالة لتحسين الصور lazy loading
export const createImageLoader = () => {
  const imageCache = new Map<string, HTMLImageElement>();
  
  const loadImage = (src: string): Promise<HTMLImageElement> => {
    if (imageCache.has(src)) {
      return Promise.resolve(imageCache.get(src)!);
    }
    
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        imageCache.set(src, img);
        resolve(img);
      };
      img.onerror = reject;
      img.src = src;
    });
  };
  
  const preloadImages = (sources: string[]): Promise<HTMLImageElement[]> => {
    return Promise.all(sources.map(loadImage));
  };
  
  return { loadImage, preloadImages, imageCache };
};

// دالة لتحسين الـ memory usage
export const createMemoryOptimizer = () => {
  const cleanup: (() => void)[] = [];
  
  const addCleanup = (cleanupFn: () => void) => {
    cleanup.push(cleanupFn);
  };
  
  const runCleanup = () => {
    cleanup.forEach(fn => {
      try {
        fn();
      } catch (error) {
        console.error('Error during cleanup:', error);
      }
    });
    cleanup.length = 0;
  };
  
  return { addCleanup, runCleanup };
};

// دالة لقياس الأداء
export const measurePerformance = (name: string, fn: () => void) => {
  const start = performance.now();
  fn();
  const end = performance.now();
  console.log(`${name} took ${end - start} milliseconds`);
};

// دالة لتحسين الـ event listeners
export const createEventManager = () => {
  const listeners: Array<{
    element: EventTarget;
    event: string;
    handler: EventListener;
    options?: AddEventListenerOptions;
  }> = [];
  
  const addEventListener = (
    element: EventTarget,
    event: string,
    handler: EventListener,
    options?: AddEventListenerOptions
  ) => {
    element.addEventListener(event, handler, options);
    listeners.push({ element, event, handler, options });
  };
  
  const removeAllListeners = () => {
    listeners.forEach(({ element, event, handler, options }) => {
      element.removeEventListener(event, handler, options);
    });
    listeners.length = 0;
  };
  
  return { addEventListener, removeAllListeners };
};
