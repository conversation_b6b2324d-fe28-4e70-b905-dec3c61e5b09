# دليل ضبط مواقع أزرار اللعبة

## نظرة عامة

يستخدم المشروع نظام مركزي لإدارة مواقع جميع أزرار اللعبة عبر أجهزة مختلفة. هذا النظام يضمن أن الأزرار تظهر في المواقع الصحيحة فوق الخلفية لكل جهاز.

## الملفات الرئيسية

### 1. `src/utils/buttonPositions.ts`
الملف المركزي الذي يحتوي على جميع مواقع الأزرار لكل جهاز:

```typescript
export const BUTTON_POSITIONS: DeviceButtonPositions[] = [
  {
    device: { name: 'iPhone 14 Pro Max', width: 428, height: 926 },
    backgroundImage: '/images/bg-428x926.webp',
    symbolButtons: { /* مواقع أزرار الفواكه */ },
    amountButtons: { /* مواقع أزرار المبالغ */ },
    betRectangles: { /* مواقع عرض الرهانات */ },
    topDisplays: { /* مواقع عرض الرصيد */ },
    gameBoard: { /* موقع لوحة اللعبة */ }
  }
];
```

### 2. `src/components/BettingControlsSimple.tsx`
مكون عرض أزرار الفواكه والتحكم في الرهانات.

### 3. `src/components/BettingAmountControls.tsx`
مكون عرض أزرار المبالغ.

### 4. `src/components/ButtonPositionDebugger.tsx`
أداة تصحيح لرؤية مواقع الأزرار بصرياً (تظهر فقط في وضع التطوير).

## كيفية ضبط مواقع الأزرار

### الطريقة الأولى: استخدام أداة التصحيح

1. تأكد من أن المشروع يعمل في وضع التطوير (`npm run dev`)
2. ابحث عن زر "🎯 Debug Positions" في الزاوية اليمنى العلوية
3. اضغط على الزر لفتح أداة التصحيح
4. ستظهر نقاط ملونة تشير إلى مواقع الأزرار:
   - 🔴 أزرار الفواكه (Symbol Buttons)
   - 🟢 أزرار المبالغ (Amount Buttons)  
   - 🟡 مربعات عرض الرهانات (Bet Rectangles)

### الطريقة الثانية: التعديل المباشر

1. افتح ملف `src/utils/buttonPositions.ts`
2. ابحث عن التكوين المناسب لجهازك
3. عدّل المواقع باستخدام النسب المئوية:

```typescript
symbolButtons: {
  bar: { left: '9%', top: '75%', name: 'BAR' },
  watermelon: { left: '28%', top: '76%', name: 'WATERMELON' },
  // ... باقي الأزرار
}
```

## أنواع المواقع

### 1. أزرار الفواكه (symbolButtons)
الأزرار الرئيسية للرهان على الفواكه المختلفة.

### 2. أزرار المبالغ (amountButtons)
أزرار اختيار مبلغ الرهان (100, 500, 1000, 3000, 5000).

### 3. مربعات الرهانات (betRectangles)
المناطق التي تظهر فيها أرقام الرهانات الحالية.

### 4. العروض العلوية (topDisplays)
مناطق عرض الرصيد والرهان الإجمالي.

### 5. لوحة اللعبة (gameBoard)
موقع وحجم لوحة اللعبة الرئيسية.

## إضافة جهاز جديد

لإضافة دعم لجهاز جديد:

1. افتح `src/utils/buttonPositions.ts`
2. أضف تكوين جديد إلى مصفوفة `BUTTON_POSITIONS`:

```typescript
{
  device: {
    name: 'iPad Pro',
    width: 1024,
    height: 1366,
  },
  backgroundImage: '/images/bg-1024x1366.webp',
  symbolButtons: {
    // حدد مواقع الأزرار هنا
  },
  // ... باقي المواقع
}
```

## نصائح مهمة

### 1. استخدم النسب المئوية
```typescript
// ✅ صحيح
{ left: '50%', top: '75%' }

// ❌ خطأ
{ left: '200px', top: '300px' }
```

### 2. اختبر على أجهزة مختلفة
- الهواتف المحمولة (عمودي وأفقي)
- الأجهزة اللوحية
- الشاشات الكبيرة

### 3. استخدم أداة التصحيح
أداة التصحيح تساعدك في رؤية المواقع الحالية وضبطها بدقة.

### 4. احفظ نسخة احتياطية
قبل إجراء تغييرات كبيرة، احفظ نسخة من الملف الأصلي.

## استكشاف الأخطاء

### المشكلة: الأزرار لا تظهر
- تأكد من أن المواقع محددة بشكل صحيح
- تحقق من أن اسم الجهاز يطابق التكوين

### المشكلة: الأزرار في مواقع خاطئة
- استخدم أداة التصحيح لرؤية المواقع الحالية
- تأكد من استخدام النسب المئوية بدلاً من القيم المطلقة

### المشكلة: الأزرار تختفي عند تغيير حجم الشاشة
- تأكد من أن النظام يكتشف الجهاز بشكل صحيح
- أضف تكوينات إضافية للأحجام المختلفة

## مثال كامل

```typescript
{
  device: {
    name: 'iPhone 14 Pro Max',
    width: 428,
    height: 926,
  },
  backgroundImage: '/images/bg-428x926.webp',
  symbolButtons: {
    bar: { left: '9%', top: '75%', name: 'BAR' },
    watermelon: { left: '28%', top: '76%', name: 'WATERMELON' },
    lemon: { left: '50%', top: '76%', name: 'LEMON' },
    banana: { left: '72%', top: '76%', name: 'BANANA' },
    apple: { left: '91%', top: '75%', name: 'APPLE' },
  },
  amountButtons: {
    amount1: { left: '10%', top: '82%', name: 'AMOUNT_1', value: 5000 },
    amount2: { left: '30%', top: '82%', name: 'AMOUNT_2', value: 3000 },
    amount3: { left: '50%', top: '82%', name: 'AMOUNT_3', value: 1000 },
    amount4: { left: '70%', top: '82%', name: 'AMOUNT_4', value: 500 },
    amount5: { left: '90%', top: '82%', name: 'AMOUNT_5', value: 100 },
  },
  betRectangles: {
    bar: { left: '9%', top: '68%', symbol: 'BAR' },
    watermelon: { left: '28%', top: '67%', symbol: 'WATERMELON' },
    lemon: { left: '50%', top: '67%', symbol: 'LEMON' },
    banana: { left: '72%', top: '67%', symbol: 'BANANA' },
    apple: { left: '91%', top: '68%', symbol: 'APPLE' },
  },
  topDisplays: {
    balanceDisplay: { left: '20%', top: '12%', name: 'BALANCE' },
    totalBetDisplay: { left: '80%', top: '12%', name: 'TOTAL_BET' },
  },
  gameBoard: {
    left: '50%',
    top: '35%',
    width: 'min(380px, 85vw)',
    height: 'min(300px, 50vh)',
    transform: 'translateX(-50%)',
    position: 'absolute',
    zIndex: 10,
  },
}
```
