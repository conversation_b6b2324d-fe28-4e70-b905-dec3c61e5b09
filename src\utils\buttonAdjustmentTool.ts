// أداة ضبط مواقع الأزرار مباشرة من المتصفح
// Button Position Adjustment Tool

export interface ButtonAdjustment {
  key: string;
  name: string;
  left: string;
  top: string;
}

// دالة لتفعيل وضع التعديل
export function enableEditMode(): void {
  console.log('🔧 تم تفعيل وضع التعديل');
  console.log('💡 اضغط على أي زر واسحبه لتغيير موقعه');
  console.log('💾 استخدم saveAdjustments() لحفظ التغييرات');

  const buttons = document.querySelectorAll('[data-symbol]') as NodeListOf<HTMLElement>;

  console.log(`🔍 تم العثور على ${buttons.length} زر`);

  if (buttons.length === 0) {
    console.log('⚠️ لم يتم العثور على أزرار. جرب:');
    console.log('1. تأكد من أن الأزرار ظاهرة على الشاشة');
    console.log('2. أعد تحميل الصفحة وجرب مرة أخرى');
    console.log('3. استخدم forceEnableEditMode() للتفعيل القسري');
    return;
  }

  buttons.forEach((button, index) => {
    console.log(`🎯 تفعيل السحب للزر ${index + 1}: ${button.getAttribute('data-symbol')}`);
    makeButtonDraggable(button);
  });
}

// دالة لإلغاء وضع التعديل
export function disableEditMode(): void {
  console.log('❌ تم إلغاء وضع التعديل');
  
  const buttons = document.querySelectorAll('[data-symbol]') as NodeListOf<HTMLElement>;
  
  buttons.forEach(button => {
    button.style.cursor = 'pointer';
    button.onmousedown = null;
  });
}

// دالة لجعل الزر قابل للسحب (محسنة)
function makeButtonDraggable(button: HTMLElement): void {
  console.log(`🎯 تفعيل السحب للزر: ${button.getAttribute('data-symbol') || 'غير معروف'}`);

  // تأكد من أن الزر قابل للتفاعل
  button.style.cursor = 'move';
  button.style.userSelect = 'none';
  button.style.pointerEvents = 'auto';

  let isDragging = false;
  let startX = 0;
  let startY = 0;
  let startLeft = 0;
  let startTop = 0;

  // إزالة أي event listeners سابقة
  button.onmousedown = null;
  button.ontouchstart = null;

  // دالة بداية السحب
  const startDrag = (clientX: number, clientY: number, e: Event) => {
    e.preventDefault();
    e.stopPropagation();

    isDragging = true;
    startX = clientX;
    startY = clientY;

    // الحصول على الحاوي الأساسي (الشاشة كاملة)
    const container = document.documentElement;
    const containerRect = container.getBoundingClientRect();
    const buttonRect = button.getBoundingClientRect();

    // حساب الموقع الحالي كنسبة مئوية
    startLeft = ((buttonRect.left + buttonRect.width/2) / window.innerWidth) * 100;
    startTop = ((buttonRect.top + buttonRect.height/2) / window.innerHeight) * 100;

    // تغيير مظهر الزر أثناء السحب
    button.style.borderColor = '#ff0066';
    button.style.boxShadow = '0 0 20px rgba(255, 0, 102, 0.8)';
    button.style.transform = 'translate(-50%, -50%) scale(1.1)';
    button.style.zIndex = '9999';

    console.log(`🚀 بدء السحب: ${button.getAttribute('data-symbol')} من ${startLeft.toFixed(2)}%, ${startTop.toFixed(2)}%`);
  };

  // دالة السحب
  const drag = (clientX: number, clientY: number) => {
    if (!isDragging) return;

    const deltaX = clientX - startX;
    const deltaY = clientY - startY;

    // حساب التغيير كنسبة مئوية من حجم الشاشة
    const deltaLeftPercent = (deltaX / window.innerWidth) * 100;
    const deltaTopPercent = (deltaY / window.innerHeight) * 100;

    // حساب الموقع الجديد
    const newLeft = Math.max(5, Math.min(95, startLeft + deltaLeftPercent));
    const newTop = Math.max(5, Math.min(95, startTop + deltaTopPercent));

    // تطبيق الموقع الجديد
    button.style.left = `${newLeft}%`;
    button.style.top = `${newTop}%`;

    // عرض الموقع الجديد
    const symbolName = button.getAttribute('data-symbol');
    console.log(`📍 ${symbolName}: ${newLeft.toFixed(2)}%, ${newTop.toFixed(2)}%`);
  };

  // دالة إنهاء السحب
  const endDrag = () => {
    if (!isDragging) return;

    isDragging = false;

    // إعادة مظهر الزر للحالة الطبيعية
    button.style.borderColor = '#00ff88';
    button.style.boxShadow = 'none';
    button.style.transform = 'translate(-50%, -50%) scale(1)';
    button.style.zIndex = '20';

    console.log(`✅ انتهى السحب: ${button.getAttribute('data-symbol')}`);

    // إزالة event listeners
    document.onmousemove = null;
    document.onmouseup = null;
    document.ontouchmove = null;
    document.ontouchend = null;
  };

  // Mouse events
  button.onmousedown = (e: MouseEvent) => {
    startDrag(e.clientX, e.clientY, e);

    document.onmousemove = (e: MouseEvent) => {
      drag(e.clientX, e.clientY);
    };

    document.onmouseup = endDrag;
  };

  // Touch events للأجهزة اللمسية
  button.ontouchstart = (e: TouchEvent) => {
    const touch = e.touches[0];
    startDrag(touch.clientX, touch.clientY, e);

    document.ontouchmove = (e: TouchEvent) => {
      const touch = e.touches[0];
      drag(touch.clientX, touch.clientY);
    };

    document.ontouchend = endDrag;
  };

  console.log(`✅ تم تفعيل السحب للزر: ${button.getAttribute('data-symbol')}`);
}

// دالة لحفظ التعديلات
export function saveAdjustments(): ButtonAdjustment[] {
  const buttons = document.querySelectorAll('[data-symbol]') as NodeListOf<HTMLElement>;
  const adjustments: ButtonAdjustment[] = [];
  
  buttons.forEach(button => {
    const symbolName = button.getAttribute('data-symbol')!;
    const key = button.getAttribute('data-key')!;
    const left = button.style.left;
    const top = button.style.top;
    
    adjustments.push({
      key,
      name: symbolName,
      left,
      top
    });
  });
  
  console.log('💾 التعديلات المحفوظة:');
  console.log('buttonLayout: {');
  adjustments.forEach(adj => {
    console.log(`  ${adj.key}: { left: '${adj.left}', top: '${adj.top}', name: '${adj.name}' },`);
  });
  console.log('}');
  
  return adjustments;
}

// دالة لإعادة تعيين المواقع
export function resetPositions(): void {
  console.log('🔄 إعادة تحميل المواقع الأصلية...');
  window.location.reload();
}

// دالة لعرض شبكة مساعدة
export function showGrid(): void {
  const existingGrid = document.getElementById('position-grid');
  if (existingGrid) {
    existingGrid.remove();
    console.log('❌ تم إخفاء الشبكة');
    return;
  }
  
  const grid = document.createElement('div');
  grid.id = 'position-grid';
  grid.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    pointer-events: none;
    z-index: 15;
    background-image: 
      linear-gradient(rgba(0, 255, 136, 0.2) 1px, transparent 1px),
      linear-gradient(90deg, rgba(0, 255, 136, 0.2) 1px, transparent 1px);
    background-size: 10% 10%;
  `;
  
  document.body.appendChild(grid);
  console.log('✅ تم عرض الشبكة المساعدة');
  console.log('💡 استخدم showGrid() مرة أخرى لإخفائها');
}

// دالة لعرض معلومات الموقع عند النقر
export function showPositionInfo(): void {
  console.log('📍 وضع عرض المعلومات مفعل');
  console.log('💡 اضغط على أي مكان لعرض الإحداثيات');
  
  const handler = (e: MouseEvent) => {
    const rect = document.documentElement.getBoundingClientRect();
    const x = ((e.clientX - rect.left) / rect.width) * 100;
    const y = ((e.clientY - rect.top) / rect.height) * 100;
    
    console.log(`📍 الموقع: ${x.toFixed(2)}%, ${y.toFixed(2)}%`);
    
    // إنشاء نقطة مؤقتة
    const dot = document.createElement('div');
    dot.style.cssText = `
      position: fixed;
      left: ${e.clientX}px;
      top: ${e.clientY}px;
      width: 10px;
      height: 10px;
      background: #ff0066;
      border-radius: 50%;
      z-index: 9999;
      transform: translate(-50%, -50%);
      pointer-events: none;
    `;
    
    document.body.appendChild(dot);
    
    setTimeout(() => {
      dot.remove();
    }, 2000);
  };
  
  document.addEventListener('click', handler, { once: true });
}

// دالة تفعيل قسري للتعديل
export function forceEnableEditMode(): void {
  console.log('🔧 تفعيل قسري لوضع التعديل...');

  // البحث عن الأزرار بطرق مختلفة
  const selectors = [
    '[data-symbol]',
    '.responsive-button',
    'button[data-symbol]',
    '[data-key]'
  ];

  let foundButtons: HTMLElement[] = [];

  selectors.forEach(selector => {
    const buttons = document.querySelectorAll(selector) as NodeListOf<HTMLElement>;
    if (buttons.length > 0) {
      console.log(`✅ وُجد ${buttons.length} زر باستخدام ${selector}`);
      foundButtons = Array.from(buttons);
    }
  });

  if (foundButtons.length === 0) {
    console.log('❌ لم يتم العثور على أي أزرار');
    console.log('🔍 البحث عن جميع الأزرار في الصفحة...');

    const allButtons = document.querySelectorAll('button') as NodeListOf<HTMLElement>;
    console.log(`📊 إجمالي الأزرار في الصفحة: ${allButtons.length}`);

    allButtons.forEach((button, index) => {
      console.log(`🔘 زر ${index + 1}: ${button.textContent?.trim() || 'بدون نص'}`);
    });

    return;
  }

  foundButtons.forEach((button, index) => {
    console.log(`🎯 تفعيل السحب للزر ${index + 1}: ${button.getAttribute('data-symbol') || button.textContent?.trim()}`);
    makeButtonDraggable(button);
  });

  console.log('✅ تم تفعيل وضع التعديل القسري');
}

// دالة اختبار سريع للسحب
export function testDragMode(): void {
  console.log('🧪 اختبار وضع السحب...');

  const buttons = document.querySelectorAll('[data-symbol]') as NodeListOf<HTMLElement>;

  if (buttons.length === 0) {
    console.log('❌ لا توجد أزرار للاختبار');
    return;
  }

  buttons.forEach((button, index) => {
    console.log(`🔍 اختبار الزر ${index + 1}:`);
    console.log(`  - الرمز: ${button.getAttribute('data-symbol')}`);
    console.log(`  - المؤشر: ${button.style.cursor}`);
    console.log(`  - الأحداث: ${button.onmousedown ? 'موجودة' : 'مفقودة'}`);
    console.log(`  - المرئية: ${button.offsetWidth > 0 && button.offsetHeight > 0}`);

    // اختبار تغيير المؤشر
    button.addEventListener('mouseenter', () => {
      console.log(`🎯 دخول المؤشر على ${button.getAttribute('data-symbol')}`);
    }, { once: true });
  });

  console.log('💡 مرر المؤشر على الأزرار لاختبار التفاعل');
}

// دالة لعرض معلومات الأزرار
export function showButtonsInfo(): void {
  console.log('📊 معلومات الأزرار الموجودة:');

  const buttons = document.querySelectorAll('[data-symbol]') as NodeListOf<HTMLElement>;

  if (buttons.length === 0) {
    console.log('❌ لا توجد أزرار بخاصية data-symbol');

    const allButtons = document.querySelectorAll('button') as NodeListOf<HTMLElement>;
    console.log(`📊 إجمالي الأزرار: ${allButtons.length}`);

    allButtons.forEach((button, index) => {
      const rect = button.getBoundingClientRect();
      console.log(`🔘 زر ${index + 1}:`, {
        text: button.textContent?.trim(),
        visible: rect.width > 0 && rect.height > 0,
        position: `${rect.left.toFixed(0)}, ${rect.top.toFixed(0)}`,
        size: `${rect.width.toFixed(0)}×${rect.height.toFixed(0)}`
      });
    });
  } else {
    buttons.forEach((button, index) => {
      const rect = button.getBoundingClientRect();
      console.log(`🎯 زر ${index + 1}:`, {
        symbol: button.getAttribute('data-symbol'),
        key: button.getAttribute('data-key'),
        visible: rect.width > 0 && rect.height > 0,
        position: `${rect.left.toFixed(0)}, ${rect.top.toFixed(0)}`,
        size: `${rect.width.toFixed(0)}×${rect.height.toFixed(0)}`
      });
    });
  }
}

// إضافة الأوامر لوحدة التحكم
if (typeof window !== 'undefined') {
  (window as any).enableEditMode = enableEditMode;
  (window as any).disableEditMode = disableEditMode;
  (window as any).saveAdjustments = saveAdjustments;
  (window as any).resetPositions = resetPositions;
  (window as any).showGrid = showGrid;
  (window as any).showPositionInfo = showPositionInfo;
  (window as any).forceEnableEditMode = forceEnableEditMode;
  (window as any).showButtonsInfo = showButtonsInfo;
  (window as any).testDragMode = testDragMode;
}
