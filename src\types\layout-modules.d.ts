// Type definitions for layout modules
declare module '*.js' {
  import { Layout } from './layout';
  const content: { default: Layout };
  export default content;
}

declare module '../New/*-layout.js' {
  import { Layout } from './layout';
  const content: { default: Layout };
  export default content;
}

declare module '../New/*-layout' {
  import { Layout } from './layout';
  const content: { default: Layout };
  export default content;
}
