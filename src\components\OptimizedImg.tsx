import React from 'react';
import { ImageConfig, getOptimizedImageProps } from '../utils/imageOptimizer';

interface OptimizedImgProps {
  config: ImageConfig;
  className?: string;
  style?: React.CSSProperties;
  onClick?: () => void;
}

const OptimizedImg: React.FC<OptimizedImgProps> = ({ 
  config, 
  className, 
  style, 
  onClick 
}) => {
  const imageProps = getOptimizedImageProps(config);
  
  return (
    <img
      {...imageProps}
      className={className}
      style={style}
      onClick={onClick}
    />
  );
};

export default OptimizedImg;
