# 🎛️ دليل أداة التحكم المتقدمة في الأزرار

## نظرة عامة

أداة التحكم المتقدمة هي الحل الشامل لضبط مواقع جميع أزرار اللعبة بدقة عالية مع إمكانية الحفظ والتحميل الفوري.

## 🚀 المميزات الرئيسية

### ✨ التحكم الدقيق
- **سحب وإفلات مباشر** للأزرار على الشاشة الحقيقية
- **شبكة مرجعية قابلة للتخصيص** (1%, 2.5%, 5%, 10%)
- **دقة حركة متغيرة** (0.1%, 0.5%, 1%)
- **محاذاة تلقائية للشبكة** أو حركة حرة
- **أزرار اتجاهية** للتحكم الدقيق بالبكسل

### 💾 إدارة التكوينات
- **حفظ تلقائي** مع نسخة احتياطية
- **تحميل فوري** للتكوينات المحفوظة
- **استرداد النسخ الاحتياطية**
- **تصدير واستيراد** جميع التكوينات
- **إحصائيات مفصلة** لكل تكوين

### 🎯 واجهة متقدمة
- **قائمة شاملة** لجميع الأزرار
- **تحديد وتحرير مباشر**
- **معاينة فورية** للتغييرات
- **ألوان مميزة** لكل نوع زر
- **معلومات تفصيلية** لكل عنصر

## 🛠️ كيفية الاستخدام

### 1. فتح الأداة
- ابحث عن زر **"🎛️ Advanced Controller"** في الجانب الأيمن
- اضغط عليه لفتح الأداة المتقدمة

### 2. فهم الواجهة

#### الألوان المرجعية:
- 🟢 **أخضر**: أزرار الفواكه (BAR, WATERMELON, LEMON, BANANA, APPLE)
- 🟡 **أصفر**: أزرار المبالغ (5k, 3k, 1k, 500, 100)
- 🔴 **أحمر**: مربعات الرهانات (عرض أرقام الرهان)
- 🔵 **أزرق**: عروض المعلومات (الرصيد، إجمالي الرهان)

#### الشبكة المرجعية:
- **خطوط بيضاء رفيعة**: الشبكة الأساسية
- **خطوط بيضاء سميكة**: الخطوط الرئيسية (كل خطين)

### 3. ضبط المواقع

#### الطريقة الأولى: السحب والإفلات
1. **اضغط على أي زر** واسحبه إلى الموقع المطلوب
2. **الزر المحدد** سيظهر بإطار أبيض
3. **اتركه في المكان المطلوب**

#### الطريقة الثانية: الأزرار الاتجاهية
1. **اضغط على زر** لتحديده
2. **استخدم الأزرار الاتجاهية** في لوحة التحكم:
   - ↑ للأعلى
   - ↓ للأسفل  
   - ← لليسار
   - → لليمين

#### الطريقة الثالثة: القائمة
1. **اضغط على اسم الزر** في قائمة الأزرار
2. **سيتم تحديده تلقائياً**
3. **استخدم الأزرار الاتجاهية** للتحكم الدقيق

### 4. إعدادات الدقة

#### الشبكة المرجعية:
- **1%**: دقة عالية جداً (100 نقطة)
- **2.5%**: دقة عالية (40 نقطة)
- **5%**: دقة متوسطة (20 نقطة) - **مُوصى به**
- **10%**: دقة منخفضة (10 نقاط)

#### دقة الحركة:
- **0.1%**: حركة دقيقة جداً
- **0.5%**: حركة دقيقة
- **1%**: حركة عادية - **مُوصى به**

### 5. حفظ التكوين

#### الحفظ الأساسي:
1. **اضغط على "💾 حفظ التكوين"**
2. **سيتم الحفظ تلقائياً** مع نسخة احتياطية
3. **سيتم نسخ الكود** للحافظة
4. **الصق الكود** في ملف `buttonPositions.ts`

#### معلومات الحفظ:
- ✅ **حفظ محلي** في المتصفح
- 🔙 **نسخة احتياطية** تلقائية
- 📋 **كود TypeScript** جاهز للاستخدام
- 📊 **إحصائيات مفصلة**

### 6. تحميل التكوينات

#### تحميل محفوظ:
- **"📂 تحميل محفوظ"**: تحميل آخر تكوين محفوظ

#### استرداد نسخة:
- **"🔙 استرداد نسخة"**: العودة للنسخة الاحتياطية

#### إعادة تعيين:
- **"🔄 إعادة تعيين"**: العودة للقيم الافتراضية

#### تصدير الكل:
- **"📤 تصدير الكل"**: تصدير جميع التكوينات المحفوظة

## 🎯 نصائح للاستخدام الأمثل

### للمبتدئين:
1. **ابدأ بالشبكة 5%** والدقة 1%
2. **فعّل المحاذاة للشبكة**
3. **استخدم السحب والإفلات** للتحريك السريع
4. **احفظ بانتظام** لتجنب فقدان التغييرات

### للمتقدمين:
1. **استخدم الشبكة 2.5%** والدقة 0.5%
2. **استخدم الأزرار الاتجاهية** للدقة العالية
3. **قارن التكوينات** قبل الحفظ
4. **صدّر التكوينات** كنسخة احتياطية

### للمحترفين:
1. **الشبكة 1%** والدقة 0.1%
2. **التحكم بالكيبورد** (قريباً)
3. **تكوينات متعددة** لأجهزة مختلفة
4. **أتمتة التطبيق** (قريباً)

## 🔧 استكشاف الأخطاء

### المشكلة: الأزرار لا تتحرك
**الحل**: 
- تأكد من تحديد الزر أولاً
- تحقق من إعدادات الدقة
- جرب إعادة تحميل الصفحة

### المشكلة: الحفظ لا يعمل
**الحل**:
- تحقق من إعدادات المتصفح
- امسح الكاش والبيانات المحفوظة
- جرب متصفح آخر

### المشكلة: التكوين لا يطبق
**الحل**:
- تأكد من نسخ الكود كاملاً
- تحقق من صيغة JSON
- راجع مسار الملف

## 📊 الإحصائيات والتقارير

عند الحفظ، ستحصل على:
- **عدد أزرار الفواكه**: 5 أزرار
- **عدد أزرار المبالغ**: 5 أزرار  
- **عدد مربعات الرهانات**: 5 مربعات
- **عدد عروض المعلومات**: 2 عرض
- **المجموع**: 17 عنصر

## 🚀 المميزات القادمة

- **التحكم بالكيبورد**: أسهم الاتجاه + مفاتيح التحكم
- **التراجع والإعادة**: Ctrl+Z / Ctrl+Y
- **القوالب الجاهزة**: تخطيطات مُعدة مسبقاً
- **المعاينة المباشرة**: رؤية التغييرات فوراً
- **التصدير المتقدم**: ملفات JSON و CSS
- **المزامنة السحابية**: حفظ عبر الأجهزة

## 📝 مثال عملي

```typescript
// مثال على التكوين المُصدَّر
{
  device: {
    name: "iPhone 12/13/14",
    width: 390,
    height: 844
  },
  symbolButtons: {
    "bar": {
      "left": "11.0%",
      "top": "65.0%",
      "name": "BAR"
    },
    // ... باقي الأزرار
  }
}
```

---

**💡 نصيحة**: احفظ تكوينك بانتظام واستخدم النسخ الاحتياطية لتجنب فقدان عملك!
