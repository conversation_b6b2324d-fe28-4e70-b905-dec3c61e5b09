// ملف مركزي لإدارة أماكن جميع الأزرار والمستطيلات لكل جهاز
// يمكنك إضافة أو تعديل أماكن الأزرار هنا فقط

import React from 'react';

export interface ButtonPosition {
  left: string;
  top: string;
  name?: string;
  symbol?: string;
  info?: string;
  value?: number;
}

export interface GameBoardSettings {
  gap: string; // التباعد بين المربعات
  padding: string; // الحشو حول الشبكة
  squareSize: {
    minWidth: string;
    minHeight: string;
  };
  gridAdjustments?: {
    firstRowMarginTop?: string;
    secondRowMarginTop?: string;
    firstColumnMarginLeft?: string;
  };
}

export interface DeviceButtonPositions {
  device: {
    name: string;
    width: number;
    height: number;
  };
  backgroundImage: string;
  symbolButtons: { [key: string]: ButtonPosition };
  amountButtons: { [key: string]: ButtonPosition };
  betRectangles: { [key: string]: ButtonPosition };
  betNumbers: { [key: string]: ButtonPosition };
  topDisplays: { [key: string]: ButtonPosition };
  gameBoard?: React.CSSProperties;
  gameBoardSettings?: GameBoardSettings; // إعدادات مربعات اللعبة
  actionButtons?: { [key: string]: ButtonPosition };
  historyBar?: React.CSSProperties;
}

export const BUTTON_POSITIONS: DeviceButtonPositions[] = [
  {
    device: {
      name: 'iPhone 14 Pro Max Real',
      width: 430, // الحجم الفعلي لجهازك
      height: 932, // الحجم الفعلي لجهازك
    },
    backgroundImage: '/images/bg-428x926.webp',

    // إعدادات مربع اللعبة - عرض أصغر وطول أكبر
    gameBoard: {
      left: '50%',
      top: '22%', // حركته للأسفل قليلاً إلى 22%
      width: 'min(340px, 85vw)', // عرض أصغر درجتين: 340px
      height: 'min(300px, 60vh)', // ارتفاع أصغر درجتين: 300px
      transform: 'translateX(-50%)',
      position: 'absolute',
      zIndex: 10,
    },

    // إعدادات مربعات اللعبة المخصصة لـ iPhone 14 Pro Max
    gameBoardSettings: {
      gap: '8px', // تباعد متوسط
      padding: '8px',
      squareSize: {
        minWidth: '70px', // تكبير من 55px إلى 70px
        minHeight: '70px', // تكبير من 55px إلى 70px
      },
      gridAdjustments: {
        firstRowMarginTop: '6px',
        secondRowMarginTop: '4px',
        firstColumnMarginLeft: '-15px', // تحريك أكثر لليسار (زيادة للوضوح)
      },
    },

    historyBar: {
      width: '50%',
      height: '33px',
      top: 'calc(75% + 55px)',
      left: '50%',
      transform: 'translateX(-50%)',
      position: 'absolute',
      zIndex: 20000,
    },

    symbolButtons: {
      "bar": { "left": "12%", "top": "65%", "name": "BAR" }, // حرك لليمين مقدار شعرة
      "watermelon": { "left": "29%", "top": "65%", "name": "WATERMELON" }, // حرك لليمين مقدار شعرة
      "lemon": { "left": "50%", "top": "65%", "name": "LEMON" }, // ثابت في الوسط
      "banana": { "left": "71%", "top": "65%", "name": "BANANA" }, // حرك لليسار مقدار شعرة
      "apple": { "left": "88%", "top": "65%", "name": "APPLE" } // حرك لليسار مقدار شعرة
    },

    amountButtons: {
      "amount1": { "left": "7%", "top": "75%", "name": "AMOUNT_1", "value": 5000 }, // يسار من 10% إلى 7%
      "amount2": { "left": "19%", "top": "75%", "name": "AMOUNT_2", "value": 3000 }, // يسار من 22% إلى 19%
      "amount3": { "left": "31%", "top": "75%", "name": "AMOUNT_3", "value": 1000 }, // يسار من 34% إلى 31%
      "amount4": { "left": "43%", "top": "75%", "name": "AMOUNT_4", "value": 500 }, // يسار من 46% إلى 43%
      "amount5": { "left": "55%", "top": "75%", "name": "AMOUNT_5", "value": 100 } // يسار من 58% إلى 55%
    },

    actionButtons: {
      spin: { left: '65%', top: '75%' }
    },

    betNumbers: {},
    topDisplays: {
      "balanceDisplay": { "left": "31%", "top": "17%", "name": "BALANCE" }, // أسفل شعرة من 16.5% إلى 17%
      "totalBetDisplay": { "left": "89%", "top": "17%", "name": "TOTAL_BET" } // أسفل شعرة من 16.5% إلى 17%
    },
    betRectangles: {
      "bar": { "left": "12%", "top": "71%", "symbol": "BAR" }, // حرك لليمين قليلاً
      "watermelon": { "left": "28%", "top": "71%", "symbol": "WATERMELON" },
      "lemon": { "left": "50%", "top": "71%", "symbol": "LEMON" },
      "banana": { "left": "67%", "top": "71%", "symbol": "BANANA" }, // حرك لليسار قليلاً
      "apple": { "left": "90%", "top": "71%", "symbol": "APPLE" }
    }
  },
  {
    device: {
      name: 'iPhone 14 Pro Max Old',
      width: 428,
      height: 926,
    },
    backgroundImage: '/images/bg-428x926.webp',
    
    // إعدادات مربع اللعبة - محسنة
    gameBoard: {
      left: '50%',
      top: '22%',
      width: 'min(340px, 85vw)',
      height: 'min(300px, 60vh)',
      transform: 'translateX(-50%)',
      position: 'absolute',
      zIndex: 10,
    },

    historyBar: {
      width: '50%',
      height: '33px',
      top: 'calc(75% + 55px)',
      left: '50%',
      transform: 'translateX(-50%)',
      position: 'absolute',
      zIndex: 20000,
    },

    symbolButtons: {
      "bar": { "left": "12%", "top": "65%", "name": "BAR" }, // حرك لليمين مقدار شعرة
      "watermelon": { "left": "29%", "top": "65%", "name": "WATERMELON" }, // حرك لليمين مقدار شعرة
      "lemon": { "left": "50%", "top": "65%", "name": "LEMON" }, // ثابت في الوسط
      "banana": { "left": "71%", "top": "65%", "name": "BANANA" }, // حرك لليسار مقدار شعرة
      "apple": { "left": "88%", "top": "65%", "name": "APPLE" } // حرك لليسار مقدار شعرة
    },

    amountButtons: {
      "amount1": { "left": "7%", "top": "75%", "name": "AMOUNT_1", "value": 5000 },
      "amount2": { "left": "19%", "top": "75%", "name": "AMOUNT_2", "value": 3000 },
      "amount3": { "left": "31%", "top": "75%", "name": "AMOUNT_3", "value": 1000 },
      "amount4": { "left": "43%", "top": "75%", "name": "AMOUNT_4", "value": 500 },
      "amount5": { "left": "55%", "top": "75%", "name": "AMOUNT_5", "value": 100 }
    },

    actionButtons: {
      spin: { left: '65%', top: '75%' }
    },

    betRectangles: {
      "bar": { "left": "12%", "top": "71%", "symbol": "BAR" }, // حرك لليمين قليلاً
      "watermelon": { "left": "39%", "top": "71%", "symbol": "WATERMELON" },
      "lemon": { "left": "50%", "top": "71%", "symbol": "LEMON" },
      "banana": { "left": "67%", "top": "71%", "symbol": "BANANA" }, // حرك لليسار قليلاً
      "apple": { "left": "90%", "top": "71%", "symbol": "APPLE" }
    },

    betNumbers: {},
    topDisplays: {
      "balanceDisplay": { "left": "31%", "top": "17%", "name": "BALANCE" },
      "totalBetDisplay": { "left": "89%", "top": "17%", "name": "TOTAL_BET" }
    },
  },
  // تكوين iPhone 12/13/14 (390x844) - مضبوط للخلفية الفعلية
  {
    device: {
      name: 'iPhone 12/13/14',
      width: 390,
      height: 844,
    },
    backgroundImage: '/images/bg-390x844.webp',
    
    // إعدادات مربع اللعبة - محسنة
    gameBoard: {
      left: '50%',
      top: '48%', // تم تحريكها للأسفل أكثر بناءً على طلب المستخدم
      width: 'min(340px, 85vw)',
      height: 'min(300px, 60vh)',
      transform: 'translateX(-50%)',
      position: 'absolute',
      zIndex: 10,
    },

    // إعدادات مربعات اللعبة المخصصة لـ iPhone 12/13/14
    gameBoardSettings: {
      gap: '6px', // تباعد أقل للشاشة الأصغر
      padding: '6px',
      squareSize: {
        minWidth: '70px', // توحيد الحجم مع باقي الأجهزة
        minHeight: '70px', // توحيد الحجم مع باقي الأجهزة
      },
      gridAdjustments: {
        firstRowMarginTop: '5px',
        secondRowMarginTop: '3px',
        firstColumnMarginLeft: '-5px', // تحريك أكثر لليسار
      },
    },

    symbolButtons: {
      "bar": { "left": "12%", "top": "65%", "name": "BAR" }, // حرك لليمين مقدار شعرة
      "watermelon": { "left": "29%", "top": "65%", "name": "WATERMELON" }, // حرك لليمين مقدار شعرة
      "lemon": { "left": "50%", "top": "65%", "name": "LEMON" }, // ثابت في الوسط
      "banana": { "left": "71%", "top": "65%", "name": "BANANA" }, // حرك لليسار مقدار شعرة
      "apple": { "left": "88%", "top": "65%", "name": "APPLE" } // حرك لليسار مقدار شعرة
    },

    amountButtons: {
      "amount1": { "left": "7%", "top": "75%", "name": "AMOUNT_1", "value": 5000 },
      "amount2": { "left": "19%", "top": "75%", "name": "AMOUNT_2", "value": 3000 },
      "amount3": { "left": "31%", "top": "75%", "name": "AMOUNT_3", "value": 1000 },
      "amount4": { "left": "43%", "top": "75%", "name": "AMOUNT_4", "value": 500 },
      "amount5": { "left": "55%", "top": "75%", "name": "AMOUNT_5", "value": 100 }
    },

    actionButtons: {
      spin: { left: '65%', top: '75%' }
    },

    betRectangles: {
      "bar": { "left": "12%", "top": "71%", "symbol": "BAR" }, // حرك لليمين قليلاً
      "watermelon": { "left": "28%", "top": "71%", "symbol": "WATERMELON" },
      "lemon": { "left": "50%", "top": "71%", "symbol": "LEMON" },
      "banana": { "left": "70%", "top": "71%", "symbol": "BANANA" }, // حرك لليسار قليلاً
      "apple": { "left": "90%", "top": "71%", "symbol": "APPLE" }
    },

    betNumbers: {},
    topDisplays: {
      "balanceDisplay": { "left": "31%", "top": "17%", "name": "BALANCE" },
      "totalBetDisplay": { "left": "89%", "top": "17%", "name": "TOTAL_BET" }
    },
  },
  // إضافة تكوين للشاشات الأكبر (Desktop/Tablet)
  {
    device: {
      name: 'Desktop',
      width: 1920,
      height: 1080,
    },
    backgroundImage: '/images/bg-1920x1080.webp',
    
    // إعدادات مربع اللعبة - محسنة للديسكتوب
    gameBoard: {
      left: '50%',
      top: '22%',
      width: 'min(600px, 65vw)',
      height: 'min(450px, 55vh)',
      transform: 'translateX(-50%)',
      position: 'absolute',
      zIndex: 10,
    },

    // إعدادات مربعات اللعبة المخصصة للديسكتوب
    gameBoardSettings: {
      gap: '12px', // تباعد أكبر للشاشة الكبيرة
      padding: '12px',
      squareSize: {
        minWidth: '70px', // توحيد الحجم مع باقي الأجهزة
        minHeight: '70px', // توحيد الحجم مع باقي الأجهزة
      },
      gridAdjustments: {
        firstRowMarginTop: '10px',
        secondRowMarginTop: '8px',
        firstColumnMarginLeft: '-7px', // تحريك أكثر لليسار
      },
    },

    symbolButtons: {
      "bar": { "left": "12%", "top": "65%", "name": "BAR" }, // حرك لليمين مقدار شعرة
      "watermelon": { "left": "30%", "top": "65%", "name": "WATERMELON" }, // حرك لليمين مقدار شعرة
      "lemon": { "left": "50%", "top": "65%", "name": "LEMON" }, // ثابت في الوسط
      "banana": { "left": "70%", "top": "65%", "name": "BANANA" }, // حرك لليسار مقدار شعرة
      "apple": { "left": "88%", "top": "65%", "name": "APPLE" } // حرك لليسار مقدار شعرة
    },
    amountButtons: {
      "amount1": { "left": "7%", "top": "75%", "name": "AMOUNT_1", "value": 5000 },
      "amount2": { "left": "19%", "top": "75%", "name": "AMOUNT_2", "value": 3000 },
      "amount3": { "left": "31%", "top": "75%", "name": "AMOUNT_3", "value": 1000 },
      "amount4": { "left": "43%", "top": "75%", "name": "AMOUNT_4", "value": 500 },
      "amount5": { "left": "55%", "top": "75%", "name": "AMOUNT_5", "value": 100 }
    },

    actionButtons: {
      spin: { left: '65%', top: '75%' }
    },

    betRectangles: {
      "bar": { "left": "12%", "top": "71%", "symbol": "BAR" }, // حرك لليمين قليلاً
      "watermelon": { "left": "28%", "top": "71%", "symbol": "WATERMELON" },
      "lemon": { "left": "50%", "top": "71%", "symbol": "LEMON" },
      "banana": { "left": "71%", "top": "71%", "symbol": "BANANA" }, // حرك لليسار قليلاً
      "apple": { "left": "90%", "top": "71%", "symbol": "APPLE" }
    },

    betNumbers: {},
    topDisplays: {
      "balanceDisplay": { "left": "31%", "top": "17%", "name": "BALANCE" },
      "totalBetDisplay": { "left": "89%", "top": "17%", "name": "TOTAL_BET" }
    },
  },
  // تكوين Samsung Galaxy S8 (412x915)
  {
    device: {
      name: 'Samsung Galaxy S8',
      width: 412,
      height: 915,
    },
    backgroundImage: '/images/bg-412x915.webp',
    
    // إعدادات مربع اللعبة - محسنة لـ Galaxy
    gameBoard: {
      left: '50%',
      top: '12%', // تم رفعها للأعلى
      width: 'min(600px, 85vw)', // عرض مربع
      height: 'min(600px, 60vh)', // طول مساوي للعرض (مربع كامل)
      transform: 'translateX(-50%)',
      position: 'absolute',
      zIndex: 10,
    },

    // إعدادات مربعات اللعبة المخصصة لـ Samsung Galaxy S8
    gameBoardSettings: {
      gap: '10px', // تباعد كبير للشاشة الطويلة
      padding: '10px',
      squareSize: {
        minWidth: '70px', // موحد مع باقي الأجهزة
        minHeight: '70px', // موحد مع باقي الأجهزة
      },
      gridAdjustments: {
        firstRowMarginTop: '8px',
        secondRowMarginTop: '6px',
        firstColumnMarginLeft: '-6px', // تحريك أكثر لليسار
      },
    },

    historyBar: {
      width: '60%',
      height: '33px',
      top: 'calc(75% + 55px)',
      left: '50%',
      transform: 'translateX(-50%)',
      position: 'absolute',
      zIndex: 20000,
    },

    symbolButtons: {
      "bar": { "left": "12.8882%", "top": "59.8958%", "name": "BAR" }, // حرك لليمين مقدار شعرة
      "watermelon": { "left": "32.3427%", "top": "60.099%", "name": "WATERMELON" }, // حرك لليمين مقدار شعرة
      "lemon": { "left": "50.3607%", "top": "59.9983%", "name": "LEMON" }, // ثابت في الوسط
      "banana": { "left": "68.375%", "top": "60.0972%", "name": "BANANA" }, // حرك لليسار مقدار شعرة
      "apple": { "left": "87.4687%", "top": "60.0625%", "name": "APPLE" } // حرك لليسار مقدار شعرة
    },

    amountButtons: {
      "amount1": { "left": "8.50588%", "top": "74.6667%", "name": "AMOUNT_1", "value": 5000 },
      "amount2": { "left": "20.3933%", "top": "74.6667%", "name": "AMOUNT_2", "value": 3000 },
      "amount3": { "left": "31.9992%", "top": "74.8333%", "name": "AMOUNT_3", "value": 1000 },
      "amount4": { "left": "43.8095%", "top": "75.1667%", "name": "AMOUNT_4", "value": 500 },
      "amount5": { "left": "55.134%", "top": "74.8333%", "name": "AMOUNT_5", "value": 100 }
    },

    actionButtons: {
      spin: { left: '70%', top: '80%' }
    },

    betRectangles: {
      "bar": { "left": "11.11123%", "top": "70.1667%", "symbol": "BAR" }, // حرك لليمين قليلاً
      "watermelon": { "left": "31.8103%", "top": "70.1667%", "symbol": "WATERMELON" },
      "lemon": { "left": "51.3399%", "top": "70.3316%", "symbol": "LEMON" },
      "banana": { "left": "67.8438%", "top": "70.1649%", "symbol": "BANANA" }, // حرك لليسار قليلاً
      "apple": { "left": "90.5764%", "top": "70.3333%", "symbol": "APPLE" }
    },

    betNumbers: {
      "total_bets": { "left": "90.3836%", "top": "12%", "info": "TOTAL_BETS" },
      "current_bet": { "left": "80.5784%", "top": "12%", "info": "CURRENT_BET" },
      "last_win": { "left": "80.8907%", "top": "79.8333%", "info": "LAST_WIN" }
    },

    topDisplays: {
      "balanceDisplay": { "left": "31.0893%", "top": "12%", "name": "BALANCE" },
      "totalBetDisplay": { "left": "89%", "top": "12%", "name": "TOTAL_BET" }
    },
  },
  // تكوين Samsung Galaxy (360x740)
  {
    device: {
      name: 'Samsung Galaxy (360x740)',
      width: 360,
      height: 740,
    },
    backgroundImage: '/images/bg-360x740.webp',
    
    // إعدادات مربع اللعبة - محسنة لـ 360x740
    gameBoard: {
      left: '50%',
      top: '4%', // رفع الحاوية للأعلى أكثر
      width: 'min(320px, 85vw)', // عرض أقل للأعمدة الأفقية (0-4)
      height: 'min(1300px, 60vh)', // ارتفاع أكبر بكثير للأعمدة العمودية (0-20 و 4-24)
      transform: 'translateX(-50%)',
      position: 'absolute',
      zIndex: 10,
    },

                    symbolButtons: {
            "bar": { "left": "10%", "top": "69%", "name": "BAR" }, // حرك لليمين مقدار شعرة
            "watermelon": { "left": "28%", "top": "69%", "name": "WATERMELON" }, // حرك لليمين مقدار شعرة
            "lemon": { "left": "50%", "top": "69%", "name": "LEMON" }, // ثابت في الوسط
            "banana": { "left": "72%", "top": "69%", "name": "BANANA" }, // حرك لليسار مقدار شعرة
            "apple": { "left": "90%", "top": "69%", "name": "APPLE" } // حرك لليسار مقدار شعرة
        },

    amountButtons: {
      "amount1": { "left": "7%", "top": "87.2%", "name": "AMOUNT_1", "value": 5000 },
      "amount2": { "left": "19%", "top": "87.2%", "name": "AMOUNT_2", "value": 3000 },
      "amount3": { "left": "31%", "top": "87.2%", "name": "AMOUNT_3", "value": 1000 },
      "amount4": { "left": "43%", "top": "87.2%", "name": "AMOUNT_4", "value": 500 },
      "amount5": { "left": "55%", "top": "87.2%", "name": "AMOUNT_5", "value": 100 }
    },

    actionButtons: {
      spin: { left: '65%', top: '75%' }
    },

    betRectangles: {
      "bar": { "left": "10%", "top": "81%", "symbol": "BAR" }, // حرك لليمين قليلاً
      "watermelon": { "left": "26%", "top": "81%", "symbol": "WATERMELON" },
      "lemon": { "left": "50%", "top": "81%", "symbol": "LEMON" },
      "banana": { "left": "72%", "top": "81%", "symbol": "BANANA" }, // حرك لليسار قليلاً
      "apple": { "left": "92%", "top": "81%", "symbol": "APPLE" }
    },

    betNumbers: {
      "total_bets": { "left": "88%", "top": "2%", "info": "TOTAL_BETS" },
      "current_bet": { "left": "85%", "top": "2%", "info": "CURRENT_BET" },
      "last_win": { "left": "78%", "top": "82%", "info": "LAST_WIN" }
    },

    topDisplays: {
      "balanceDisplay": { "left": "32%", "top": "2%", "name": "BALANCE" },
      "totalBetDisplay": { "left": "87%", "top": "2%", "name": "TOTAL_BET" }
    },
  },
];

// دالة جديدة لتطبيق المواقع مباشرة على الأزرار
export function getButtonStyle(deviceName: string, buttonType: 'symbol' | 'amount' | 'betRectangle' | 'betNumber', buttonName: string) {
  const device = BUTTON_POSITIONS.find(d => d.device.name === deviceName);
  if (!device) return {};

  switch (buttonType) {
    case 'symbol':
      const symbolPos = device.symbolButtons[buttonName.toLowerCase()];
      return symbolPos ? {
        left: symbolPos.left,
        top: symbolPos.top
      } : {};
    
    case 'amount':
      const amountPos = device.amountButtons[buttonName];
      return amountPos ? {
        left: amountPos.left,
        top: amountPos.top
      } : {};
    
    case 'betRectangle':
      const rectPos = device.betRectangles[buttonName];
      return rectPos ? {
        left: rectPos.left,
        top: rectPos.top
      } : {};
    
    case 'betNumber':
      const numPos = device.betNumbers[buttonName];
      return numPos ? {
        left: numPos.left,
        top: numPos.top
      } : {};
    
    default:
      return {};
  }
}

// دالة للحصول على تكوين الجهاز الحالي
export function getCurrentDeviceConfig() {
  if (typeof window === 'undefined') return BUTTON_POSITIONS[0];
  
  const width = window.innerWidth;
  const height = window.innerHeight;
  
  return BUTTON_POSITIONS.find(d => {
    const widthMatch = Math.abs(d.device.width - width) < 50;
    const heightMatch = Math.abs(d.device.height - height) < 50;
    return widthMatch && heightMatch;
  }) || BUTTON_POSITIONS[0];
}
