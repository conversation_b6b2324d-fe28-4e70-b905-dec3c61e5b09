import { SMOOTH_LIGHT_PATH, LIGHT_CYCLES, SPEED_SETTINGS, LUCKY_SETTINGS } from './gameConstants';

// نظام تسلسل الإضاءة الجديد - دوران منتظم وسلس
export const generateLightSequence = (targetPosition: number, startPosition?: number): number[] => {
  console.log(`💡 إنشاء تسلسل الإضاءة للوصول إلى الموقع ${targetPosition}`);
  
  const sequence: number[] = [];
  
  // تحديد نقطة البداية
  let currentPosition = startPosition || SMOOTH_LIGHT_PATH[0];
  if (startPosition !== undefined) {
    // البحث عن أقرب موقع في المسار لنقطة البداية
    const startIndex = SMOOTH_LIGHT_PATH.indexOf(startPosition);
    if (startIndex !== -1) {
      currentPosition = startPosition;
      console.log(`🚀 بدء من الموقع: ${currentPosition}`);
    }
  }
  
  // إضافة الدورات الكاملة بشكل متسلسل ومنتظم
  for (let cycle = 0; cycle < LIGHT_CYCLES; cycle++) {
    console.log(`🔄 الدورة ${cycle + 1}/${LIGHT_CYCLES}`);
    
    // إضافة المسار الكامل
    for (const position of SMOOTH_LIGHT_PATH) {
      sequence.push(position);
    }
  }
  
  // إضافة الحركة النهائية للوصول للموقع المستهدف
  const targetIndex = SMOOTH_LIGHT_PATH.indexOf(targetPosition);
  if (targetIndex !== -1) {
    // إضافة المواقع من بداية المسار حتى الموقع المستهدف
    for (let i = 0; i <= targetIndex; i++) {
      sequence.push(SMOOTH_LIGHT_PATH[i]);
    }
  } else {
    console.warn(`⚠️ الموقع المستهدف ${targetPosition} غير موجود في المسار`);
    // إضافة دورة إضافية والتوقف عند أول موقع
    for (const position of SMOOTH_LIGHT_PATH) {
      sequence.push(position);
    }
  }
  
  console.log(`✅ تم إنشاء تسلسل بطول ${sequence.length} موقع`);
  console.log(`🎯 الموقع النهائي: ${sequence[sequence.length - 1]}`);
  
  return sequence;
};

// حساب سرعة الإضاءة مع تسارع تدريجي
export const calculateLightSpeed = (currentStep: number, totalSteps: number): number => {
  const progress = currentStep / totalSteps;
  
  // سرعة بطيئة في البداية، ثم تسارع، ثم تباطؤ في النهاية
  let speed: number;
  
  if (progress < 0.3) {
    // البداية: سرعة بطيئة
    speed = SPEED_SETTINGS.INITIAL_SPEED;
  } else if (progress < 0.8) {
    // الوسط: تسارع تدريجي
    const accelerationProgress = (progress - 0.3) / 0.5;
    speed = SPEED_SETTINGS.INITIAL_SPEED + 
           (accelerationProgress * (SPEED_SETTINGS.FINAL_SPEED - SPEED_SETTINGS.INITIAL_SPEED));
  } else {
    // النهاية: تباطؤ تدريجي
    const decelerationProgress = (progress - 0.8) / 0.2;
    speed = SPEED_SETTINGS.FINAL_SPEED + 
           (decelerationProgress * (SPEED_SETTINGS.MAX_SPEED - SPEED_SETTINGS.FINAL_SPEED));
  }
  
  return Math.max(SPEED_SETTINGS.MIN_SPEED, Math.min(SPEED_SETTINGS.MAX_SPEED, speed));
};

// تحديد الاختيار النهائي بناءً على موقع الضوء
export const determineFinalSelection = (lightPosition: number): number[] => {
  console.log(`🎯 تحديد الاختيار النهائي للموقع: ${lightPosition}`);
  
  // البحث عن المربع في المسار النشط
  const squareIndex = SMOOTH_LIGHT_PATH.indexOf(lightPosition);
  
  if (squareIndex !== -1) {
    console.log(`✅ تم العثور على المربع في الفهرس: ${squareIndex}`);
    return [squareIndex];
  } else {
    console.warn(`⚠️ لم يتم العثور على الموقع ${lightPosition} في المسار النشط`);
    return [0]; // العودة للموقع الأول كاحتياط
  }
};

// إنشاء حركة متعددة للاكي
export const generateLuckyAnimation = (luckyType: 'luckyDoubleText' | 'luckyTripleText') => {
  console.log(`🍀 إنشاء حركة اللاكي: ${luckyType}`);
  
  const sequences: number[][] = [];
  const finalPositions: number[] = [];
  
  // تحديد عدد الحركات بناءً على نوع اللاكي
  const numSequences = luckyType === 'luckyDoubleText' ? 2 : 3;
  
  for (let i = 0; i < numSequences; i++) {
    // اختيار موقع عشوائي من المسار
    const randomIndex = Math.floor(Math.random() * SMOOTH_LIGHT_PATH.length);
    const targetPosition = SMOOTH_LIGHT_PATH[randomIndex];
    
    // إنشاء تسلسل قصير للوصول لهذا الموقع
    const sequence = generateShortSequence(targetPosition);
    sequences.push(sequence);
    finalPositions.push(targetPosition);
    
    console.log(`🎯 الحركة ${i + 1}: الهدف ${targetPosition}`);
  }
  
  return { sequences, finalPositions };
};

// إنشاء تسلسل قصير للحركات السريعة
function generateShortSequence(targetPosition: number): number[] {
  const sequence: number[] = [];
  const targetIndex = SMOOTH_LIGHT_PATH.indexOf(targetPosition);
  
  if (targetIndex !== -1) {
    // إضافة نصف دورة للوصول للموقع
    const halfPath = SMOOTH_LIGHT_PATH.slice(0, Math.ceil(SMOOTH_LIGHT_PATH.length / 2));
    sequence.push(...halfPath);
    
    // إضافة الموقع المستهدف
    if (!sequence.includes(targetPosition)) {
      sequence.push(targetPosition);
    }
  }
  
  return sequence;
}
