// إعدادات توزيع الأزرار والخانات لكل خلفية/مقاس
export interface BackgroundLayoutConfig {
  name: string;
  width: number;
  height: number;
  backgroundImage: string;
  symbolButtons: {
    [key: string]: { left: string; top: string; name: string };
  };
  amountButtons: {
    [key: string]: { left: string; top: string; name: string; value: number };
  };
  betRectangles: {
    [key: string]: { left: string; top: string; symbol: string };
  };
  // أضف أي إعدادات أخرى تحتاجها
}

export const BACKGROUND_LAYOUTS: BackgroundLayoutConfig[] = [
  {
    name: 'iPhone SE',
    width: 375,
    height: 667,
    backgroundImage: '/images/bg-375x667.webp',
    symbolButtons: {
      bar: { left: '5%', top: '75%', name: 'BAR' },
      watermelon: { left: '22%', top: '73%', name: 'WATERMELON' },
      lemon: { left: '50%', top: '73%', name: 'LEMON' },
      banana: { left: '78%', top: '73%', name: '<PERSON><PERSON><PERSON>' },
      apple: { left: '95%', top: '75%', name: 'APPLE' },
    },
    amountButtons: {},
    betRectangles: {
      rect0: { left: '10%', top: '85%', symbol: 'BAR' }, // تحت الرصيد إلى اليسار قليلاً
      rect25: { left: '95%', top: '70%', symbol: 'APPLE' }, // فوق زر المراهنة الخاص بالتفاح
    },
  },
  // ... باقي الأجهزة ...
];