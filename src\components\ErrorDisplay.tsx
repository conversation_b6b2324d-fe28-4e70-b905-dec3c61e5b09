import React, { useState, useEffect } from 'react';
import { errorHandler, GameError } from '../utils/errorHandler';

interface ErrorDisplayProps {
  isVisible?: boolean;
  maxErrors?: number;
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
}

const ErrorDisplay: React.FC<ErrorDisplayProps> = ({ 
  isVisible = false, 
  maxErrors = 5,
  position = 'bottom-left'
}) => {
  const [errors, setErrors] = useState<GameError[]>([]);
  const [isExpanded, setIsExpanded] = useState(false);

  useEffect(() => {
    if (!isVisible) return;

    // تحديث الأخطاء عند التحميل
    setErrors(errorHandler.getAllErrors().slice(0, maxErrors));

    // الاستماع للأخطاء الجديدة
    const handleNewError = (error: GameError) => {
      setErrors(prev => [error, ...prev.slice(0, maxErrors - 1)]);
    };

    errorHandler.addErrorListener(handleNewError);

    return () => {
      errorHandler.removeErrorListener(handleNewError);
    };
  }, [isVisible, maxErrors]);

  if (!isVisible || errors.length === 0) return null;

  const getPositionStyles = () => {
    const baseStyles = {
      position: 'fixed' as const,
      zIndex: 9998,
      maxWidth: '400px',
      maxHeight: isExpanded ? '60vh' : '200px',
      overflow: 'auto',
      background: 'rgba(20, 20, 20, 0.95)',
      border: '1px solid #ff4444',
      borderRadius: '6px',
      backdropFilter: 'blur(4px)',
      transition: 'all 0.3s ease'
    };

    switch (position) {
      case 'top-left':
        return { ...baseStyles, top: '10px', left: '10px' };
      case 'top-right':
        return { ...baseStyles, top: '10px', right: '10px' };
      case 'bottom-left':
        return { ...baseStyles, bottom: '10px', left: '10px' };
      case 'bottom-right':
        return { ...baseStyles, bottom: '10px', right: '10px' };
      default:
        return { ...baseStyles, bottom: '10px', left: '10px' };
    }
  };

  const getSeverityColor = (severity: GameError['severity']) => {
    switch (severity) {
      case 'LOW': return '#4CAF50';
      case 'MEDIUM': return '#FF9800';
      case 'HIGH': return '#F44336';
      case 'CRITICAL': return '#9C27B0';
      default: return '#757575';
    }
  };

  const getSeverityIcon = (severity: GameError['severity']) => {
    switch (severity) {
      case 'LOW': return '💡';
      case 'MEDIUM': return '⚠️';
      case 'HIGH': return '🚨';
      case 'CRITICAL': return '💥';
      default: return '❓';
    }
  };

  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString('ar-SA', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const stats = errorHandler.getErrorStats();

  return (
    <div style={getPositionStyles()}>
      {/* رأس العرض */}
      <div 
        style={{
          padding: '8px 12px',
          background: 'rgba(255, 68, 68, 0.2)',
          borderBottom: '1px solid #ff4444',
          cursor: 'pointer',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div style={{ color: '#ff4444', fontWeight: 'bold', fontSize: '12px' }}>
          🐛 أخطاء اللعبة ({stats.total})
        </div>
        <div style={{ color: '#ffffff', fontSize: '10px' }}>
          {isExpanded ? '▼' : '▶'}
        </div>
      </div>

      {/* إحصائيات سريعة */}
      {isExpanded && (
        <div style={{
          padding: '6px 12px',
          background: 'rgba(0, 0, 0, 0.3)',
          borderBottom: '1px solid #333',
          fontSize: '10px',
          color: '#cccccc'
        }}>
          <div>🚨 حرجة: {stats.bySeverity.CRITICAL || 0}</div>
          <div>⚠️ عالية: {stats.bySeverity.HIGH || 0}</div>
          <div>💡 متوسطة: {stats.bySeverity.MEDIUM || 0}</div>
        </div>
      )}

      {/* قائمة الأخطاء */}
      <div style={{ maxHeight: isExpanded ? '400px' : '150px', overflow: 'auto' }}>
        {errors.map((error, index) => (
          <div
            key={error.id}
            style={{
              padding: '8px 12px',
              borderBottom: index < errors.length - 1 ? '1px solid #333' : 'none',
              fontSize: '11px',
              lineHeight: '1.3'
            }}
          >
            {/* رأس الخطأ */}
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '4px'
            }}>
              <div style={{
                color: getSeverityColor(error.severity),
                fontWeight: 'bold'
              }}>
                {getSeverityIcon(error.severity)} {error.type}
              </div>
              <div style={{ color: '#888888', fontSize: '9px' }}>
                {formatTime(error.timestamp)}
              </div>
            </div>

            {/* رسالة الخطأ */}
            <div style={{
              color: '#ffffff',
              marginBottom: '4px',
              wordBreak: 'break-word'
            }}>
              {error.message}
            </div>

            {/* السياق */}
            {error.context && (
              <div style={{ color: '#aaaaaa', fontSize: '9px' }}>
                {error.context.gamePhase && `المرحلة: ${error.context.gamePhase} | `}
                {error.context.componentName && `المكون: ${error.context.componentName} | `}
                {error.context.userAction && `الإجراء: ${error.context.userAction}`}
              </div>
            )}

            {/* التفاصيل (في الوضع الموسع فقط) */}
            {isExpanded && error.details && (
              <details style={{ marginTop: '4px' }}>
                <summary style={{ 
                  color: '#888888', 
                  fontSize: '9px', 
                  cursor: 'pointer' 
                }}>
                  عرض التفاصيل
                </summary>
                <pre style={{
                  color: '#cccccc',
                  fontSize: '8px',
                  background: 'rgba(0, 0, 0, 0.5)',
                  padding: '4px',
                  borderRadius: '2px',
                  marginTop: '2px',
                  overflow: 'auto',
                  maxHeight: '100px'
                }}>
                  {typeof error.details === 'string' 
                    ? error.details 
                    : JSON.stringify(error.details, null, 2)
                  }
                </pre>
              </details>
            )}
          </div>
        ))}
      </div>

      {/* أزرار الإجراءات */}
      {isExpanded && (
        <div style={{
          padding: '6px 12px',
          background: 'rgba(0, 0, 0, 0.3)',
          borderTop: '1px solid #333',
          display: 'flex',
          gap: '8px'
        }}>
          <button
            onClick={() => {
              errorHandler.clearErrors();
              setErrors([]);
            }}
            style={{
              background: '#ff4444',
              color: 'white',
              border: 'none',
              padding: '4px 8px',
              borderRadius: '3px',
              fontSize: '9px',
              cursor: 'pointer'
            }}
          >
            مسح الكل
          </button>
          
          <button
            onClick={() => {
              console.table(errorHandler.getAllErrors());
        
            }}
            style={{
              background: '#2196F3',
              color: 'white',
              border: 'none',
              padding: '4px 8px',
              borderRadius: '3px',
              fontSize: '9px',
              cursor: 'pointer'
            }}
          >
            طباعة في وحدة التحكم
          </button>
        </div>
      )}
    </div>
  );
};

export default ErrorDisplay;
