export interface SquareConfig {
  gridIndex: number;
  symbol: string;
  type: 'normal' | 'halfFruit' | 'inner' | 'centerCountdownNumber' | 'luckyDoubleText' | 'luckyTripleText' | 'stackedBar' | 'infinityText';
  luckyNumber?: number;
}

export interface PayoutMultipliers {
  'half-fruit-win': number;
  '2-match': number;
  '4-match': number;
  '5-match': number;
  'APPLE': number;
  'LEMON': number;
  'BANANA': number;
  'WATERMELON': number;
  'BAR': number;
  'lucky-double-text': number;
  'lucky-triple-text': number;
}

export interface BetsOnTypes {
  'APPLE': number;
  'BANANA': number;
  'LEMON': number;
  'WATERMELON': number;
  'BAR': number;
}

export interface GameState {
  balance: number;
  betsOnTypes: BetsOnTypes;
  isSpinning: boolean;
  countdown: number;
  selectedSquares: number[];
  currentBetValueToApply: number;
  collectedSymbols: string[];
  messages: string[];
  totalWinAmount: number;
  // New 24/7 system states
  gamePhase: 'betting' | 'light_animation' | 'result_display';
  lightPosition: number;
  lightPath: number[];
  isLightAnimating: boolean;
  roundStartTime: number;
}
