import { Layout } from '../types/layout';

// Default layout that works for all devices
const defaultLayout: Layout = {
  device: {
    width: 1920,
    height: 1080,
    name: 'desktop',
    type: 'desktop',
    dpr: 1,
    scale: 1
  },
  backgroundImage: '/images/bg-1920x1080.webp',
  symbolButtons: {
    bar: { left: '10%', top: '70%' },
    watermelon: { left: '30%', top: '70%' },
    lemon: { left: '50%', top: '70%' },
    banana: { left: '70%', top: '70%' },
    apple: { left: '90%', top: '70%' }
  },
  betDisplays: [
    { left: '7%', bottom: '25%', top: 'auto' },    // BAR
    { left: '24%', bottom: '22.5%', top: 'auto' }, // WATERMELON
    { left: '45%', bottom: '22.5%', top: 'auto' }, // LEMON
    { left: '66%', bottom: '22.5%', top: 'auto' }, // BANANA
    { left: '84%', bottom: '22.5%', top: 'auto' }  // APPLE
  ]
};

// Get the default layout (simplified to always return the default layout)
export async function getLayoutForScreen(): Promise<Layout> {
  return defaultLayout;
}

// Get a specific layout by name (not currently used, but kept for compatibility)
// Using _name to indicate the parameter is intentionally unused
export async function getLayout(_name: string): Promise<Layout> {
  return defaultLayout;
}
