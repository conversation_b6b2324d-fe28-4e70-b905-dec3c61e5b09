# ✏️ دليل وضع التعديل البسيط

## نظرة عامة

وضع التعديل البسيط هو الحل الأمثل لضبط مواقع أزرار اللعبة بطريقة سهلة ومباشرة. يحول الأزرار الحقيقية في اللعبة إلى عناصر قابلة للسحب والإفلات.

## 🎯 المميزات الرئيسية

### ✨ البساطة والسهولة
- **تفعيل بضغطة واحدة** - لا تعقيدات
- **سحب وإفلات مباشر** للأزرار الحقيقية
- **لا نسب مئوية معقدة** - فقط اسحب للمكان المطلوب
- **واجهة بسيطة** بدون تعقيدات

### 🎮 التحكم الشامل
- **جميع أزرار الفواكه** (BAR, WATERMELON, LEMON, BANANA, APPLE)
- **أزرار المبالغ** (5k, 3k, 1k, 500, 100)
- **مربعات الرهانات** (عرض أرقام الرهان)
- **عروض المعلومات** (الرصيد، إجمالي الرهان)
- **🆕 مساحة اللعبة** (Game Board) - **جديد!**

### 💾 الحفظ الذكي
- **حفظ فوري** عند الإغلاق
- **نسخ تلقائي** للكود
- **حفظ محلي** كنسخة احتياطية
- **إلغاء التغييرات** إذا لزم الأمر

## 🚀 كيفية الاستخدام

### 1. فتح وضع التعديل
- ابحث عن زر **"✏️ وضع التعديل البسيط"** في الجانب الأيمن
- اضغط عليه لفتح لوحة التحكم

### 2. تفعيل الوضع
- اضغط على **"✏️ تفعيل التعديل"**
- ستظهر طبقة شفافة خضراء تشير لتفعيل الوضع
- جميع العناصر القابلة للتحرير ستظهر بإطارات ملونة

### 3. فهم الألوان
- 🟢 **إطار أخضر متقطع**: الأزرار العادية
- 🟡 **إطار ذهبي متقطع**: مساحة اللعبة
- 🔵 **خلفية شفافة خضراء**: وضع التعديل مفعل

### 4. تحريك العناصر
- **اسحب أي عنصر** إلى المكان المطلوب
- **مساحة اللعبة** يمكن تحريكها أيضاً
- **العناصر تتبع الماوس** أثناء السحب
- **لا حاجة للدقة العالية** - فقط ضعها في المكان المناسب

### 5. مراقبة التغييرات
- **عداد التغييرات** يظهر في اللوحة
- **تحذير التغييرات غير المحفوظة** باللون الأصفر
- **حالة الوضع** تظهر بوضوح

### 6. الحفظ والإنهاء
- اضغط **"💾 حفظ"** لحفظ التغييرات
- أو اضغط **"❌ إلغاء"** للتراجع
- **"🔒 إيقاف التعديل"** لإنهاء الوضع

## 🎨 أنواع العناصر القابلة للتحرير

### 🟢 الأزرار العادية
- **أزرار الفواكه**: 5 أزرار مع صور الفواكه
- **أزرار المبالغ**: 5 أزرار دائرية بالمبالغ
- **مربعات الرهانات**: عرض أرقام الرهان الحالية
- **عروض المعلومات**: الرصيد وإجمالي الرهان

### 🟡 مساحة اللعبة (جديد!)
- **المنطقة الرئيسية** للعبة
- **أكبر العناصر** (عرض > 200px، ارتفاع > 150px)
- **تحتوي على** Canvas أو عناصر اللعبة
- **قابلة للتحريك** مثل باقي العناصر

## 💡 نصائح للاستخدام الأمثل

### للمبتدئين:
1. **ابدأ بتحريك عنصر واحد** لفهم الآلية
2. **لا تقلق من الدقة** - يمكن التعديل لاحقاً
3. **احفظ بانتظام** لتجنب فقدان العمل
4. **استخدم الإلغاء** إذا لم تعجبك النتيجة

### للمتقدمين:
1. **رتب العناصر منطقياً** من الأعلى للأسفل
2. **اترك مساحات مناسبة** بين الأزرار
3. **اضبط مساحة اللعبة أولاً** ثم الأزرار حولها
4. **اختبر على أجهزة مختلفة**

## 🔧 استكشاف الأخطاء

### المشكلة: العناصر لا تظهر بإطارات
**الحل**: 
- تأكد من تفعيل وضع التعديل
- أعد تحميل الصفحة وحاول مرة أخرى
- تحقق من أن العناصر ظاهرة على الشاشة

### المشكلة: لا يمكن سحب العناصر
**الحل**:
- تأكد من الضغط والسحب بشكل صحيح
- تحقق من أن الوضع مفعل (الطبقة الخضراء ظاهرة)
- جرب عنصر آخر

### المشكلة: التغييرات لا تحفظ
**الحل**:
- تأكد من الضغط على "حفظ" قبل الإغلاق
- تحقق من إعدادات المتصفح للحافظة
- انسخ الكود يدوياً من وحدة التحكم

## 📊 مثال على الاستخدام

```typescript
// مثال على الكود المُصدَّر
symbolButtons: {
  "bar": {
    "left": "10.5%",
    "top": "65.2%",
    "name": "BAR"
  },
  "watermelon": {
    "left": "28.3%",
    "top": "65.1%",
    "name": "WATERMELON"
  }
},
gameBoard: {
  "left": "50.0%",
  "top": "35.5%",
  "width": "min(380px, 85vw)",
  "height": "min(300px, 50vh)",
  "transform": "translateX(-50%)",
  "position": "absolute",
  "zIndex": 10
}
```

## 🆕 الجديد في هذا الإصدار

### مساحة اللعبة قابلة للتحرير
- **تحريك مساحة اللعبة** بنفس طريقة الأزرار
- **إطار ذهبي مميز** لسهولة التعرف
- **حفظ موقع مساحة اللعبة** في التكوين
- **تحديد تلقائي** لمساحة اللعبة

### تحسينات الواجهة
- **تعليمات محدثة** تشمل مساحة اللعبة
- **ألوان مميزة** لكل نوع عنصر
- **مؤشرات بصرية محسنة**

## 🎯 الخطوات التالية

بعد حفظ التكوين:
1. **انسخ الكود** من الحافظة
2. **افتح ملف** `src/utils/buttonPositions.ts`
3. **الصق التكوين** في المكان المناسب
4. **احفظ الملف** وأعد تحميل اللعبة
5. **اختبر النتيجة** على الجهاز

---

**💡 نصيحة**: وضع التعديل البسيط مثالي للتعديلات السريعة والمباشرة. للتحكم الدقيق، استخدم الأداة المتقدمة.
