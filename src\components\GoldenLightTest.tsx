import React, { useState } from 'react';

export default function GoldenLightTest() {
  const [show, setShow] = useState(false);

  return (
    <div style={{
      width: '100vw',
      height: '100vh',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      background: '#222',
    }}>
      <div style={{ position: 'relative', width: 100, height: 100 }}>
        {/* Golden Light Effect */}
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: 100,
            height: 100,
            borderRadius: '50%',
            pointerEvents: 'none',
            background: show ? 'radial-gradient(circle, #FFD700 0%, #FFD70099 60%, transparent 100%)' : 'none',
            boxShadow: show ? '0 0 40px 20px #FFD700, 0 0 80px 40px #FFD70088' : 'none',
            opacity: show ? 0.85 : 0,
            transition: 'opacity 0.1s, box-shadow 0.1s',
            zIndex: 10,
            mixBlendMode: show ? 'screen' : 'normal',
          }}
        ></div>
        <button
          style={{
            width: 100,
            height: 100,
            borderRadius: '50%',
            border: '2px solid #FFD700',
            background: '#333',
            color: '#FFD700',
            fontSize: 24,
            cursor: 'pointer',
            zIndex: 20,
            position: 'relative',
          }}
          onPointerDown={() => setShow(true)}
          onPointerUp={() => setShow(false)}
          onPointerLeave={() => setShow(false)}
          onTouchStart={() => setShow(true)}
          onTouchEnd={() => setShow(false)}
        >
          اضغط هنا
        </button>
      </div>
    </div>
  );
}
