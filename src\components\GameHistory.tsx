import React, { useState } from 'react';
import { COLORS, COLOR_SCHEMES } from '../theme/colors';

interface GameHistoryProps {
  gameHistory: string[][];
}

const GameHistory: React.FC<GameHistoryProps> = ({ gameHistory }) => {
  // const [openIndex, setOpenIndex] = useState<number | null>(null);

  if (gameHistory.length === 0) {
    return (
      <div className="flex items-center justify-center" style={{ width: '100%', margin: '0 auto' }}>
        <div className="p-2 rounded flex items-center justify-center relative"
             style={{
               background: 'linear-gradient(135deg, #4A0E4E 0%, #6B1B7A 50%, #4A0E4E 100%)',
               width: '100%',
               height: '65px',
               boxShadow: `inset 0 2px 4px rgba(0,0,0,0.3), inset 0 -1px 2px rgba(107,27,122,0.2)`,
               border: `2px solid #8B5C2A`,
               borderRadius: '8px'
             }}>
          <div className="text-center text-xs" style={{ color: COLORS.GOLD_ACCENT.BRIGHT, fontSize: '13px' }}></div>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="flex items-center justify-center" style={{ width: '100%', margin: '0 auto', position: 'relative' }}>
        {/* مساحة لأيقونة مستقبلية في الجهة اليسرى */}
        <div style={{ width: '40px', height: '100%', position: 'absolute', left: 0, top: 0, zIndex: 2 }}></div>
        <div className="p-2 rounded flex items-center justify-center relative"
             style={{
               background: 'linear-gradient(135deg, #4A0E4E 0%, #6B1B7A 50%, #4A0E4E 100%)',
               width: '320px',
               height: '64px',
               overflow: 'hidden',
               boxShadow: `inset 0 2px 4px rgba(0,0,0,0.3), inset 0 -1px 2px rgba(107,27,122,0.2)`,
               border: `2px solid #8B5C2A`,
               padding: '0 4px',
               borderRadius: '8px',
               margin: 0
             }}>
          <div style={{
            display: 'flex',
            gap: '8px',
            width: 'calc(100% - 40px)',
            height: '100%',
            overflowX: 'auto',
            alignItems: 'center',
            justifyContent: 'flex-start',
            paddingLeft: '12px',
            paddingRight: '12px',
            position: 'relative'
          }} className="hide-scrollbar">
            {gameHistory.slice(0, 25).flat().map((symbol, idx) => {
              // استخراج الرمز والمضاعف من النص
              const symbolMatch = symbol.match(/^(.+?)(?:\s+x(\d+))?$/);
              const baseSymbol = symbolMatch ? symbolMatch[1] : symbol;
              const multiplier = symbolMatch ? symbolMatch[2] : null;
              
              return (
                <div key={idx} style={{ margin: '0 8px', display: 'inline-block' }}>
                  <div
                    style={{
                      fontSize: '18px',
                      flexShrink: 0,
                      lineHeight: '1.1',
                      textShadow: '0 2px 4px rgba(0,0,0,0.3), 0 -1px 2px rgba(255,255,255,0.15)',
                      background: 'linear-gradient(135deg, #2D1B3D 0%, #4A0E4E 100%)',
                      border: '2.5px solid #FFD700',
                      borderRadius: '10px',
                      padding: '6px 10px',
                      boxShadow: '0 2px 8px #FFD70044, 0 2px 8px #0004',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      minWidth: '42px',
                      minHeight: '42px',
                      maxWidth: '42px',
                      maxHeight: '42px',
                      transition: 'all 0.18s',
                      position: 'relative',
                    }}
                  >
                    {/* عرض صورة الفاكهة أو البار بشكل خاص */}
                    {baseSymbol === 'BAR' ? (
                      <div style={{
                        width: '36px',
                        height: '36px',
                        background: 'linear-gradient(135deg, #8B4513 0%, #A0522D 50%, #8B4513 100%)',
                        borderRadius: '6px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        boxShadow: '0 2px 6px rgba(0,0,0,0.25), inset 0 1px 2px rgba(255,255,255,0.1)',
                        margin: '0 auto',
                        border: '1px solid #654321',
                      }}>
                        <span style={{
                          color: '#FFD700',
                          fontWeight: 'bold',
                          fontSize: '9px',
                          letterSpacing: '1px',
                          textShadow: '0 1px 2px rgba(0,0,0,0.8), 0 0 4px #FFD70044',
                          lineHeight: 1.1,
                          textAlign: 'center',
                          whiteSpace: 'pre-line',
                        }}>{'BAR\nBAR\nBAR'}</span>
                      </div>
                    ) : baseSymbol === 'APPLE' || baseSymbol === 'BANANA' || baseSymbol === 'LEMON' || baseSymbol === 'WATERMELON' ? (
                      <div className="relative" style={{ width: '34px', height: '34px' }}>
                        <img
                          src={
                            baseSymbol === 'APPLE' ? '/images/3.png' :
                            baseSymbol === 'BANANA' ? '/images/6.png' :
                            baseSymbol === 'LEMON' ? '/images/8.png' :
                            baseSymbol === 'WATERMELON' ? '/images/12.png' : ''
                          }
                          alt={baseSymbol}
                          style={{
                            width: '34px',
                            height: '34px',
                            objectFit: 'contain',
                            display: 'block',
                            margin: '0 auto',
                            filter: 'drop-shadow(0 0 6px #FFD70088)',
                            // إذا كانت نصف فاكهة، اقطع النصف الأيمن بصرياً
                            clipPath: multiplier === '2' ? 'polygon(0 0, 50% 0, 50% 100%, 0 100%)' : 'none',
                          }}
                        />
                        {/* إضافة خط فاصل لنصف الفاكهة */}
                        {multiplier === '2' && (
                          <>
                            <div style={{
                              position: 'absolute',
                              top: '0',
                              left: '50%',
                              width: '1px',
                              height: '100%',
                              background: 'linear-gradient(to bottom, #FFD700, #FF6B35, #FFD700)',
                              boxShadow: '0 0 3px #FFD700',
                              transform: 'translateX(-50%)',
                              zIndex: 10,
                            }} />
                            <div style={{
                              position: 'absolute',
                              top: '0',
                              right: '0',
                              width: '50%',
                              height: '100%',
                              background: 'linear-gradient(90deg, transparent 0%, rgba(255, 107, 53, 0.2) 100%)',
                              borderRadius: '0 6px 6px 0',
                              pointerEvents: 'none',
                            }} />
                          </>
                        )}
                      </div>
                    ) : (
                      <span style={{ fontWeight: 'bold', color: '#FFD700', fontSize: '18px' }}>{baseSymbol}</span>
                    )}
                    
                    {/* عرض المضاعف إذا وجد */}
                    {multiplier && (
                      <div style={{
                        position: 'absolute',
                        top: '-8px',
                        right: '-8px',
                        background: multiplier === '2' ? '#FF6B35' : '#FFD700',
                        color: multiplier === '2' ? '#FFF' : '#000',
                        borderRadius: '50%',
                        width: '20px',
                        height: '20px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '12px',
                        fontWeight: 'bold',
                        border: '2px solid #000',
                        boxShadow: multiplier === '2'
                          ? '0 2px 4px rgba(0,0,0,0.3), 0 0 6px #FF6B3588'
                          : '0 2px 4px rgba(0,0,0,0.3)',
                      }}>
                        x{multiplier}
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
          <style>{`.hide-scrollbar::-webkit-scrollbar { display: none; }`}</style>
        </div>
      </div>
    </>
  );
};

export default GameHistory;
