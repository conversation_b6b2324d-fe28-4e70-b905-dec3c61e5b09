// إعدادات iPhone 14 - منفصلة ومحمية
export const iPhone14Config = {
  device: {
    name: 'iPhone 14 Pro Max Real',
    width: 430,
    height: 932,
  },
  backgroundImage: '/images/bg-428x926.webp',
  
  gameBoard: {
    left: '50%',
    top: '22%',
    width: 'min(340px, 85vw)',
    height: 'min(300px, 60vh)',
    transform: 'translateX(-50%)',
    position: 'absolute',
    zIndex: 10,
  },

  symbolButtons: {
    "bar": { "left": "12%", "top": "65%", "name": "BAR" },
    "watermelon": { "left": "29%", "top": "65%", "name": "WATERMELON" },
    "lemon": { "left": "50%", "top": "65%", "name": "LEMON" },
    "banana": { "left": "71%", "top": "65%", "name": "BANANA" },
    "apple": { "left": "88%", "top": "65%", "name": "APPLE" }
  },

  amountButtons: {
    "amount1": { "left": "7%", "top": "75%", "name": "AMOUNT_1", "value": 5000 },
    "amount2": { "left": "19%", "top": "75%", "name": "AMOUNT_2", "value": 3000 },
    "amount3": { "left": "31%", "top": "75%", "name": "AMOUNT_3", "value": 1000 },
    "amount4": { "left": "43%", "top": "75%", "name": "AMOUNT_4", "value": 500 },
    "amount5": { "left": "55%", "top": "75%", "name": "AMOUNT_5", "value": 100 }
  },

  actionButtons: {
    spin: { left: '65%', top: '75%' }
  },

  betRectangles: {
    "bar": { "left": "12%", "top": "71%", "symbol": "BAR" },
    "watermelon": { "left": "28%", "top": "71%", "symbol": "WATERMELON" },
    "lemon": { "left": "50%", "top": "71%", "symbol": "LEMON" },
    "banana": { "left": "67%", "top": "71%", "symbol": "BANANA" },
    "apple": { "left": "90%", "top": "71%", "symbol": "APPLE" }
  },

  betNumbers: {},
  
  topDisplays: {
    "balanceDisplay": { "left": "31%", "top": "17%", "name": "BALANCE" },
    "totalBetDisplay": { "left": "89%", "top": "17%", "name": "TOTAL_BET" }
  },
};

// حماية من التعديلات الخارجية
Object.freeze(iPhone14Config);
Object.freeze(iPhone14Config.device);
Object.freeze(iPhone14Config.symbolButtons);
Object.freeze(iPhone14Config.amountButtons);
Object.freeze(iPhone14Config.betRectangles);
Object.freeze(iPhone14Config.topDisplays); 