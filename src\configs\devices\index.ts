// نظام تجميع آمن لجميع الأجهزة
import { iPhone14Config } from './iphone14';
import { SamsungGalaxyS8Config } from './samsung-galaxy-s8';

// تجميع جميع الأجهزة مع الحماية
export const DEVICE_CONFIGS = {
  'iPhone 14 Pro Max Real': iPhone14Config,
  'iPhone 14 Pro Max Old': { ...iPhone14Config, device: { ...iPhone14Config.device, width: 428, height: 926 } },
  'iPhone 12/13/14': { ...iPhone14Config, device: { ...iPhone14Config.device, name: 'iPhone 12/13/14', width: 390, height: 844 } },
  'Samsung Galaxy S8': SamsungGalaxyS8Config,
  'Samsung Galaxy (360x740)': { ...SamsungGalaxyS8Config, device: { ...SamsungGalaxyS8Config.device, name: 'Samsung Galaxy (360x740)', width: 360, height: 740 } },
  'Desktop': { ...iPhone14Config, device: { ...iPhone14Config.device, name: 'Desktop', width: 1920, height: 1080 } },
};

// حماية من التعديلات الخارجية
Object.freeze(DEVICE_CONFIGS);

// دالة آمنة للحصول على إعدادات الجهاز
export function getSafeDeviceConfig(deviceName: string) {
  const config = DEVICE_CONFIGS[deviceName];
  if (!config) {
    console.warn(`Device config not found for: ${deviceName}`);
    return DEVICE_CONFIGS['iPhone 14 Pro Max Real']; // إعداد افتراضي آمن
  }
  
  // إرجاع نسخة عميقة لمنع التعديل
  return JSON.parse(JSON.stringify(config));
}

// دالة للتحقق من سلامة الإعدادات
export function validateDeviceConfig(deviceName: string, config: any) {
  const requiredFields = ['device', 'symbolButtons', 'amountButtons', 'betRectangles'];
  
  for (const field of requiredFields) {
    if (!config[field]) {
      throw new Error(`Missing required field: ${field} for device: ${deviceName}`);
    }
  }
  
  return true;
}

// دالة لإنشاء نسخة احتياطية
export function createDeviceBackup(deviceName: string) {
  const config = DEVICE_CONFIGS[deviceName];
  if (!config) {
    throw new Error(`Device not found: ${deviceName}`);
  }
  
  const backup = {
    deviceName,
    timestamp: new Date().toISOString(),
    config: JSON.parse(JSON.stringify(config))
  };
  
  // حفظ النسخة الاحتياطية (يمكن حفظها في localStorage أو ملف)
  localStorage.setItem(`device_backup_${deviceName}`, JSON.stringify(backup));
  
  return backup;
}

// دالة لاستعادة النسخة الاحتياطية
export function restoreDeviceBackup(deviceName: string) {
  const backup = localStorage.getItem(`device_backup_${deviceName}`);
  if (!backup) {
    throw new Error(`No backup found for device: ${deviceName}`);
  }
  
  return JSON.parse(backup);
} 