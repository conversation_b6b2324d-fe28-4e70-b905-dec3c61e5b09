import { BetsOnTypes } from '../types/game';

// نظام اقتصادي بسيط ومفعل للعبة
export class GameEconomics {
  private static instance: GameEconomics;
  
  // إحصائيات اللعبة
  private totalBets = 0;
  private totalWinnings = 0;
  private dailyStats: { date: string; bets: number; winnings: number }[] = [];
  
  // حدود الخسائر
  private readonly DAILY_LOSS_LIMIT = 500000; // 500 ألف دولار يومياً
  private readonly MAX_SINGLE_ROUND_LOSS = 100000; // 100 ألف دولار لكل جولة
  
  // رسوم الخدمة
  private readonly SERVICE_FEE_RATE = 0.02; // 2% رسوم خدمة
  
  private constructor() {
    this.loadStats();
  }

  public static getInstance(): GameEconomics {
    if (!GameEconomics.instance) {
      GameEconomics.instance = new GameEconomics();
    }
    return GameEconomics.instance;
  }

  // تسجيل نتيجة جولة
  public recordRoundResult(bets: BetsOnTypes, winnings: number): void {
    console.log('🟡 [ECONOMICS] recordRoundResult - bets:', bets, 'winnings:', winnings);
    const totalBets = Object.values(bets).reduce((sum, bet) => sum + bet, 0);
    
    this.totalBets += totalBets;
    this.totalWinnings += winnings;
    
    // تحديث الإحصائيات اليومية
    const today = new Date().toISOString().split('T')[0];
    const todayStats = this.dailyStats.find(stat => stat.date === today);
    
    if (todayStats) {
      todayStats.bets += totalBets;
      todayStats.winnings += winnings;
    } else {
      this.dailyStats.push({
        date: today,
        bets: totalBets,
        winnings: winnings
      });
    }
    
    this.saveStats();
  }

  // حساب رسوم الخدمة
  public calculateServiceFee(totalBets: number): number {
    return Math.floor(totalBets * this.SERVICE_FEE_RATE);
  }

  // التحقق من حدود الخسائر
  public checkLossLimits(bets: BetsOnTypes): {
    canProceed: boolean;
    reason?: string;
    dailyLoss: number;
    maxSingleLoss: number;
  } {
    const totalBets = Object.values(bets).reduce((sum, bet) => sum + bet, 0);
    const todayStats = this.getTodayStats();
    const dailyLoss = todayStats.winnings - todayStats.bets;
    
    // حساب أسوأ خسارة محتملة لهذه الجولة
    const maxSingleLoss = this.calculateMaxPotentialLoss(bets);
    
    if (dailyLoss - maxSingleLoss < -this.DAILY_LOSS_LIMIT) {
      console.log('🚨 [ECONOMICS] رفض الرهان: تجاوز الحد اليومي للخسائر', {dailyLoss, maxSingleLoss, limit: this.DAILY_LOSS_LIMIT});
      return {
        canProceed: false,
        reason: `تجاوز الحد اليومي للخسائر (${this.DAILY_LOSS_LIMIT.toLocaleString()}$)`,
        dailyLoss,
        maxSingleLoss
      };
    }
    
    if (maxSingleLoss > this.MAX_SINGLE_ROUND_LOSS) {
      console.log('�� [ECONOMICS] رفض الرهان: تجاوز الحد الأقصى للخسارة في جولة واحدة', {maxSingleLoss, limit: this.MAX_SINGLE_ROUND_LOSS});
      return {
        canProceed: false,
        reason: `تجاوز الحد الأقصى للخسارة في جولة واحدة (${this.MAX_SINGLE_ROUND_LOSS.toLocaleString()}$)`,
        dailyLoss,
        maxSingleLoss
      };
    }
    
    return {
      canProceed: true,
      dailyLoss,
      maxSingleLoss
    };
  }

  // حساب أسوأ خسارة محتملة
  private calculateMaxPotentialLoss(bets: BetsOnTypes): number {
    let maxLoss = 0;
    
    for (const [symbol, bet] of Object.entries(bets)) {
      if (bet > 0) {
        // أسوأ حالة: Lucky 3 مع أعلى مضاعف
        const multiplier = this.getSymbolMultiplier(symbol);
        const worstCase = bet * multiplier * 3; // Lucky 3 multiplier
        maxLoss = Math.max(maxLoss, worstCase);
      }
    }
    
    const totalBets = Object.values(bets).reduce((sum, bet) => sum + bet, 0);
    return maxLoss - totalBets;
  }

  // الحصول على مضاعف الرمز
  private getSymbolMultiplier(symbol: string): number {
    const multipliers: { [key: string]: number } = {
      '🍎': 3,
      '🍋': 8,
      '🍌': 6,
      '🍉': 12,
      'BAR': 30
    };
    return multipliers[symbol] || 1;
  }

  // الحصول على إحصائيات اليوم
  private getTodayStats(): { bets: number; winnings: number } {
    const today = new Date().toISOString().split('T')[0];
    const todayStats = this.dailyStats.find(stat => stat.date === today);
    
    return todayStats || { bets: 0, winnings: 0 };
  }

  // الحصول على تقرير اقتصادي
  public getEconomicReport(): {
    totalBets: number;
    totalWinnings: number;
    netProfit: number;
    profitMargin: number;
    todayStats: { bets: number; winnings: number; netProfit: number };
    limits: {
      dailyLossLimit: number;
      maxSingleRoundLoss: number;
      serviceFeeRate: number;
    };
  } {
    const netProfit = this.totalWinnings - this.totalBets;
    const profitMargin = this.totalBets > 0 ? (netProfit / this.totalBets) * 100 : 0;
    const todayStats = this.getTodayStats();
    
    return {
      totalBets: this.totalBets,
      totalWinnings: this.totalWinnings,
      netProfit,
      profitMargin,
      todayStats: {
        ...todayStats,
        netProfit: todayStats.winnings - todayStats.bets
      },
      limits: {
        dailyLossLimit: this.DAILY_LOSS_LIMIT,
        maxSingleRoundLoss: this.MAX_SINGLE_ROUND_LOSS,
        serviceFeeRate: this.SERVICE_FEE_RATE
      }
    };
  }

  // إعادة تعيين الإحصائيات
  public resetStats(): void {
    this.totalBets = 0;
    this.totalWinnings = 0;
    this.dailyStats = [];
    this.saveStats();
  }

  // حفظ الإحصائيات
  private saveStats(): void {
    const data = {
      totalBets: this.totalBets,
      totalWinnings: this.totalWinnings,
      dailyStats: this.dailyStats
    };
    localStorage.setItem('game_economics_stats', JSON.stringify(data));
  }

  // تحميل الإحصائيات
  private loadStats(): void {
    const saved = localStorage.getItem('game_economics_stats');
    if (saved) {
      const data = JSON.parse(saved);
      this.totalBets = data.totalBets || 0;
      this.totalWinnings = data.totalWinnings || 0;
      this.dailyStats = data.dailyStats || [];
    }
  }
}

// تصدير instance واحد للاستخدام في التطبيق
export const gameEconomics = GameEconomics.getInstance(); 