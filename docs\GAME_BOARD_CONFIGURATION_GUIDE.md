# دليل تكوين لوحة اللعبة للأجهزة المختلفة

## 📋 نظرة عامة

هذا الدليل يوضح كيفية تكوين `BOARD_SQUARES_CONFIG` لشاشات الأجهزة المختلفة.

## 🎯 المربعات المتاحة

### المربعات الظاهرة حالياً (16 مربع):
```
0  1  2  3  4
5  -  -  -  9
10 -  -  -  14
15 -  -  -  19
20 21 22 23 24
```

### المربعات المخفية (9 مربعات):
```
-  -  -  -  -
-  6  7  8  -
-  11 12 13 -
-  16 17 18 -
-  -  -  -  -
```

## 🔧 كيفية التعديل

### لإظهار جميع المربعات (25 مربع):
```typescript
export const BOARD_SQUARES_CONFIG: SquareConfig[] = [
  // الصف الأول (الإطار العلوي)
  { gridIndex: 0, symbol: 'APPLE', type: 'normal' },
  { gridIndex: 1, symbol: 'LEMON', type: 'halfFruit' },
  { gridIndex: 2, symbol: 'BAR', type: 'stackedBar' },
  { gridIndex: 3, symbol: 'LEMON', type: 'normal' },
  { gridIndex: 4, symbol: 'APPLE', type: 'normal' },

  // الصف الثاني
  { gridIndex: 5, symbol: 'BANANA', type: 'halfFruit' },
  { gridIndex: 6, symbol: '', type: 'inner' },
  { gridIndex: 7, symbol: '', type: 'inner' },
  { gridIndex: 8, symbol: '', type: 'inner' },
  { gridIndex: 9, symbol: 'APPLE', type: 'halfFruit' },

  // الصف الثالث
  { gridIndex: 10, symbol: 'LUCKY', type: 'luckyDoubleText', luckyNumber: 2 },
  { gridIndex: 11, symbol: '', type: 'inner' },
  { gridIndex: 12, symbol: '', type: 'inner' },
  { gridIndex: 13, symbol: '', type: 'inner' },
  { gridIndex: 14, symbol: 'LUCKY', type: 'luckyTripleText', luckyNumber: 3 },

  // الصف الرابع
  { gridIndex: 15, symbol: 'LEMON', type: 'normal' },
  { gridIndex: 16, symbol: '', type: 'inner' },
  { gridIndex: 17, symbol: '', type: 'inner' },
  { gridIndex: 18, symbol: '', type: 'inner' },
  { gridIndex: 19, symbol: 'BANANA', type: 'normal' },

  // الصف الخامس (الإطار السفلي)
  { gridIndex: 20, symbol: 'APPLE', type: 'normal' },
  { gridIndex: 21, symbol: 'BANANA', type: 'normal' },
  { gridIndex: 22, symbol: 'WATERMELON', type: 'normal' },
  { gridIndex: 23, symbol: 'WATERMELON', type: 'halfFruit' },
  { gridIndex: 24, symbol: 'APPLE', type: 'normal' }
];
```

### لإظهار مربعات محددة فقط:
```typescript
// مثال: إظهار المربعات المركزية فقط (6, 7, 8, 11, 12, 13, 16, 17, 18)
// أضف هذه المربعات إلى المصفوفة:
{ gridIndex: 6, symbol: '', type: 'inner' },
{ gridIndex: 7, symbol: '', type: 'inner' },
{ gridIndex: 8, symbol: '', type: 'inner' },
{ gridIndex: 11, symbol: '', type: 'inner' },
{ gridIndex: 12, symbol: '', type: 'inner' },
{ gridIndex: 13, symbol: '', type: 'inner' },
{ gridIndex: 16, symbol: '', type: 'inner' },
{ gridIndex: 17, symbol: '', type: 'inner' },
{ gridIndex: 18, symbol: '', type: 'inner' },
```

## 📱 تكوينات مقترحة للأجهزة

### للهواتف الصغيرة (360x740):
- **الحالة الحالية**: 16 مربع (الإطار الخارجي فقط)
- **السبب**: مساحة محدودة

### للهواتف المتوسطة (390x844):
- **مقترح**: 20 مربع (إضافة المربعات المركزية العلوية والسفلية)
- **المربعات الإضافية**: 6, 7, 8, 16, 17, 18

### للهواتف الكبيرة (412x915+):
- **مقترح**: 25 مربع (جميع المربعات)
- **المربعات الإضافية**: 11, 12, 13

### للتابلت (768x1024):
- **مقترح**: 25 مربع (جميع المربعات)
- **السبب**: مساحة كافية

### للديسكتوب (1920x1080):
- **مقترح**: 25 مربع (جميع المربعات)
- **السبب**: مساحة كبيرة

## ⚠️ ملاحظات مهمة

1. **ترتيب المربعات**: يجب الحفاظ على ترتيب `gridIndex` من 0 إلى 24
2. **النوع**: المربعات المركزية يجب أن تكون `type: 'inner'`
3. **الرمز**: المربعات المركزية فارغة `symbol: ''`
4. **التأثير**: تغيير المربعات يؤثر على منطق اللعبة

## 🔄 خطوات التعديل

1. افتح `src/constants/gameConfig.ts`
2. ابحث عن `BOARD_SQUARES_CONFIG`
3. أضف أو احذف المربعات حسب الحاجة
4. احفظ الملف
5. اختبر على الجهاز المستهدف

## 📊 جدول المربعات

| المربع | النوع | الرمز | الحالة |
|--------|-------|-------|--------|
| 0-4 | normal/halfFruit/stackedBar | APPLE/LEMON/BAR | ظاهر |
| 5 | halfFruit | BANANA | ظاهر |
| 6-8 | inner | فارغ | مخفي |
| 9 | halfFruit | APPLE | ظاهر |
| 10 | luckyDoubleText | LUCKY | ظاهر |
| 11-13 | inner | فارغ | مخفي |
| 14 | luckyTripleText | LUCKY | ظاهر |
| 15 | normal | LEMON | ظاهر |
| 16-18 | inner | فارغ | مخفي |
| 19 | normal | BANANA | ظاهر |
| 20-24 | normal/halfFruit | APPLE/BANANA/WATERMELON | ظاهر | 