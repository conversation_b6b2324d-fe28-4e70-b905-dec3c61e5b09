// نظام العرض المتجاوب المحسن
export interface ScreenSize {
  width: number;
  height: number;
  type: 'mobile' | 'tablet' | 'desktop';
  orientation: 'portrait' | 'landscape';
}

export interface ResponsiveLayout {
  gameBoard: {
    width: string;
    height: string;
    top: string;
    left: string;
    scale: number;
  };
  bettingControls: {
    positions: Array<{ left: string; bottom: string }>;
    buttonSize: string;
  };
  amountControls: {
    positions: Array<{ left: string; bottom: string }>;
    buttonSize: string;
  };
  balance: {
    top: string;
    left: string;
    fontSize: string;
  };
  background: {
    size: 'cover' | 'contain';
    position: string;
    backgroundColor: string;
  };
}

// نقاط التوقف للشاشات
export const BREAKPOINTS = {
  mobile: { maxWidth: 768, maxHeight: 1024 },
  tablet: { maxWidth: 1024, maxHeight: 1366 },
  desktop: { minWidth: 1025 }
};

// تحديد نوع الشاشة
export const getScreenSize = (): ScreenSize => {
  const width = window.innerWidth;
  const height = window.innerHeight;
  
  let type: 'mobile' | 'tablet' | 'desktop';
  if (width <= BREAKPOINTS.mobile.maxWidth) {
    type = 'mobile';
  } else if (width <= BREAKPOINTS.tablet.maxWidth) {
    type = 'tablet';
  } else {
    type = 'desktop';
  }
  
  const orientation = width > height ? 'landscape' : 'portrait';
  
  return { width, height, type, orientation };
};

// دالة لتحسين إعدادات الخلفية بناءً على نسبة العرض إلى الارتفاع
const optimizeBackgroundForAspectRatio = (width: number, height: number) => {
  const aspectRatio = width / height;

  // iPhone 14 Pro Max وأجهزة مشابهة (428x926)
  if (width === 428 && height === 926) {
    return {
      size: 'contain' as const,
      position: 'center center',
      backgroundColor: '#1a1a2e'
    };
  }

  // الشاشات الطويلة جداً (نسبة أقل من 0.5)
  if (aspectRatio <= 0.46) {
    return {
      size: 'cover' as const,
      position: 'center center',
      backgroundColor: '#1a1a2e'
    };
  }

  // الشاشات العريضة جداً (21:9 أو أكثر)
  if (aspectRatio >= 2.33) {
    return {
      size: 'contain' as const,
      position: 'center center',
      backgroundColor: '#1a1a2e'
    };
  }

  // الشاشات الطويلة (9:21 أو أقل)
  if (aspectRatio <= 0.43) {
    return {
      size: 'cover' as const,
      position: 'center center',
      backgroundColor: '#1a1a2e'
    };
  }

  // الشاشات المربعة تقريباً (3:4 إلى 4:3)
  if (aspectRatio >= 0.75 && aspectRatio <= 1.33) {
    return {
      size: 'contain' as const,
      position: 'center center',
      backgroundColor: '#1a1a2e'
    };
  }

  // الافتراضي للشاشات العادية
  return {
    size: 'contain' as const,
    position: 'center center',
    backgroundColor: '#1a1a2e'
  };
};

// تخطيطات محددة لكل نوع شاشة
export const getResponsiveLayout = (screenSize: ScreenSize): ResponsiveLayout => {
  const { width, height, type, orientation } = screenSize;

  // تخطيط خاص لـ iPhone 14 Pro Max (428x926)
  if (width === 428 && height === 926 && type === 'mobile' && orientation === 'portrait') {
    return {
      gameBoard: {
        width: 'min(380px, 85vw)',
        height: 'min(350px, 60vh)',
        top: '18%',
        left: '50%',
        scale: 1.05
      },
      bettingControls: {
        positions: [
          { left: '20px', bottom: '200px' },   // BAR
          { left: '85px', bottom: '195px' },   // WATERMELON
          { left: '195px', bottom: '195px' },  // LEMON
          { left: '305px', bottom: '195px' },  // BANANA
          { left: '415px', bottom: '195px' },  // APPLE
        ],
        buttonSize: '52px'
      },
      amountControls: {
        positions: [
          { left: '20px', bottom: '90px' },
          { left: '80px', bottom: '90px' },
          { left: '140px', bottom: '90px' },
          { left: '200px', bottom: '90px' },
          { left: '260px', bottom: '90px' },
        ],
        buttonSize: '42px'
      },
      balance: {
        top: '15px',
        left: '15px',
        fontSize: '16px'
      },
      background: optimizeBackgroundForAspectRatio(width, height)
    };
  }

  // تخطيط خاص لـ iPhone 12/13/14 (390x844)
  if (width === 390 && height === 844 && type === 'mobile' && orientation === 'portrait') {
    return {
      gameBoard: {
        width: 'min(340px, 85vw)',
        height: 'min(300px, 60vh)',
        top: '34%', // تم تحريكها للأسفل بناءً على طلب المستخدم
        left: '50%',
        scale: 1.0
      },
      bettingControls: {
        positions: [
          { left: '15px', bottom: '180px' },   // BAR
          { left: '75px', bottom: '175px' },   // WATERMELON
          { left: '180px', bottom: '175px' },  // LEMON
          { left: '285px', bottom: '175px' },  // BANANA
          { left: '390px', bottom: '175px' },  // APPLE
        ],
        buttonSize: '45px'
      },
      amountControls: {
        positions: [
          { left: '15px', bottom: '80px' },
          { left: '70px', bottom: '80px' },
          { left: '125px', bottom: '80px' },
          { left: '180px', bottom: '80px' },
          { left: '235px', bottom: '80px' },
        ],
        buttonSize: '35px'
      },
      balance: {
        top: '12px',
        left: '12px',
        fontSize: '14px'
      },
      background: optimizeBackgroundForAspectRatio(width, height)
    };
  }
  
  // تخطيط الهاتف المحمول (542x857)
  if (type === 'mobile' && orientation === 'portrait') {
    return {
      gameBoard: {
        width: 'min(420px, 85vw)',
        height: 'min(320px, 60vh)',
        top: '25%',
        left: '50%',
        scale: 0.85
      },
      bettingControls: {
        positions: [
          { left: '15px', bottom: '180px' },   // BAR
          { left: '75px', bottom: '175px' },   // WATERMELON
          { left: '180px', bottom: '175px' },  // LEMON
          { left: '285px', bottom: '175px' },  // BANANA
          { left: '390px', bottom: '175px' },  // APPLE
        ],
        buttonSize: '45px'
      },
      amountControls: {
        positions: [
          { left: '15px', bottom: '80px' },
          { left: '70px', bottom: '80px' },
          { left: '125px', bottom: '80px' },
          { left: '180px', bottom: '80px' },
          { left: '235px', bottom: '80px' },
        ],
        buttonSize: '35px'
      },
      balance: {
        top: '12px',
        left: '12px',
        fontSize: '14px'
      },
      background: optimizeBackgroundForAspectRatio(width, height)
    };
  }
  
  // تخطيط الهاتف المحمول الأفقي
  if (type === 'mobile' && orientation === 'landscape') {
    return {
      gameBoard: {
        width: 'min(380px, 50vw)',
        height: 'min(280px, 70vh)',
        top: '20%',
        left: '30%',
        scale: 0.75
      },
      bettingControls: {
        positions: [
          { left: '60%', bottom: '120px' },
          { left: '65%', bottom: '120px' },
          { left: '70%', bottom: '120px' },
          { left: '75%', bottom: '120px' },
          { left: '80%', bottom: '120px' },
        ],
        buttonSize: '40px'
      },
      amountControls: {
        positions: [
          { left: '60%', bottom: '60px' },
          { left: '65%', bottom: '60px' },
          { left: '70%', bottom: '60px' },
          { left: '75%', bottom: '60px' },
          { left: '80%', bottom: '60px' },
        ],
        buttonSize: '30px'
      },
      balance: {
        top: '10px',
        left: '10px',
        fontSize: '12px'
      },
      background: optimizeBackgroundForAspectRatio(width, height)
    };
  }
  
  // تخطيط التابلت
  if (type === 'tablet') {
    return {
      gameBoard: {
        width: 'min(500px, 70vw)',
        height: 'min(380px, 55vh)',
        top: '30%',
        left: '50%',
        scale: 1.0
      },
      bettingControls: {
        positions: [
          { left: '80px', bottom: '200px' },
          { left: '150px', bottom: '195px' },
          { left: '280px', bottom: '195px' },
          { left: '410px', bottom: '195px' },
          { left: '540px', bottom: '195px' },
        ],
        buttonSize: '55px'
      },
      amountControls: {
        positions: [
          { left: '80px', bottom: '100px' },
          { left: '140px', bottom: '100px' },
          { left: '200px', bottom: '100px' },
          { left: '260px', bottom: '100px' },
          { left: '320px', bottom: '100px' },
        ],
        buttonSize: '45px'
      },
      balance: {
        top: '16px',
        left: '16px',
        fontSize: '16px'
      },
      background: optimizeBackgroundForAspectRatio(width, height)
    };
  }
  
  // تخطيط الكمبيوتر (افتراضي)
  return {
    gameBoard: {
      width: 'min(450px, 88vw)',
      height: 'min(340px, 68vh)',
      top: '37.5%',
      left: '50%',
      scale: 1.0
    },
    bettingControls: {
      positions: [
        { left: '58px', bottom: '190px' },
        { left: '124px', bottom: '185px' },
        { left: '235px', bottom: '185px' },
        { left: '340px', bottom: '185px' },
        { left: '440px', bottom: '185px' },
      ],
      buttonSize: '50px'
    },
    amountControls: {
      positions: [
        { left: '20px', bottom: '87px' },
        { left: '84px', bottom: '87px' },
        { left: '152px', bottom: '87px' },
        { left: '220px', bottom: '87px' },
        { left: '280px', bottom: '87px' },
      ],
      buttonSize: '40px'
    },
    balance: {
      top: '16px',
      left: '16px',
      fontSize: '16px'
    },
    background: optimizeBackgroundForAspectRatio(width, height)
  };
}

// دالة مساعدة لتطبيق الخلفية المتجاوبة
export const applyResponsiveBackground = (element: HTMLElement, screenSize: ScreenSize) => {
  const layout = getResponsiveLayout(screenSize);
  const { background } = layout;

  element.style.backgroundSize = background.size;
  element.style.backgroundPosition = background.position;
  element.style.backgroundColor = background.backgroundColor;

  // إضافة class للتحكم في CSS
  element.classList.add('responsive-background');

  // إضافة class خاص بنوع الجهاز
  element.classList.remove('mobile-bg', 'tablet-bg', 'desktop-bg');
  element.classList.add(`${screenSize.type}-bg`);

  // إضافة class خاص بالاتجاه
  element.classList.remove('portrait-bg', 'landscape-bg');
  element.classList.add(`${screenSize.orientation}-bg`);
};

// دالة لمراقبة تغيير حجم الشاشة وتحديث الخلفية
export const setupResponsiveBackground = (element: HTMLElement) => {
  const updateBackground = () => {
    const screenSize = getScreenSize();
    applyResponsiveBackground(element, screenSize);
  };

  // تطبيق الخلفية عند البداية
  updateBackground();

  // مراقبة تغيير حجم الشاشة
  window.addEventListener('resize', updateBackground);
  window.addEventListener('orientationchange', () => {
    // تأخير قصير للسماح للمتصفح بتحديث الأبعاد
    setTimeout(updateBackground, 100);
  });

  // إرجاع دالة لإلغاء المراقبة
  return () => {
    window.removeEventListener('resize', updateBackground);
    window.removeEventListener('orientationchange', updateBackground);
  };
};;

// Hook للعرض المتجاوب
import React, { useState, useEffect } from 'react';

export const useResponsive = () => {
  const [screenSize, setScreenSize] = useState<ScreenSize>(getScreenSize);
  const [layout, setLayout] = useState<ResponsiveLayout>(() => getResponsiveLayout(getScreenSize()));

  useEffect(() => {
    const handleResize = () => {
      const newScreenSize = getScreenSize();
      setScreenSize(newScreenSize);
      setLayout(getResponsiveLayout(newScreenSize));
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return { screenSize, layout };
};
