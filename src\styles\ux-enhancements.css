/* 🎯 تحسينات تجربة المستخدم - تفاعل وسهولة استخدام محسنة */

/* ===============================
   ✨ تأثيرات التفاعل المحسنة
   =============================== */

/* تأثيرات الهوفر للأزرار */
.fruit-button {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.fruit-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.fruit-button:hover::before {
  left: 100%;
}

.fruit-button:hover {
  transform: translate(-50%, -50%) scale(1.05) rotateY(5deg);
  box-shadow: 
    0 8px 25px rgba(0, 0, 0, 0.3),
    0 0 30px rgba(255, 215, 0, 0.6),
    inset 0 2px 4px rgba(255, 255, 255, 0.3);
}

.fruit-button:active {
  transform: translate(-50%, -50%) scale(0.95);
  transition: transform 0.1s ease;
}

/* تأثيرات أزرار المبالغ */
.amount-button {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.amount-button::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 215, 0, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
}

.amount-button:hover::after {
  width: 120%;
  height: 120%;
}

.amount-button:hover {
  transform: translate(-50%, -50%) scale(1.1);
  box-shadow: 
    0 6px 20px rgba(0, 0, 0, 0.3),
    0 0 25px rgba(255, 215, 0, 0.5);
}

.amount-button.selected {
  animation: selectedPulse 2s ease-in-out infinite;
}

@keyframes selectedPulse {
  0%, 100% {
    box-shadow: 
      0 8px 25px rgba(0, 0, 0, 0.4),
      0 0 30px rgba(255, 215, 0, 0.8),
      inset 0 0 15px rgba(255, 255, 255, 0.3);
  }
  50% {
    box-shadow: 
      0 8px 25px rgba(0, 0, 0, 0.4),
      0 0 40px rgba(255, 215, 0, 1),
      inset 0 0 20px rgba(255, 255, 255, 0.4);
  }
}

/* ===============================
   🎨 تحسينات بصرية للعناصر
   =============================== */

/* تحسين عرض الرصيد */
.balance-display {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.balance-display::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, transparent 50%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.balance-display:hover::before {
  opacity: 1;
}

.balance-display:hover {
  transform: scale(1.02);
  box-shadow: 
    0 6px 20px rgba(0, 0, 0, 0.4),
    0 0 20px rgba(255, 215, 0, 0.4);
}

/* تحسين عرض الرهان الإجمالي */
.total-bet-display {
  transition: all 0.3s ease;
  position: relative;
}

.total-bet-display:hover {
  transform: scale(1.02);
  box-shadow: 
    0 6px 20px rgba(0, 0, 0, 0.4),
    0 0 15px rgba(139, 69, 19, 0.5);
}

/* تحسين عرض مبالغ الرهانات */
.bet-display {
  transition: all 0.3s ease;
  animation: fadeInUp 0.3s ease;
}

.bet-display:hover {
  transform: translate(-50%, -50%) scale(1.1);
  box-shadow: 
    0 4px 15px rgba(0, 0, 0, 0.4),
    0 0 15px rgba(255, 215, 0, 0.6);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate(-50%, -40%);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%);
  }
}

/* ===============================
   🔔 تحسينات التنبيهات
   =============================== */

.phase-alert {
  animation: alertSlideIn 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  position: relative;
  overflow: hidden;
}

.phase-alert::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.2), transparent);
  animation: shimmer 2s infinite;
}

@keyframes alertSlideIn {
  0% {
    opacity: 0;
    transform: translate(-50%, -60%) scale(0.8);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* ===============================
   🎮 تحسينات لوحة اللعبة
   =============================== */

/* تأثير الإضاءة المحسن */
.game-square {
  transition: all 0.3s ease;
  position: relative;
}

.game-square.highlighted {
  animation: lightPulse 1s ease-in-out infinite alternate;
}

@keyframes lightPulse {
  0% {
    box-shadow: 
      0 0 20px rgba(255, 215, 0, 0.8),
      0 0 40px rgba(255, 215, 0, 0.6),
      inset 0 0 20px rgba(255, 215, 0, 0.3);
  }
  100% {
    box-shadow: 
      0 0 30px rgba(255, 215, 0, 1),
      0 0 60px rgba(255, 215, 0, 0.8),
      inset 0 0 30px rgba(255, 215, 0, 0.5);
  }
}

/* ===============================
   📱 تحسينات اللمس للجوال
   =============================== */

@media (hover: none) and (pointer: coarse) {
  /* تحسين حجم منطقة اللمس */
  .fruit-button,
  .amount-button {
    min-width: 44px;
    min-height: 44px;
    touch-action: manipulation;
  }
  
  /* تأثير اللمس */
  .fruit-button:active {
    transform: translate(-50%, -50%) scale(0.9);
    background: linear-gradient(135deg, #E6D3A3 0%, #D4A574 30%, #C2A47C 70%, #B29A5A 100%);
  }
  
  .amount-button:active {
    transform: translate(-50%, -50%) scale(0.9);
    background: linear-gradient(135deg, #E6D3A3 0%, #D4A574 100%);
  }
  
  /* تأثير الاهتزاز للتفاعل */
  .fruit-button:active,
  .amount-button:active {
    animation: tapVibrate 0.1s ease;
  }
  
  @keyframes tapVibrate {
    0%, 100% { transform: translate(-50%, -50%) scale(0.9); }
    50% { transform: translate(-50%, -50%) scale(0.85); }
  }
}

/* ===============================
   🎯 تحسينات إمكانية الوصول
   =============================== */

/* تحسين التركيز بلوحة المفاتيح */
.fruit-button:focus,
.amount-button:focus {
  outline: 3px solid rgba(255, 215, 0, 0.8);
  outline-offset: 2px;
  z-index: 100;
}

/* تحسين التباين للنصوص */
.balance-text,
.bet-text,
.phase-alert-text {
  text-shadow: 
    1px 1px 2px rgba(0, 0, 0, 0.8),
    0 0 4px rgba(0, 0, 0, 0.6);
}

/* تحسين للمستخدمين الذين يفضلون التباين العالي */
@media (prefers-contrast: high) {
  .fruit-button {
    border-width: 4px !important;
    box-shadow: 0 0 0 2px #000000 !important;
  }
  
  .amount-button {
    border-width: 3px !important;
    box-shadow: 0 0 0 1px #000000 !important;
  }
  
  .balance-text,
  .bet-text {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 1) !important;
  }
}

/* ===============================
   ⚡ تحسينات الأداء
   =============================== */

/* تحسين الرسم للعناصر المتحركة */
.fruit-button,
.amount-button,
.balance-display,
.total-bet-display,
.bet-display,
.phase-alert {
  will-change: transform, opacity;
  backface-visibility: hidden;
  perspective: 1000px;
}

/* تحسين التمرير */
.game-container {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* تحسين الذاكرة */
.fruit-button::before,
.amount-button::after,
.balance-display::before,
.phase-alert::before {
  contain: layout style paint;
}

/* ===============================
   🌙 وضع الليل (إذا كان مفضلاً)
   =============================== */

@media (prefers-color-scheme: dark) {
  .balance-display,
  .total-bet-display {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.9) 0%, rgba(30, 30, 30, 0.9) 100%);
    border-color: #FFD700;
  }
  
  .bet-display {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.95) 0%, rgba(25, 25, 25, 0.95) 100%);
  }
}

/* ===============================
   🔧 تحسينات متقدمة
   =============================== */

/* تحسين الانتقالات للعناصر الديناميكية */
.dynamic-element {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* تحسين الظلال للعمق البصري */
.elevated-element {
  box-shadow: 
    0 4px 6px rgba(0, 0, 0, 0.1),
    0 2px 4px rgba(0, 0, 0, 0.06),
    0 1px 2px rgba(0, 0, 0, 0.05);
}

/* تحسين الحدود للوضوح */
.bordered-element {
  border: 1px solid rgba(255, 215, 0, 0.3);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
}
