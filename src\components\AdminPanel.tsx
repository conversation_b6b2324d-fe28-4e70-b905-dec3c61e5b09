import React, { useState, useEffect } from 'react';
import { gameEconomics } from '../utils/economics';
import { playerTracking } from '../utils/playerTracking';

const AdminPanel: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [password, setPassword] = useState('');
  const [activeTab, setActiveTab] = useState<'login' | 'stats' | 'players' | 'settings'>('login');
  const [economicReport, setEconomicReport] = useState<any>(null);
  const [playersReport, setPlayersReport] = useState<any>(null);

  useEffect(() => {
    const updateReports = () => {
      if (isLoggedIn) {
        const econReport = gameEconomics.getEconomicReport();
        const playersRep = playerTracking.getPlayersReport();
        setEconomicReport(econReport);
        setPlayersReport(playersRep);
      }
    };

    updateReports();
    const interval = setInterval(updateReports, 5000);
    return () => clearInterval(interval);
  }, [isLoggedIn]);

  const handleLogin = () => {
    if (password === 'admin123') {
      setIsLoggedIn(true);
      setActiveTab('stats');
      setPassword('');
    } else {
      alert('كلمة المرور غير صحيحة!');
    }
  };

  const handleLogout = () => {
    setIsLoggedIn(false);
    setActiveTab('login');
  };

  if (!isVisible) {
    return (
      <div className="fixed bottom-4 left-4 z-50">
        <button
          onClick={() => setIsVisible(true)}
          className="bg-red-600 text-white px-4 py-2 rounded-lg shadow-lg hover:bg-red-700 transition-colors"
        >
          🔐 لوحة المدير
        </button>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-red-600 to-red-800 text-white p-4 flex justify-between items-center">
          <h2 className="text-xl font-bold">🔐 لوحة إدارة اللعبة</h2>
          <button
            onClick={() => setIsVisible(false)}
            className="text-white hover:text-gray-200 text-2xl"
          >
            ×
          </button>
        </div>

        {/* Content */}
        <div className="p-4">
          {!isLoggedIn ? (
            // Login Screen
            <div className="text-center">
              <h3 className="text-lg font-semibold mb-4">تسجيل دخول المدير</h3>
              <div className="max-w-sm mx-auto">
                <input
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="كلمة المرور"
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg mb-4 focus:outline-none focus:ring-2 focus:ring-red-500"
                  onKeyPress={(e) => e.key === 'Enter' && handleLogin()}
                />
                <button
                  onClick={handleLogin}
                  className="w-full bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
                >
                  تسجيل الدخول
                </button>
                <p className="text-sm text-gray-600 mt-2">كلمة المرور الافتراضية: admin123</p>
              </div>
            </div>
          ) : (
            // Admin Dashboard
            <div>
              {/* Navigation Tabs */}
              <div className="flex space-x-2 mb-4 border-b">
                {[
                  { id: 'stats', label: '📊 الإحصائيات', icon: '📊' },
                  { id: 'players', label: '👥 اللاعبون', icon: '👥' },
                  { id: 'settings', label: '⚙️ الإعدادات', icon: '⚙️' }
                ].map(tab => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`px-4 py-2 rounded-t-lg transition-colors ${
                      activeTab === tab.id 
                        ? 'bg-red-600 text-white' 
                        : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                    }`}
                  >
                    {tab.icon} {tab.label}
                  </button>
                ))}
                <button
                  onClick={handleLogout}
                  className="ml-auto px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                >
                  تسجيل الخروج
                </button>
              </div>

              {/* Tab Content */}
              <div className="min-h-64">
                {/* Statistics Tab */}
                {activeTab === 'stats' && economicReport && (
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold mb-4">📊 الإحصائيات الاقتصادية</h3>
                    
                    {/* Main Stats */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="bg-blue-50 p-4 rounded-lg">
                        <h4 className="font-semibold text-blue-800">إجمالي الرهانات</h4>
                        <div className="text-2xl font-bold text-blue-600">
                          {economicReport.totalBets.toLocaleString()}$
                        </div>
                      </div>
                      
                      <div className="bg-green-50 p-4 rounded-lg">
                        <h4 className="font-semibold text-green-800">إجمالي الأرباح</h4>
                        <div className="text-2xl font-bold text-green-600">
                          {economicReport.totalWinnings.toLocaleString()}$
                        </div>
                      </div>
                      
                      <div className="bg-purple-50 p-4 rounded-lg">
                        <h4 className="font-semibold text-purple-800">صافي الربح</h4>
                        <div className={`text-2xl font-bold ${
                          economicReport.netProfit >= 0 ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {(economicReport.netProfit >= 0 ? '+' : '') + economicReport.netProfit.toLocaleString()}$
                        </div>
                      </div>
                    </div>

                    {/* Profit Margin */}
                    <div className="bg-yellow-50 p-4 rounded-lg">
                      <h4 className="font-semibold text-yellow-800 mb-2">هامش الربح</h4>
                      <div className={`text-3xl font-bold ${
                        economicReport.profitMargin >= 0 ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {economicReport.profitMargin.toFixed(2)}%
                      </div>
                    </div>

                    {/* Today's Stats */}
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h4 className="font-semibold text-gray-800 mb-3">📅 إحصائيات اليوم</h4>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div>
                          <span className="text-gray-600">رهانات اليوم:</span>
                          <div className="font-bold text-blue-600">
                            {economicReport.todayStats.bets.toLocaleString()}$
                          </div>
                        </div>
                        <div>
                          <span className="text-gray-600">أرباح اليوم:</span>
                          <div className="font-bold text-green-600">
                            {economicReport.todayStats.winnings.toLocaleString()}$
                          </div>
                        </div>
                        <div>
                          <span className="text-gray-600">صافي اليوم:</span>
                          <div className={`font-bold ${
                            economicReport.todayStats.netProfit >= 0 ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {(economicReport.todayStats.netProfit >= 0 ? '+' : '') + economicReport.todayStats.netProfit.toLocaleString()}$
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Players Tab */}
                {activeTab === 'players' && playersReport && (
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold mb-4">👥 تقرير اللاعبين</h3>
                    
                    {/* Summary Stats */}
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                      <div className="bg-blue-50 p-4 rounded-lg text-center">
                        <div className="text-2xl font-bold text-blue-600">{playersReport.totalPlayers}</div>
                        <div className="text-sm text-blue-800">إجمالي اللاعبين</div>
                      </div>
                      
                      <div className="bg-green-50 p-4 rounded-lg text-center">
                        <div className="text-2xl font-bold text-green-600">{playersReport.trackedPlayers}</div>
                        <div className="text-sm text-green-800">تحت المراقبة</div>
                      </div>
                      
                      <div className="bg-yellow-50 p-4 rounded-lg text-center">
                        <div className="text-2xl font-bold text-yellow-600">{playersReport.highRiskPlayers}</div>
                        <div className="text-sm text-yellow-800">عاليي المخاطر</div>
                      </div>
                      
                      <div className="bg-red-50 p-4 rounded-lg text-center">
                        <div className="text-2xl font-bold text-red-600">{playersReport.suspiciousPlayers}</div>
                        <div className="text-sm text-red-800">مشبوهون</div>
                      </div>
                    </div>

                    {/* Players List */}
                    {playersReport.players.length > 0 && (
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <h4 className="font-semibold text-gray-800 mb-3">📋 قائمة اللاعبين</h4>
                        <div className="overflow-x-auto">
                          <table className="min-w-full text-sm">
                            <thead>
                              <tr className="bg-gray-100">
                                <th className="px-3 py-2 text-right">اللاعب</th>
                                <th className="px-3 py-2 text-right">الرهانات</th>
                                <th className="px-3 py-2 text-right">صافي الربح</th>
                                <th className="px-3 py-2 text-right">المخاطر</th>
                                <th className="px-3 py-2 text-right">المراقبة</th>
                              </tr>
                            </thead>
                            <tbody>
                              {playersReport.players.slice(0, 10).map((player: any, index: number) => (
                                <tr key={player.id} className="border-b hover:bg-gray-50">
                                  <td className="px-3 py-2 font-medium">{player.name}</td>
                                  <td className="px-3 py-2 text-blue-600">
                                    {player.totalBets.toLocaleString()}$
                                  </td>
                                  <td className={`px-3 py-2 font-bold ${
                                    player.netProfit >= 0 ? 'text-green-600' : 'text-red-600'
                                  }`}>
                                    {(player.netProfit >= 0 ? '+' : '') + player.netProfit.toLocaleString()}$
                                  </td>
                                  <td className="px-3 py-2">
                                    <span className={`px-2 py-1 rounded-full text-xs ${
                                      player.riskLevel === 'high' ? 'bg-red-100 text-red-600' :
                                      player.riskLevel === 'medium' ? 'bg-yellow-100 text-yellow-600' :
                                      'bg-green-100 text-green-600'
                                    }`}>
                                      {player.riskLevel === 'high' ? 'عالي' : 
                                       player.riskLevel === 'medium' ? 'متوسط' : 'منخفض'}
                                    </span>
                                  </td>
                                  <td className="px-3 py-2">
                                    <span className={`px-2 py-1 rounded text-xs ${
                                      player.isTracked ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-600'
                                    }`}>
                                      {player.isTracked ? 'نعم' : 'لا'}
                                    </span>
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                        {playersReport.players.length > 10 && (
                          <p className="text-sm text-gray-600 mt-2">
                            عرض أول 10 لاعبين من أصل {playersReport.players.length}
                          </p>
                        )}
                      </div>
                    )}
                  </div>
                )}

                {/* Settings Tab */}
                {activeTab === 'settings' && (
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold mb-4">⚙️ إعدادات النظام</h3>
                    
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <h4 className="font-semibold text-blue-800 mb-2">🔐 معلومات الأمان</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span>كلمة مرور المدير:</span>
                          <span className="font-mono">admin123</span>
                        </div>
                        <div className="flex justify-between">
                          <span>حالة تسجيل الدخول:</span>
                          <span className="text-green-600">مسجل دخول</span>
                        </div>
                        <div className="flex justify-between">
                          <span>آخر تحديث:</span>
                          <span>{new Date().toLocaleTimeString('ar-SA')}</span>
                        </div>
                      </div>
                    </div>

                    <div className="bg-green-50 p-4 rounded-lg">
                      <h4 className="font-semibold text-green-800 mb-2">✅ حالة النظام</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex items-center">
                          <span className="w-3 h-3 bg-green-500 rounded-full mr-2"></span>
                          النظام يعمل بشكل طبيعي
                        </div>
                        <div className="flex items-center">
                          <span className="w-3 h-3 bg-green-500 rounded-full mr-2"></span>
                          جميع المكونات مفعلة
                        </div>
                        <div className="flex items-center">
                          <span className="w-3 h-3 bg-green-500 rounded-full mr-2"></span>
                          البيانات محفوظة بأمان
                        </div>
                      </div>
                    </div>

                    <div className="bg-yellow-50 p-4 rounded-lg">
                      <h4 className="font-semibold text-yellow-800 mb-2">💡 نصائح للمدير</h4>
                      <div className="space-y-2 text-sm text-yellow-700">
                        <div>• راقب الإحصائيات بانتظام</div>
                        <div>• تحقق من اللاعبين عاليي المخاطر</div>
                        <div>• غيّر كلمة المرور دورياً</div>
                        <div>• احتفظ بنسخ احتياطية</div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AdminPanel; 