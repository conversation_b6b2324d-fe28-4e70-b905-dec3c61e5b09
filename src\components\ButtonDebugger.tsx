import React from 'react';
import { BetsOnTypes } from '../types/game';
import { SYMBOL_MAX_BETS, BET_AMOUNTS } from '../constants/gameConfig';

interface ButtonDebuggerProps {
  balance: number;
  betsOnTypes: BetsOnTypes;
  pendingBetsOnTypes: BetsOnTypes;
  currentBetValueToApply: number;
  isBettingPhase: boolean;
}

const ButtonDebugger: React.FC<ButtonDebuggerProps> = ({
  balance,
  betsOnTypes,
  pendingBetsOnTypes,
  currentBetValueToApply,
  isBettingPhase
}) => {
  const symbols = ['BAR', 'WATERMELON', 'LEMON', 'BANANA', 'APPLE'];

  const analyzeButton = (symbol: keyof BetsOnTypes) => {
    const confirmedBet = betsOnTypes[symbol] || 0;
    const pendingBet = pendingBetsOnTypes[symbol] || 0;
    const totalBet = confirmedBet + pendingBet;
    const maxBet = SYMBOL_MAX_BETS[symbol];
    const totalPendingBets = Object.values(pendingBetsOnTypes || {}).reduce((sum, bet) => sum + bet, 0);

    const issues = [];

    if (!isBettingPhase) {
      issues.push('❌ اللعبة ليست في مرحلة الرهان');
    }

    if (currentBetValueToApply === 0) {
      issues.push('❌ لم يتم اختيار مبلغ للرهان');
    }

    if (balance < totalPendingBets + currentBetValueToApply) {
      issues.push(`❌ الرصيد غير كافي (تحتاج ${totalPendingBets + currentBetValueToApply}, لديك ${balance})`);
    }

    if (totalBet + currentBetValueToApply > maxBet) {
      issues.push(`❌ تجاوز الحد الأقصى (${totalBet + currentBetValueToApply} > ${maxBet})`);
    }

    const canWork = isBettingPhase && 
                   currentBetValueToApply > 0 && 
                   balance >= totalPendingBets + currentBetValueToApply && 
                   totalBet + currentBetValueToApply <= maxBet;

    return {
      symbol,
      confirmedBet,
      pendingBet,
      totalBet,
      maxBet,
      canWork,
      issues
    };
  };

  const analyzeAmountButton = (amount: number) => {
    const isDisabled = !(balance >= amount && isBettingPhase);
    const issues = [];

    if (!isBettingPhase) {
      issues.push('❌ اللعبة ليست في مرحلة الرهان');
    }

    if (balance < amount) {
      issues.push(`❌ الرصيد غير كافي (تحتاج ${amount}, لديك ${balance})`);
    }

    return {
      amount,
      isDisabled,
      issues
    };
  };

  return (
    <div style={{
      position: 'fixed',
      top: '10px',
      right: '10px',
      background: 'rgba(0,0,0,0.95)',
      color: 'white',
      padding: '15px',
      borderRadius: '8px',
      fontSize: '11px',
      maxWidth: '350px',
      zIndex: 9999,
      border: '1px solid #FFD700',
      maxHeight: '80vh',
      overflow: 'auto'
    }}>
      <h3 style={{ margin: '0 0 10px 0', color: '#FFD700', fontSize: '14px' }}>🔧 تشخيص الأزرار</h3>
      
      <div style={{ marginBottom: '8px' }}>
        <strong>💰 الرصيد:</strong> {balance.toLocaleString()}
      </div>
      
      <div style={{ marginBottom: '8px' }}>
        <strong>🎯 المبلغ المختار:</strong> {currentBetValueToApply.toLocaleString()}
      </div>
      
      <div style={{ marginBottom: '8px' }}>
        <strong>⏰ مرحلة اللعبة:</strong> {isBettingPhase ? '✅ رهان' : '❌ غير رهان'}
      </div>

      <div style={{ marginBottom: '8px' }}>
        <strong>📊 الرهانات المؤقتة:</strong> {Object.values(pendingBetsOnTypes).reduce((sum, bet) => sum + bet, 0).toLocaleString()}
      </div>

      <div style={{ marginTop: '12px', marginBottom: '8px' }}>
        <strong style={{ color: '#FFD700' }}>🔢 أزرار المبالغ:</strong>
        {BET_AMOUNTS.map(amount => {
          const analysis = analyzeAmountButton(amount);
          return (
            <div key={amount} style={{ 
              margin: '3px 0', 
              padding: '3px', 
              border: analysis.isDisabled ? '1px solid #FF0000' : '1px solid #00FF00',
              borderRadius: '3px',
              fontSize: '10px'
            }}>
              <div style={{ fontWeight: 'bold', color: analysis.isDisabled ? '#FF0000' : '#00FF00' }}>
                {amount}: {analysis.isDisabled ? '❌ معطل' : '✅ يعمل'}
              </div>
              {analysis.issues.length > 0 && (
                <div style={{ fontSize: '9px', color: '#FF6B6B' }}>
                  {analysis.issues.map((issue, index) => (
                    <div key={index}>{issue}</div>
                  ))}
                </div>
              )}
            </div>
          );
        })}
      </div>

      <div style={{ marginTop: '12px' }}>
        <strong style={{ color: '#FFD700' }}>🍎 أزرار الفواكه:</strong>
        {symbols.map(symbol => {
          const analysis = analyzeButton(symbol as keyof BetsOnTypes);
          return (
            <div key={symbol} style={{ 
              margin: '3px 0', 
              padding: '3px', 
              border: analysis.canWork ? '1px solid #00FF00' : '1px solid #FF0000',
              borderRadius: '3px',
              fontSize: '10px'
            }}>
              <div style={{ fontWeight: 'bold', color: analysis.canWork ? '#00FF00' : '#FF0000' }}>
                {symbol}: {analysis.canWork ? '✅ يعمل' : '❌ لا يعمل'}
              </div>
              <div style={{ fontSize: '9px', marginTop: '1px' }}>
                الرهان: {analysis.totalBet.toLocaleString()} / {analysis.maxBet.toLocaleString()}
              </div>
              {analysis.issues.length > 0 && (
                <div style={{ fontSize: '9px', color: '#FF6B6B', marginTop: '1px' }}>
                  {analysis.issues.map((issue, index) => (
                    <div key={index}>{issue}</div>
                  ))}
                </div>
              )}
            </div>
          );
        })}
      </div>

      <div style={{ marginTop: '12px', padding: '8px', background: 'rgba(255,215,0,0.1)', borderRadius: '4px' }}>
        <strong style={{ color: '#FFD700' }}>💡 نصائح سريعة:</strong>
        <div style={{ fontSize: '9px', marginTop: '4px' }}>
          • اختر مبلغ أولاً من أزرار المبالغ
          • تأكد أن اللعبة في مرحلة الرهان
          • تحقق من كفاية الرصيد
          • لا تتجاوز الحد الأقصى لكل فاكهة
        </div>
      </div>
    </div>
  );
};

export default ButtonDebugger; 