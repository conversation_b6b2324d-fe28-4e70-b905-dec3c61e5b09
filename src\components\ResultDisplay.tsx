import React from 'react';
import { COLORS } from '../theme/colors';

interface ResultDisplayProps {
  collectedSymbols: string[];
  messages: string[];
  isVisible: boolean;
  winAmount?: number;
  onClose?: () => void;
}

const getSymbolImage = (symbol: string): string => {
  const symbolMap: { [key: string]: string } = {
    'APPLE': '/images/3.png',
    'BANANA': '/images/6.png',
    'LEMON': '/images/8.png',
    'WATERMELON': '/images/12.png',
    'BAR': 'BAR'
  };
  
  let baseSymbol = symbol;
  if (symbol.includes(' x2')) {
    baseSymbol = symbol.replace(' x2', '').trim();
  }

  const cleanSymbol = baseSymbol.toUpperCase();
  return symbolMap[cleanSymbol] || '/images/3.png';
};

const ResultDisplay: React.FC<ResultDisplayProps> = ({ 
  collectedSymbols, 
  messages, 
  isVisible,
  winAmount = 0,
  onClose
}) => {
  if (!isVisible) return null;

  return (
    <>
      {/* خلفية شفافة للصفحة بأكملها */}
      <div style={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        background: 'rgba(0, 0, 0, 0.7)',
        zIndex: 9997,
        backdropFilter: 'blur(5px)',
        pointerEvents: 'none',
      }} />

      {/* شاشة النتيجة - تظهر من أعلى يمين الصفحة */}
      <div className="w-full max-w-sm mx-auto p-3 rounded-t-xl shadow-2xl"
           style={{
             background: 'linear-gradient(135deg, rgba(45, 27, 61, 0.95) 0%, rgba(74, 14, 78, 0.95) 50%, rgba(45, 27, 61, 0.95) 100%)',
             position: 'fixed',
             bottom: '0',
             left: '50%',
             right: 'auto',
             top: 'auto',
             margin: '0',
             transform: 'translateX(-50%)',
             zIndex: 9998,
             border: '2px solid #FFD700',
             borderBottom: 'none',
             backdropFilter: 'blur(15px)',
             minHeight: '35vh',
             maxHeight: '60vh',
             overflowY: 'auto',
             pointerEvents: 'auto',
           }}>
        {/* زر إغلاق في الزاوية العلوية اليمنى */}
        <button
          onClick={() => {
            if (onClose && typeof onClose === 'function') {
              onClose();
            }
          }}
          className="absolute top-2 right-2 text-lg font-bold text-yellow-400 hover:text-yellow-300 bg-black bg-opacity-40 rounded-full w-8 h-8 flex items-center justify-center shadow-md"
          style={{ zIndex: 10000 }}
          title="إغلاق"
        >
          ×
        </button>
        
        {/* عنوان النتيجة */}
        <div className="text-center mb-3">
          <h2 className="text-lg font-bold text-yellow-400 mb-1" style={{
            textShadow: '0 2px 8px rgba(255, 215, 0, 0.5)',
            letterSpacing: '1px'
          }}>
            🎉 نتيجة الجولة
          </h2>
        </div>

        {/* الفواكه الفائزة */}
        <div className="text-center mb-3">
          <h3 className="text-xs font-bold text-white mb-2">الفواكه الفائزة:</h3>
          <div className="flex justify-center gap-1 flex-wrap">
            {collectedSymbols && collectedSymbols.length > 0 ? collectedSymbols.map((symbol, index) => {
              const baseSymbol = symbol.split(' ')[0].trim();
              const multiplierMatch = symbol.match(/x\s*(\d+)/i);
              const multiplier = multiplierMatch ? multiplierMatch[1] : null;
              
              const imageSrc = getSymbolImage(baseSymbol.toUpperCase());
              const isImage = imageSrc.startsWith('/');

              // تحديد إذا كان BAR أو بطيخ
              const isBar = baseSymbol === 'BAR' || baseSymbol === 'بار';
              const isWatermelon = baseSymbol === 'WATERMELON' || baseSymbol === 'بطيخ';

              if (isBar) {
                return (
                  <div key={index} className="flex flex-col items-center" style={{ transform: 'translateX(-6px)' }}>
                    <div style={{
                      width: '40px',
                      height: '40px',
                      background: 'linear-gradient(135deg, #2D1B3D 0%, #4A0E4E 100%)',
                      border: '1px solid #FFD700',
                      borderRadius: '6px',
                      padding: '1px 2px',
                      boxShadow: '0 1px 4px #FFD70044',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                      <span style={{
                        color: '#FFD700',
                        fontWeight: 'bold',
                        fontSize: '6px',
                        textShadow: '0 1px 2px rgba(0,0,0,0.8)',
                        lineHeight: 1.1,
                        textAlign: 'center',
                      }}>{'BAR\nBAR\nBAR'}</span>
                    </div>
                    {multiplier && (
                      <div className="mt-1 text-yellow-400 font-bold text-xs">
                        x{multiplier}
                      </div>
                    )}
                  </div>
                );
              }

              if (isImage) {
                return (
                  <div key={index} className="flex flex-col items-center" style={baseSymbol === 'WATERMELON' ? { transform: 'translateX(-6px)' } : {}}>
                    <img
                      src={imageSrc}
                      alt={baseSymbol}
                      className="w-10 h-10 object-contain"
                      style={{
                        boxShadow: '0 1px 4px rgba(139, 69, 19, 0.5)',
                        borderRadius: '4px',
                      }}
                    />
                    {multiplier && (
                      <div className="mt-1 text-yellow-400 font-bold text-xs">
                        x{multiplier}
                      </div>
                    )}
                  </div>
                );
              }

              return (
                <div key={index} className="flex flex-col items-center">
                  <div style={{
                    width: '40px',
                    height: '40px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    background: 'linear-gradient(135deg, #8B4513 0%, #A0522D 50%, #8B4513 100%)',
                    borderRadius: '6px',
                    boxShadow: '0 1px 4px rgba(139, 69, 19, 0.5)',
                    border: '1px solid #654321',
                  }}>
                    <span style={{
                      color: '#FFD700',
                      fontWeight: 'bold',
                      fontSize: '10px',
                      textShadow: '0 1px 2px rgba(0,0,0,0.8)',
                    }}>
                      {baseSymbol}
                    </span>
                  </div>
                  {multiplier && (
                    <div className="mt-1 text-yellow-400 font-bold text-xs">
                      x{multiplier}
                    </div>
                  )}
                </div>
              );
            }) : (
              <div className="text-gray-400 text-xs">
                لا توجد فواكه فائزة
              </div>
            )}
          </div>
        </div>

        {/* مجموع المبلغ العائد */}
        <div className="text-center mb-3">
          <h3 className="text-xs font-bold text-white mb-1">المبلغ العائد:</h3>
          <div className="text-lg font-bold text-green-400" style={{
            textShadow: '0 2px 8px rgba(34, 197, 94, 0.5)',
            letterSpacing: '1px'
          }}>
            {winAmount > 0 ? `${winAmount.toLocaleString()} ريال` : '0 ريال'}
          </div>
        </div>

        {/* أعلى ثلاث نتائج */}
        <div className="text-center mb-3">
          <h3 className="text-xs font-bold text-white mb-2">🏆 أعلى النتائج:</h3>
          <div className="grid grid-cols-3 gap-3 max-w-xs mx-auto">
            {/* المركز الأول */}
            <div className="bg-gradient-to-br from-yellow-400 to-yellow-600 p-1 rounded">
              <div className="text-sm mb-1">🥇</div>
              <div className="font-bold text-black text-xs">أحمد</div>
              <div className="text-black text-xs">15,000</div>
            </div>
            {/* المركز الثاني */}
            <div className="bg-gradient-to-br from-gray-300 to-gray-500 p-1 rounded">
              <div className="text-sm mb-1">🥈</div>
              <div className="font-bold text-black text-xs">فاطمة</div>
              <div className="text-black text-xs">12,500</div>
            </div>
            {/* المركز الثالث */}
            <div className="bg-gradient-to-br from-yellow-600 to-yellow-800 p-1 rounded">
              <div className="text-sm mb-1">🥉</div>
              <div className="font-bold text-white text-xs">محمد</div>
              <div className="text-white text-xs">10,000</div>
            </div>
          </div>
        </div>

        {/* رسالة للاعب الذي لم يشارك */}
        <div className="text-center mb-3">
          <div className="p-2 rounded border"
               style={{
                 background: 'linear-gradient(135deg, rgba(139, 69, 19, 0.3) 0%, rgba(160, 82, 45, 0.3) 100%)',
                 border: '1px solid rgba(255, 215, 0, 0.4)'
               }}>
            <div className="text-yellow-300 font-bold text-xs mb-1">⚠️ تنبيه</div>
            <div className="text-yellow-200 text-xs">
              لم تشارك في هذه الجولة. شارك في الجولة القادمة للفوز!
            </div>
          </div>
        </div>

        {/* زر إغلاق في الأسفل */}
        <div className="text-center">
          <button
            onClick={() => {
              if (onClose && typeof onClose === 'function') {
                onClose();
              }
            }}
            className="bg-gradient-to-r from-yellow-400 to-yellow-600 text-black font-bold py-1.5 px-4 rounded-full hover:from-yellow-500 hover:to-yellow-700 transition-all duration-300 transform hover:scale-105 shadow-lg text-xs"
            style={{
              boxShadow: '0 2px 10px rgba(255, 215, 0, 0.4)',
            }}
          >
            إغلاق النتيجة
          </button>
        </div>
      </div>
    </>
  );
};

export default ResultDisplay;