import React, { useState, useEffect, useCallback, useRef } from 'react';
import './styles/gameButtons.css';
import GameBoard from './components/GameBoard';
import BettingControls from './components/BettingControlsSimple';
import BettingAmountControls from './components/BettingAmountControls';
import MultiDeviceBackground from './components/MultiDeviceBackground';
import { BUTTON_POSITIONS } from './utils/buttonPositions';
import GameHistory from './components/GameHistory';
import HistoryBox from './components/HistoryBox';

import {
  BETTING_PHASE_DURATION,
  LIGHT_ANIMATION_DURATION,
  RESULT_DISPLAY_DURATION,
  TOTAL_ROUND_DURATION,
  STARTING_BALANCE,
  ACTIVE_GAME_SQUARES,
  SYMBOL_MAX_BETS
} from './constants/gameConfig';
import {
  calculateWin,
  generateLightSequence,
  determineFinalSelection,
  calculateLightSpeed,
  generateLuckyAnimation,
  calculateLuckyMultiWin
} from './utils/gameLogicNew';

// نوع بيانات المشاركة
interface PlayerBetLog {
  time: number; // timestamp
  bet: number;
  result: number;
}

const App: React.FC = () => {
  const [gameState, setGameState] = useState<any>({
    balance: STARTING_BALANCE,
    betsOnTypes: { 'APPLE': 0, 'BANANA': 0, 'LEMON': 0, 'WATERMELON': 0, 'BAR': 0 },
    isSpinning: false,
    countdown: BETTING_PHASE_DURATION,
    selectedSquares: [],
    currentBetValueToApply: 0,
    collectedSymbols: [],
    messages: [],
    totalWinAmount: 0,
    // New 24/7 system states
    gamePhase: 'betting',
    lightPosition: -1,
    lightPath: ACTIVE_GAME_SQUARES.map(square => square.gridIndex),
    isLightAnimating: false,
    roundStartTime: Date.now(),
    lightPositions: [], // <-- أضف هذا المتغير
  });
  // متغير جديد لتخزين رهانات اللاعب المؤقتة قبل التأكيد
  const [pendingBetsOnTypes, setPendingBetsOnTypes] = useState<any>({ 'APPLE': 0, 'BANANA': 0, 'LEMON': 0, 'WATERMELON': 0, 'BAR': 0 });
  
  // تخزين آخر 20 لعبة
  const [gameHistory, setGameHistory] = useState<string[][]>([
    ['APPLE x2', 'BANANA', 'LEMON x3', 'BAR', 'WATERMELON'],
    ['BANANA', 'APPLE', 'LEMON', 'BAR x2', 'WATERMELON x2'],
    ['LEMON', 'BANANA', 'APPLE', 'BAR', 'WATERMELON x3']
  ]);

  const [lightAnimationInterval, setLightAnimationInterval] = useState<number | null>(null);

  // أضف متغيرات حالة لإدارة ظهور البطاقة ونتيجتها
  const [showResultCard, setShowResultCard] = useState(false);
  const [didParticipate, setDidParticipate] = useState(false);
  const [lastResultMessages, setLastResultMessages] = useState<string[]>([]);

  // متغيرات حالة لرسالة التنبيه
  const [showPhaseAlert, setShowPhaseAlert] = useState(false);
  const [phaseAlertMessage, setPhaseAlertMessage] = useState('');

  // حالة ظهور رسالة المساعدة
  const [showHelp, setShowHelp] = useState(false);

  // حالتا إظهار صناديق النتائج
  const [showPlayerHistory, setShowPlayerHistory] = useState(false);
  const [showGameHistory, setShowGameHistory] = useState(false);
  const [showMedals, setShowMedals] = useState(false);

  // حالة تحديد مربعات الفاكهة
  const [selectedSquares, setSelectedSquares] = useState<number[]>([]);

  // سجل مراهنات اللاعب
  const [playerBetsLog, setPlayerBetsLog] = useState<PlayerBetLog[]>([]);
  // حالة إظهار النافذة المنبثقة
  const [showPlayerBetsPopup, setShowPlayerBetsPopup] = useState(false);

  // دالة لتسجيل المراهنة بعد كل جولة
  const logPlayerBet = useCallback((bet: number, result: number) => {
    setPlayerBetsLog(prev => [
      { time: Date.now(), bet, result },
      ...prev.filter(entry => Date.now() - entry.time < 60 * 60 * 1000) // احتفظ فقط بمراهنات الساعة الحالية
    ].slice(0, 20)); // احتفظ بآخر 20 فقط
  }, []);

  // دالة جلب آخر 10 مراهنات خلال الساعة الحالية (مستقبلاً يمكن تعديلها لجلب البيانات من قاعدة بيانات)
  const fetchLastPlayerBets = useCallback(() => {
    // مستقبلاً: إذا كان هناك API أو قاعدة بيانات، استبدل هذا المنطق
    const now = Date.now();
    return playerBetsLog.filter(entry => now - entry.time < 60 * 60 * 1000).slice(0, 10);
  }, [playerBetsLog]);

  // Ref to store the last second the sound was played
  const lastCountdownRef = useRef<number | null>(null);
  // Ref for the audio element
  const countdownAudioRef = useRef<HTMLAudioElement | null>(null);

  // حالة كتم الصوت
  const [isMuted, setIsMuted] = useState(false);
  const [showHelpPopup, setShowHelpPopup] = useState(false);
  const [showTop3Popup, setShowTop3Popup] = useState(false);

  // حالة وضع التعديل البسيط
  const [showSimpleEditMode, setShowSimpleEditMode] = useState(false);

  // حالة أداة تحريك الأزرار البسيطة
  const [showEasyMover, setShowEasyMover] = useState(false);

  // 24/7 Game Timer System
  const updateGameTimer = useCallback(() => {
    setGameState(prev => {
      const elapsedTime = Date.now() - prev.roundStartTime;
      const remainingTime = TOTAL_ROUND_DURATION * 1000 - elapsedTime;
      
      if (remainingTime <= 0) {
        // Start new round - always start with betting phase
        // مسح الرهانات المعلقة والرهانات المؤكدة من الجولة السابقة
        setPendingBetsOnTypes({ 'APPLE': 0, 'BANANA': 0, 'LEMON': 0, 'WATERMELON': 0, 'BAR': 0 });

        // مسح الرهانات المؤكدة أيضاً لبداية جولة جديدة
        setGameState(prevState => ({
          ...prevState,
          betsOnTypes: { 'APPLE': 0, 'BANANA': 0, 'LEMON': 0, 'WATERMELON': 0, 'BAR': 0 },
          lightPositions: [], // <-- إعادة تعيين المربعات المضيئة
        }));

        // رسالة بداية جولة جديدة
        setTimeout(() => {
          setPhaseAlertMessage('🔥 جولة جديدة! ضع رهاناتك الآن');
          setShowPhaseAlert(true);
          setTimeout(() => setShowPhaseAlert(false), 2500);
        }, 500);

        return {
          ...prev,
          roundStartTime: Date.now(),
          gamePhase: 'betting',
          countdown: BETTING_PHASE_DURATION,
          isLightAnimating: false,
          lightPosition: -1,
          selectedSquares: [],
          collectedSymbols: [],
          messages: [],
          totalWinAmount: 0,

          // لا نعيد تعيين currentBetValueToApply
        };
      }
      
      // Handle phase transitions and countdown display
      if (prev.gamePhase === 'betting') {
        const bettingTimeLeft = remainingTime - (LIGHT_ANIMATION_DURATION + RESULT_DISPLAY_DURATION) * 1000;
        
        if (bettingTimeLeft <= 0) {
          // Start light animation phase
          // مسح الرهانات المؤقتة فقط (الرهانات المؤكدة تبقى)
          setPendingBetsOnTypes({ 'APPLE': 0, 'BANANA': 0, 'LEMON': 0, 'WATERMELON': 0, 'BAR': 0 });

          // رسالة بداية الدوران
          setPhaseAlertMessage('🎰 بدء الدوران... حظ سعيد!');
          setShowPhaseAlert(true);
          setTimeout(() => setShowPhaseAlert(false), 2000);

          startLightAnimation();
          return {
            ...prev,
            gamePhase: 'light_animation',
            countdown: 0, // Show 00 during light animation
            isLightAnimating: true
          };
        }
        
        return {
          ...prev,
          countdown: Math.ceil(bettingTimeLeft / 1000)
        };
      }
      
      if (prev.gamePhase === 'light_animation') {
        if (remainingTime <= RESULT_DISPLAY_DURATION * 1000) {
          // Start result display phase
          return {
            ...prev,
            gamePhase: 'result_display',
            countdown: 0, // Show 00 during result display
            isLightAnimating: false
          };
        }
        
        return {
          ...prev,
          countdown: 0 // Keep showing 00 during light animation
        };
      }
      
      // Result display phase
      return {
        ...prev,
        countdown: 0 // Show 00 during result display
      };
    });
  }, []);

  // Light Animation System with variable speed
  const startLightAnimation = useCallback(() => {
    if (lightAnimationInterval) clearInterval(lightAnimationInterval);

    // اختيار موقع عشوائي للعبة الفارغة
    const randomPosition = Math.floor(Math.random() * 16);

    // التحقق من نوع المربع الفائز
    const squareIndex = ACTIVE_GAME_SQUARES.findIndex(sq => sq.gridIndex === randomPosition);
    const winningSquare = ACTIVE_GAME_SQUARES[squareIndex];

    // إذا كان المربع الفائز لاكي، استخدم الحركة المتعددة
    if (winningSquare && (winningSquare.type === 'luckyDoubleText' || winningSquare.type === 'luckyTripleText')) {
      // console.log(`🍀 LUCKY detected: ${winningSquare.type} - starting lucky animation`);
      startLuckyMultiAnimation(winningSquare.type);
      return;
    }

    // الحركة العادية للمربعات الأخرى
          // console.log(`🎯 Normal animation for: ${winningSquare?.symbol} (${winningSquare?.type})`);

    // بدء من الموقع الحالي للضوء (إذا كان موجوداً)
    const currentLightPosition = gameState.lightPosition >= 0 ? gameState.lightPosition : undefined;
    const lightSequence = generateLightSequence(randomPosition, currentLightPosition);
    let currentIndex = 0;
    
    const animateStep = () => {
      if (currentIndex >= lightSequence.length) {
        // Animation complete, determine final selection
        const finalLightPosition = lightSequence[lightSequence.length - 1];
        const finalSelectedIndices = determineFinalSelection(finalLightPosition);
        
        // Calculate win based on final selection
        setGameState(prev => {
          const result = calculateWin(finalSelectedIndices, prev.betsOnTypes);
          
          // Update game history
          setGameHistory(historyPrev => {
            const newHistory = [result.collectedSymbols, ...historyPrev];
            return newHistory.slice(0, 20);
          });
          
          // عرض النتائج فوراً عند توقف الإضاءة
          const didParticipate = Object.values(prev.betsOnTypes).some(bet => bet > 0);
          if (didParticipate) {
            setShowResultCard(true);
            setDidParticipate(true);
            setLastResultMessages(result.messages);
          }

          return {
            ...prev,
            selectedSquares: finalSelectedIndices.map(i => ACTIVE_GAME_SQUARES[i].gridIndex),
            collectedSymbols: result.collectedSymbols,
            messages: result.messages,
            totalWinAmount: result.totalWinAmount,
            balance: prev.balance + result.totalWinAmount,
            lightPosition: finalLightPosition,
            isLightAnimating: false,
            gamePhase: 'result_display' // تغيير المرحلة فوراً لعرض النتائج
          };
        });
        
        setLightAnimationInterval(null);
        return;
      }
      
      setGameState(prev => ({
        ...prev,
        lightPosition: lightSequence[currentIndex]
      }));
      
      currentIndex++;
      
      // Calculate next speed and schedule next step with smooth transition
      const nextSpeed = calculateLightSpeed(currentIndex, lightSequence.length);
      const smoothSpeed = Math.max(nextSpeed, 50); // ضمان حد أدنى للسلاسة
      const nextTimeout = setTimeout(animateStep, smoothSpeed);
      setLightAnimationInterval(nextTimeout);
    };
    
    // Start the animation
    animateStep();
  }, [lightAnimationInterval]);

  // دالة الحركة المتعددة للاكي
  const startLuckyMultiAnimation = useCallback((luckyType: 'luckyDoubleText' | 'luckyTripleText') => {
    const { sequences, finalPositions } = generateLuckyAnimation(luckyType);
    let currentSequenceIndex = 0;
    let currentStepIndex = 0;

    const animateLuckyStep = () => {
      if (currentSequenceIndex >= sequences.length) {
        // انتهاء جميع الحركات، حساب النتيجة النهائية
        setGameState(prev => {
          const result = calculateLuckyMultiWin(finalPositions, prev.betsOnTypes);

          // Update game history - إضافة النتائج كمجموعة واحدة
          setGameHistory(historyPrev => {
            const newHistory = [result.collectedSymbols, ...historyPrev];
            return newHistory.slice(0, 20);
          });

          // لا تعرض بطاقة النتيجة هنا، فقط بعد تغيير المرحلة في useEffect
          const didParticipate = Object.values(prev.betsOnTypes).some((bet: any) => bet > 0);
          if (didParticipate) {
            setDidParticipate(true);
            setLastResultMessages(result.messages);
          }

          return {
            ...prev,
            selectedSquares: finalPositions,
            collectedSymbols: result.collectedSymbols,
            messages: result.messages,
            totalWinAmount: result.totalWinAmount,
            balance: prev.balance + result.totalWinAmount,
            lightPosition: finalPositions[finalPositions.length - 1],
            lightPositions: finalPositions, // <-- أضف هذا السطر
            isLightAnimating: false,
            gamePhase: 'result_display'
          };
        });

        setLightAnimationInterval(null);
        return;
      }

      const currentSequence = sequences[currentSequenceIndex];

      if (currentStepIndex >= currentSequence.length) {
        // انتهاء الحركة الحالية، الانتقال للحركة التالية
        currentSequenceIndex++;
        currentStepIndex = 0;

        // تأخير قصير بين الحركات
        const nextTimeout = setTimeout(animateLuckyStep, 500);
        setLightAnimationInterval(nextTimeout);
        return;
      }

      // تحديث موقع الضوء
      setGameState(prev => ({
        ...prev,
        lightPosition: currentSequence[currentStepIndex]
      }));

      currentStepIndex++;

      // حساب السرعة مع تحسين السلاسة
      const nextSpeed = calculateLightSpeed(currentStepIndex, currentSequence.length);
      const smoothDelay = Math.max(nextSpeed, 50); // حد أدنى أقل للسلاسة

      const nextTimeout = setTimeout(animateLuckyStep, smoothDelay);
      setLightAnimationInterval(nextTimeout);
    };

    animateLuckyStep();
  }, [lightAnimationInterval]);

  const handleBetValueSelect = (value: number) => {
    // console.log('🎯 handleBetValueSelect: تحديث المبلغ إلى', value);
    // console.log('🔍 قبل التحديث: currentBetValueToApply =', gameState.currentBetValueToApply);
    
    setGameState(prev => {
              // console.log('✅ تم تحديث currentBetValueToApply من', prev.currentBetValueToApply, 'إلى', value);
      return { ...prev, currentBetValueToApply: value };
    });
    
    // تأكيد التحديث بعد فترة قصيرة
    setTimeout(() => {
      // console.log('🔍 بعد التحديث: currentBetValueToApply =', gameState.currentBetValueToApply);
    }, 100);
  };

  const handleSymbolBet = (symbol: keyof typeof SYMBOL_MAX_BETS) => {
    // التحقق من صحة الرهان
    if (gameState.currentBetValueToApply === 0) {
      return;
    }

    if (gameState.balance < gameState.currentBetValueToApply) {
      return;
    }

    // يسمح بالرهان فقط خلال فترة المراهنة
    if (gameState.gamePhase !== 'betting') {
      setPhaseAlertMessage('الجولة بدأت، انتظر الجولة القادمة');
      setShowPhaseAlert(true);
      setTimeout(() => setShowPhaseAlert(false), 2000);
      return;
    }

    // التحقق من الحد الأقصى للرهان على هذا الرمز
    const currentBet = pendingBetsOnTypes[symbol] || 0;
    const maxBet = SYMBOL_MAX_BETS[symbol];
    if (currentBet + gameState.currentBetValueToApply > maxBet) {
      setPhaseAlertMessage(`الحد الأقصى للرهان على ${symbol} هو ${maxBet.toLocaleString()}`);
      setShowPhaseAlert(true);
      setTimeout(() => setShowPhaseAlert(false), 2000);
      return;
    }

    setPendingBetsOnTypes(prev => ({
      ...prev,
      [symbol]: prev[symbol] + gameState.currentBetValueToApply
    }));
  };

  const handleSpin = () => {
    // يسمح بتأكيد الرهان فقط خلال فترة المراهنة
    if (gameState.gamePhase !== 'betting') {
      return;
    }

    // حساب مجموع الرهانات المعلقة
    const totalPendingBet = Object.values(pendingBetsOnTypes).reduce((sum: number, bet: any) => sum + bet, 0);

    if (totalPendingBet === 0) {
      setPhaseAlertMessage('ضع رهانات أولاً ثم اضغط تأكيد');
      setShowPhaseAlert(true);
      setTimeout(() => setShowPhaseAlert(false), 2000);
      return;
    }

    if (gameState.balance < totalPendingBet) {
      setPhaseAlertMessage(`الرصيد غير كافي. تحتاج ${totalPendingBet.toLocaleString()} ولديك ${gameState.balance.toLocaleString()}`);
      setShowPhaseAlert(true);
      setTimeout(() => setShowPhaseAlert(false), 3000);
      return;
    }

    // إضافة الرهانات المؤقتة إلى الرهانات المؤكدة (لا نستبدل، بل نضيف)
    setGameState(prev => ({
      ...prev,
                  betsOnTypes: {
              'APPLE': prev.betsOnTypes['APPLE'] + pendingBetsOnTypes['APPLE'],
              'BANANA': prev.betsOnTypes['BANANA'] + pendingBetsOnTypes['BANANA'],
              'LEMON': prev.betsOnTypes['LEMON'] + pendingBetsOnTypes['LEMON'],
              'WATERMELON': prev.betsOnTypes['WATERMELON'] + pendingBetsOnTypes['WATERMELON'],
              'BAR': prev.betsOnTypes['BAR'] + pendingBetsOnTypes['BAR']
            },
      balance: prev.balance - totalPendingBet
    }));
                setPendingBetsOnTypes({ 'APPLE': 0, 'BANANA': 0, 'LEMON': 0, 'WATERMELON': 0, 'BAR': 0 });

    // حساب المجموع الإجمالي للرهانات بعد الإضافة
    const newTotalBets =
      (gameState.betsOnTypes['APPLE'] + pendingBetsOnTypes['APPLE']) +
      (gameState.betsOnTypes['BANANA'] + pendingBetsOnTypes['BANANA']) +
      (gameState.betsOnTypes['LEMON'] + pendingBetsOnTypes['LEMON']) +
      (gameState.betsOnTypes['WATERMELON'] + pendingBetsOnTypes['WATERMELON']) +
      (gameState.betsOnTypes['BAR'] + pendingBetsOnTypes['BAR']);

    // رسالة نجاح تظهر الرهان الجديد والمجموع الإجمالي
    const currentTotalBets = Object.values(gameState.betsOnTypes).reduce((sum: number, bet: any) => sum + bet, 0);

    if (currentTotalBets > 0) {
      // إذا كان هناك رهانات سابقة
      setPhaseAlertMessage(`تم إضافة ${totalPendingBet.toLocaleString()} للرهانات. المجموع الإجمالي: ${newTotalBets.toLocaleString()}`);
    } else {
      // إذا كان هذا أول رهان
      setPhaseAlertMessage(`تم تأكيد الرهان بمبلغ ${totalPendingBet.toLocaleString()}. حظ سعيد!`);
    }

    setShowPhaseAlert(true);
    setTimeout(() => setShowPhaseAlert(false), 3000);
  };

  // Start light animation on component mount
  useEffect(() => {
    const timer = setTimeout(() => {
      startLightAnimation();
    }, 100);
    
    return () => clearTimeout(timer);
  }, []);

  // Initialize 24/7 game timer
  useEffect(() => {
    const interval = setInterval(updateGameTimer, 100); // Update every 100ms for smooth timer

    return () => {
      if (interval) clearInterval(interval);
      if (lightAnimationInterval) clearInterval(lightAnimationInterval);
    };
  }, [updateGameTimer, lightAnimationInterval]);

  // عند تحميل الصفحة: اقرأ الرصيد من localStorage أو استخدم القيمة الابتدائية
  useEffect(() => {
    const savedBalance = localStorage.getItem('player_balance');
    if (savedBalance) {
      setGameState(prev => ({ ...prev, balance: parseInt(savedBalance, 10) }));
    }
  }, []);

  // عند كل تغيير في الرصيد: خزّنه في localStorage
  useEffect(() => {
    localStorage.setItem('player_balance', gameState.balance.toString());
  }, [gameState.balance]);

  // منطق إظهار البطاقة بعد كل جولة
  useEffect(() => {
    if (gameState.gamePhase === 'result_display') {
      // إذا كان هناك رهان مؤكد في الجولة الماضية
      const didPlayerParticipate = Object.values(gameState.betsOnTypes).some(bet => bet > 0);
      setDidParticipate(didPlayerParticipate);
      setLastResultMessages(gameState.messages);
      setShowResultCard(true);
    }
  }, [gameState.gamePhase]);

  // إخفاء البطاقة عند بدء جولة جديدة
  useEffect(() => {
    if (gameState.gamePhase === 'betting') {
      setShowResultCard(false);
    }
  }, [gameState.gamePhase]);

  // تشغيل صوت beep في آخر 5 ثواني من العد التنازلي في مرحلة المراهنة
  useEffect(() => {
    if (gameState.gamePhase === 'betting' && gameState.countdown > 0 && gameState.countdown <= 5) {
      if (lastCountdownRef.current !== gameState.countdown) {
        // playBeep(); // This function was removed, so this line is commented out or removed if not used elsewhere.
        lastCountdownRef.current = gameState.countdown;
      }
    } else {
      lastCountdownRef.current = null;
    }
  }, [gameState.countdown, gameState.gamePhase]);

  const totalBet = Object.values(gameState.betsOnTypes).reduce((sum: number, bet: any) => sum + bet, 0);

  // تحديد الجهاز الحالي ديناميكيًا بناءً على أبعاد الشاشة
  const width = typeof window !== 'undefined' ? window.innerWidth : 428;
  const height = typeof window !== 'undefined' ? window.innerHeight : 926;
  
  // البحث عن أقرب تكوين جهاز بفارق ±50 بيكسل
  let deviceConfig = BUTTON_POSITIONS.find(d => {
    const widthMatch = Math.abs(d.device.width - width) < 50;
    const heightMatch = Math.abs(d.device.height - height) < 50;
    return widthMatch && heightMatch;
  });
  
  // إذا لم نجد تطابق قريب، استخدم التكوين الأول كافتراضي
  if (!deviceConfig) {
    deviceConfig = BUTTON_POSITIONS[0];
  }
  const gameBoardStyle = deviceConfig.gameBoard
    ? {
    ...deviceConfig.gameBoard,
    position: deviceConfig.gameBoard.position as React.CSSProperties['position'] || 'absolute',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    // حدود مخفية - تم الانتهاء من الضبط
    border: 'none',
    borderRadius: '8px',
    backgroundColor: 'transparent',
    boxShadow: 'none',
    }
    : {
        position: 'absolute' as const,
        top: '32%', // رفعته من 37.5% إلى 32%
        left: '50%',
        transform: 'translate(-50%, -50%)',
        zIndex: 10,
        width: 'min(518px, 90vw)', // العرض المحدد: 518px
        height: 'min(450px, 80vh)', // الارتفاع أكثر: 450px
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        // حدود مخفية - تم الانتهاء من الضبط
        border: 'none',
        borderRadius: '8px',
        backgroundColor: 'rgba(255,0,0,0.2)', // لون خلفية مؤقت أحمر شفاف
        boxShadow: 'none',
      };
  


  // دالة لحساب أعلى ثلاث مراهنين
  const getTopThreePlayers = () => {
    // بيانات تجريبية: استخرج من playerBetsLog (أو منطق حقيقي لاحقاً)
    // كل عنصر: { name: string, amount: number }
    // سنعتبر أن اسم اللاعب الحالي هو "أنت"
    const currentPlayerName = 'أنت';
    // جمع المبالغ حسب الاسم (هنا فقط اللاعب الحالي)
    const playerTotal = playerBetsLog.reduce((sum, entry) => sum + (entry.result > 0 ? entry.result : 0), 0);
    // بيانات ثابتة للاعبين الآخرين
    const others = [
      { name: 'أحمد', amount: 12500 },
      { name: 'سارة', amount: 10800 },
      { name: 'خالد', amount: 9200 },
    ];
    // أضف اللاعب الحالي إذا كان من ضمن الأعلى
    let all = [...others];
    if (playerTotal > 0) {
      all.push({ name: currentPlayerName, amount: playerTotal });
    }
    // رتب حسب المبلغ تنازلياً
    all = all.sort((a, b) => b.amount - a.amount);
    // خذ الثلاثة الأعلى فقط
    const topThree = all.slice(0, 3);
    return topThree;
  };

  return (
    <div
      style={{
        width: '100vw',
        height: '100vh',
        minHeight: '100dvh',
        minWidth: '100vw',
        overflow: 'hidden',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'relative',
        background: '#000',
      }}
    >
      {/* الخلفية الذكية المتجاوبة */}
      <MultiDeviceBackground 
        className="responsive-background" 
      />

      {/* زر كتم الصوت - مربع صغير أسفل زر الرهان على اليمين */}
      <div
        style={{
          position: 'absolute',
          left: '93%',
          top: 'calc(75% + 50px)', // رفع أكثر للأعلى
          transform: 'translate(-50%, -50%)',
          zIndex: 20001,
          background: 'linear-gradient(135deg, #4E342E 0%, #2D1B12 100%)', // خلفية خشبية غامقة
          borderRadius: 6,
          width: 32,
          height: 32,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          boxShadow: '0 2px 8px #0002',
          cursor: 'pointer',
          border: 'none',
          transition: 'border 0.2s',
          opacity: 1, // إظهار زر كتم الصوت
        }}
        title={isMuted ? 'إلغاء كتم الصوت' : 'كتم الصوت'}
        onClick={() => setIsMuted(m => !m)}
      >
        <span style={{ fontSize: 22, color: isMuted ? '#d32f2f' : '#FFD700' }}>
          {isMuted ? '🔇' : '🔊'}
        </span>
      </div>

      {/* زر تعليمات (استفهام) - مربع صغير أسفل زر 5000 */}
      <div
        style={{
          position: 'absolute',
          left: '7%',
          top: 'calc(75% + 60px)', // رفع أكثر للأعلى
          transform: 'translate(-50%, -50%)',
          zIndex: 20001,
          background: 'linear-gradient(135deg, #1976d2 0%, #0d47a1 100%)', // خلفية زرقاء
          borderRadius: 6,
          width: 36,
          height: 36,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          boxShadow: '0 2px 8px #0002',
          cursor: 'pointer',
          border: 'none',
          opacity: 0, // إخفاء زر التعليمات مع الحفاظ على النقر
        }}
        title="تعليمات اللعبة"
        onClick={() => setShowHelpPopup(true)}
      >
        <span style={{ fontSize: 22, color: '#ffffff', fontWeight: 900 }}>؟</span>
      </div>

      {/* نافذة منبثقة للتعليمات */}
      {showHelpPopup && (
        <div
          style={{
            position: 'fixed',
            left: '50%',
            top: '50%',
            transform: 'translate(-50%, -50%)',
            background: '#FFF8E1',
            color: '#222',
            borderRadius: 18,
            border: '4px solid #1976d2',
            boxShadow: '0 6px 32px #0004',
            zIndex: 30000,
            minWidth: 320,
            maxWidth: '90vw',
            minHeight: 180,
            padding: '32px 24px',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <button
            onClick={() => setShowHelpPopup(false)}
            style={{ position: 'absolute', top: 10, right: 18, background: 'none', border: 'none', color: '#d32f2f', fontSize: 28, cursor: 'pointer', fontWeight: 900, lineHeight: 1 }}
            title="إغلاق"
          >×</button>
          <div style={{ fontWeight: 700, fontSize: 20, marginBottom: 18, color: '#1976d2', letterSpacing: 1, textAlign: 'center' }}>تعليمات اللعبة</div>
          <div style={{ fontSize: 16, lineHeight: 1.7, textAlign: 'right', maxWidth: 400 }}>
            <ul style={{ paddingRight: 18, margin: 0 }}>
              <li>اختر مبلغ الرهان من الأزرار أسفل اللعبة.</li>
              <li>اضغط على رموز الفواكه لوضع الرهان عليها.</li>
              <li>اضغط زر <b>الرهان</b> أو <b>تأكيد</b> لبدء الجولة.</li>
              <li>انتظر حتى تظهر النتائج في شريط النتائج أسفل الشاشة.</li>
              <li>يمكنك مراجعة آخر مشاركاتك من المربع الصغير أسفل زر الرهان.</li>
              <li>استخدم زر كتم الصوت أو زر التعليمات حسب الحاجة.</li>
            </ul>
          </div>
        </div>
      )}

      {/* مربع صغير جدًا بجانب زر التعليمات */}
      <div
        style={{
          position: 'absolute',
          left: '19%',
          top: 'calc(75% + 60px)', // رفع أكثر للأعلى
          transform: 'translate(-50%, -50%)',
          zIndex: 20001,
          background: 'linear-gradient(135deg, #FFD700 0%, #FFA000 100%)', // خلفية ذهبية
          borderRadius: 8,
          width: 36,
          height: 36,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          boxShadow: '0 2px 8px #0002',
          cursor: 'pointer',
          border: 'none',
          opacity: 0, // إخفاء زر الترتيب الشهري مع الحفاظ على النقر
        }}
        title="عرض المراكز الثلاثة الأولى شهريًا"
        onClick={() => setShowTop3Popup(true)}
      >
        <span style={{ fontSize: 18, color: '#000000', fontWeight: 900 }}>🏆</span>
      </div>
      {/* نافذة منبثقة للمراكز الثلاثة الأولى شهريًا */}
      {showTop3Popup && (
        <div
          style={{
            position: 'fixed',
            left: '50%',
            top: '50%',
            transform: 'translate(-50%, -50%)',
            background: 'linear-gradient(135deg, #6D4C41 0%, #3E2723 100%)',
            color: '#fff',
            borderRadius: 18,
            border: '4px solid #FFD700',
            boxShadow: '0 6px 32px #0004',
            zIndex: 30000,
            minWidth: 320,
            maxWidth: '90vw',
            minHeight: 180,
            padding: '32px 24px',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <button
            onClick={() => setShowTop3Popup(false)}
            style={{ position: 'absolute', top: 10, right: 18, background: 'none', border: 'none', color: '#d32f2f', fontSize: 28, cursor: 'pointer', fontWeight: 900, lineHeight: 1 }}
            title="إغلاق"
          >×</button>
          <div style={{ fontWeight: 700, fontSize: 20, marginBottom: 18, color: '#FFD700', letterSpacing: 1, textAlign: 'center' }}>المراكز الثلاثة الأولى شهريًا</div>
          <div style={{ width: '100%', fontSize: 17 }}>
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: 10 }}>
              <span style={{ width: 22, fontWeight: 900, color: '#FFD700', fontSize: 19, textAlign: 'center' }}>1</span>
              <span><b>أحمد علي</b> — 12,500 نقطة</span>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: 10 }}>
              <span style={{ width: 22, fontWeight: 900, color: '#C0C0C0', fontSize: 19, textAlign: 'center' }}>2</span>
              <span><b>سارة محمد</b> — 10,800 نقطة</span>
            </div>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <span style={{ width: 22, fontWeight: 900, color: '#CD7F32', fontSize: 19, textAlign: 'center' }}>3</span>
              <span><b>خالد يوسف</b> — 9,200 نقطة</span>
            </div>
          </div>
        </div>
      )}

      {/* حاوية مربعات اللعبة - موضوعة في المنتصف */}
      <div style={gameBoardStyle}>
        <GameBoard
          countdown={gameState.countdown}
          selectedSquares={selectedSquares}
          setSelectedSquares={setSelectedSquares}
          isSpinning={gameState.isSpinning}
          gamePhase={gameState.gamePhase}
          lightPosition={gameState.lightPosition}
          isLightAnimating={gameState.isLightAnimating}
          lightPositions={gameState.gamePhase === 'result_display' && gameState.lightPositions && gameState.lightPositions.length > 0 ? gameState.lightPositions : undefined}
        />
      </div>

      {/* أزرار مبالغ الرهان */}
      <BettingAmountControls
        currentBetValueToApply={gameState.currentBetValueToApply}
        isBettingPhase={gameState.gamePhase === 'betting'}
        onBetValueSelect={handleBetValueSelect}
        onSpin={handleSpin}
        balance={gameState.balance}
        pendingBetsOnTypes={pendingBetsOnTypes}
      />
      {/* مربع رمادي أسفل زر الرهان - تفاعلي */}
      <div
        style={{
          position: 'absolute',
          left: '80%',
          top: 'calc(75% + 60px)', // خفض أكثر للأسفل
          transform: 'translate(-50%, -50%)',
          minWidth: 36,
          minHeight: 36,
          width: 36,
          height: 36,
          background: 'linear-gradient(135deg, #666666 0%, #444444 100%)', // خلفية رمادية
          opacity: 0, // إخفاء زر آخر عشر جولات مع الحفاظ على النقر
          borderRadius: 8,
          zIndex: 1001,
          cursor: 'pointer',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          boxShadow: '0 2px 8px #0002',
        }}
        title="عرض آخر مشاركات اللاعب"
        onClick={() => setShowPlayerBetsPopup(true)}
      >
        <span style={{ color: '#ffffff', fontSize: 18, fontWeight: 900 }}>🕑</span>
      </div>

      {/* نافذة منبثقة لعرض آخر 10 مراهنات */}
      {showPlayerBetsPopup && (
        <div
          style={{
            position: 'fixed',
            left: '50%',
            top: '50%',
            transform: 'translate(-50%, -50%)',
            background: '#FFF8E1',
            color: '#222',
            borderRadius: 18,
            border: '4px solid #FFD700',
            boxShadow: '0 6px 32px #0004',
            zIndex: 20000,
            minWidth: 180,
            minHeight: 340,
            maxWidth: 340,
            maxHeight: 600,
            padding: '0 18px 32px 18px',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            overflow: 'hidden',
          }}
        >
          <button
            onClick={() => setShowPlayerBetsPopup(false)}
            style={{ position: 'absolute', top: 10, right: 18, background: 'none', border: 'none', color: '#d32f2f', fontSize: 28, cursor: 'pointer', fontWeight: 900, lineHeight: 1 }}
            title="إغلاق"
          >×</button>
          <div style={{
            fontWeight: 700,
            fontSize: 12,
            color: '#8d6e3c',
            letterSpacing: 0.5,
            textAlign: 'center',
            position: 'absolute',
            top: 10,
            left: 0,
            right: 0,
            margin: 0,
            padding: 0,
            alignSelf: 'center',
            zIndex: 2,
          }}>آخر 10 مشاركات</div>
          {playerBetsLog.filter(entry => Date.now() - entry.time < 60 * 60 * 1000).length === 0 ? (
            <div style={{ color: '#B8860B', fontSize: 16, margin: 18, textAlign: 'center' }}>لا يوجد مشاركات</div>
          ) : (
            <div style={{
              width: '100%',
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              gap: 18,
              fontWeight: 900,
              fontSize: 16,
              color: '#222',
              fontFamily: 'Cairo, Tahoma, Arial',
              letterSpacing: 0.5,
              marginTop: 8,
            }}>
              {playerBetsLog
                .filter(entry => Date.now() - entry.time < 60 * 60 * 1000)
                .slice(0, 10)
                .map((entry, idx, arr) => (
                  <span key={idx} style={{
                    color: '#222',
                    fontWeight: 900,
                    fontSize: 16,
                    opacity: 0.95,
                  }}>
                    {entry.bet.toLocaleString()}{idx < arr.length - 1 ? <span style={{ color: '#B8860B', fontWeight: 700, margin: '0 6px' }}>-</span> : ''}
                  </span>
                ))}
            </div>
          )}
        </div>
      )}

      {/* شريط التاريخ - موضوعة بين الأيقونات الأربع */}
      <div style={{ 
        ...deviceConfig?.historyBar,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center'
      }}>
        <HistoryBox results={gameHistory.map(arr => ({ symbols: arr }))} />
      </div>



      {/* حاوية تاريخ اللعبة */}
      <div style={{ marginTop: '120px', marginBottom: '40px', width: '100%', display: 'flex', justifyContent: 'center', background: 'rgba(255,0,0,0.2)', border: '2px solid red', minHeight: 80 }}>
        <span style={{color: 'red', fontWeight: 'bold'}}>هنا يجب أن تظهر حاوية تاريخ اللعبة</span>
        <GameHistory gameHistory={gameHistory} />
      </div>

      {/* رسائل التنبيه */}
      {showPhaseAlert && (
        <div style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          zIndex: 100,
          background: 'linear-gradient(145deg, rgba(0, 0, 0, 0.95), rgba(20, 20, 20, 0.95))',
          color: '#FFD700',
          padding: '20px 30px',
          borderRadius: '15px',
          border: '3px solid #8B4513',
          boxShadow: '0 10px 30px rgba(0, 0, 0, 0.7), 0 0 50px rgba(255, 215, 0, 0.3)',
          fontSize: '18px',
          fontWeight: 'bold',
          textAlign: 'center',
          maxWidth: '80vw',
          animation: 'fadeInScale 0.3s ease-out',
        }}>
          {phaseAlertMessage}
        </div>
      )}

      {/* بطاقة النتيجة المنبثقة */}
      {showResultCard && (
        <div
          style={{
            position: 'fixed',
            left: 0,
            top: '50%',
            width: '100vw',
            height: '44vh',
            maxWidth: '100vw',
            minWidth: 320,
            minHeight: 180,
            transform: 'translateY(-50%)',
            background: 'linear-gradient(135deg, #2d0036 60%, #4b006e 100%)',
            color: '#fff',
            borderRadius: 32,
            border: '8px solid',
            borderImage: 'linear-gradient(120deg, #FFD700 10%, #fffbe6 40%, #b8860b 60%, #FFD700 90%) 1',
            boxShadow: '0 0 0 8px #fffbe633, 0 8px 32px #000a, 0 0 32px #FFD70055',
            zIndex: 30000,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'flex-start',
            padding: 0,
            overflow: 'hidden',
          }}
        >
          <button
            onClick={() => setShowResultCard(false)}
            style={{ position: 'absolute', top: 24, right: 32, background: 'none', border: 'none', color: '#ff4444', fontSize: 38, cursor: 'pointer', fontWeight: 900, lineHeight: 1 }}
            title="إغلاق"
          >×</button>
          <div style={{ display: 'flex', flexDirection: 'row', gap: 18, justifyContent: 'center', alignItems: 'center', marginTop: 24, marginBottom: 12 }}>
            {(gameState.collectedSymbols && gameState.collectedSymbols.length > 0) ? (
              gameState.collectedSymbols.map((result, idx) => {
                const match = result.match(/^([A-Z]+)(?: x(\d+))?$/i);
                const symbol = match ? match[1] : result;
                const multiplier = match && match[2] ? match[2] : null;
                const isBar = symbol.toUpperCase() === 'BAR';
                const getSymbolImage = (symbol: string) => {
                  switch (symbol.toUpperCase()) {
                    case 'APPLE': return '/images/3.png';
                    case 'BANANA': return '/images/6.png';
                    case 'LEMON': return '/images/8.png';
                    case 'WATERMELON': return '/images/12.png';
                    case 'BAR': return '/images/30.png';
                    default: return '';
                  }
                };
                const imgSrc = getSymbolImage(symbol);
                return (
                  <div key={idx} style={{
                    minWidth: 56,
                    minHeight: 56,
                    width: 56,
                    height: 56,
                    background: '#4a267a',
                    borderRadius: 12,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontWeight: 'bold',
                    fontSize: 18,
                    color: '#fff',
                    border: 'none',
                    boxShadow: 'none',
                    position: 'relative',
                    overflow: 'hidden',
                    padding: 0,
                  }}>
                    {isBar ? (
                      <div style={{
                        width: 42,
                        height: 42,
                        background: '#8B5C2A',
                        borderRadius: 8,
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontWeight: 'bold',
                        fontSize: 13,
                        color: '#FFD700',
                        lineHeight: 1.1,
                        textAlign: 'center',
                        boxShadow: 'none',
                      }}>
                        {'BAR'}<br />{'BAR'}<br />{'BAR'}
                      </div>
                    ) : (
                      imgSrc ? (
                        <img src={imgSrc} alt={symbol} style={{ width: 42, height: 42, objectFit: 'contain' }} />
                      ) : (
                        <span>{symbol}</span>
                      )
                    )}
                    {multiplier && !isBar && (
                      <span style={{
                        position: 'absolute',
                        bottom: 4,
                        left: '50%',
                        transform: 'translateX(-50%)',
                        background: '#FFD700',
                        color: '#3a1859',
                        borderRadius: '50%',
                        fontSize: 14,
                        fontWeight: 900,
                        width: 22,
                        height: 22,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        border: 'none',
                        boxShadow: 'none',
                        lineHeight: 1,
                        padding: 0,
                      }}>
                        x{multiplier}
                      </span>
                    )}
                  </div>
                );
              })
            ) : (
              <span style={{ color: '#FFD700', fontSize: 22 }}>لا توجد نتيجة</span>
            )}
          </div>
          {/* تفاصيل الفوز أو نص عدم المشاركة */}
          {didParticipate ? (
            <div style={{ fontSize: 22, color: '#ff6a00', fontWeight: 900, textShadow: '0 1px 8px #ffb300', marginTop: 8 }}>
              {gameState.totalWinAmount > 0
                ? gameState.totalWinAmount.toLocaleString()
                : (lastResultMessages && lastResultMessages.length > 0
                  ? lastResultMessages[0]
                  : 'لم تربح في هذه الجولة')}
            </div>
          ) : (
            <div style={{ fontSize: 14, color: '#FFD700', marginTop: 8 }}>لم تشارك في هذه الجولة</div>
          )}

          {/* خط فاصل ذهبي مزخرف */}
          <div style={{
            width: '60%',
            height: 0,
            borderBottom: '3px solid',
            borderImage: 'linear-gradient(90deg, #FFD700 0%, #fffbe6 50%, #FFD700 100%) 1',
            margin: '18px auto 8px auto',
            boxShadow: '0 2px 8px #FFD70055',
            borderRadius: 2,
          }} />

          {/* عنوان الفائز الأكبر */}
          <div style={{
            textAlign: 'center',
            color: '#FFD700',
            fontWeight: 900,
            fontSize: 17,
            letterSpacing: 1,
            marginBottom: 2,
            textShadow: '0 1px 8px #fffbe6',
          }}>
            الفائز الأكبر
          </div>

          {/* شريط أعلى ثلاث لاعبين */}
          <div style={{
            position: 'absolute',
            bottom: 38,
            left: '50%',
            transform: 'translateX(-50%)',
            display: 'flex',
            flexDirection: 'row',
            gap: 32,
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: 15,
            color: '#FFD700',
            fontWeight: 700,
            letterSpacing: 0.5,
            zIndex: 1,
          }}>
            {getTopThreePlayers().map((player, idx) => (
              <div key={idx} style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                <span style={{ fontSize: 16, color: player.name === 'أنت' ? '#00e676' : idx === 0 ? '#FFD700' : idx === 1 ? '#C0C0C0' : '#CD7F32', fontWeight: 900 }}>{player.name}</span>
                <span style={{ fontSize: 15, color: '#ff6a00', fontWeight: 900 }}>{player.amount.toLocaleString()}</span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* منطقة التحكم في الرهانات - في الأسفل */}
      <BettingControls
        balance={gameState.balance}
        betsOnTypes={gameState.betsOnTypes}
        pendingBetsOnTypes={pendingBetsOnTypes}
        currentBetValueToApply={gameState.currentBetValueToApply}
        isBettingPhase={gameState.gamePhase === 'betting'}
        onBetValueSelect={handleBetValueSelect}
        onSymbolBet={handleSymbolBet}
        onSpin={handleSpin}
      />

      {/* أدوات التطوير */}
      {/* <PerformanceMonitor
        isVisible={true}
        position="top-right"
      /> */}
    </div>
  );
};

export default App;