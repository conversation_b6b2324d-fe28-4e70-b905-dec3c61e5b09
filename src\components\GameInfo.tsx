import React from 'react';
import { COLORS } from '../theme/colors';

interface GameInfoProps {
  balance: number;
  totalBet: number;
}

const GameInfo: React.FC<GameInfoProps> = ({ balance, totalBet }) => {
  return (
    <>
      <style>{`
        @keyframes goldShimmer {
          0% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
          100% { background-position: 0% 50%; }
        }
        @keyframes goldGlow {
          0% {
            box-shadow:
              0 0 20px rgba(255, 215, 0, 0.6),
              0 0 40px rgba(255, 215, 0, 0.4),
              inset 0 2px 4px rgba(255, 255, 255, 0.3),
              inset 0 -2px 4px rgba(0, 0, 0, 0.2);
          }
          50% {
            box-shadow:
              0 0 30px rgba(255, 215, 0, 0.8),
              0 0 60px rgba(255, 215, 0, 0.6),
              inset 0 2px 4px rgba(255, 255, 255, 0.4),
              inset 0 -2px 4px rgba(0, 0, 0, 0.3);
          }
          100% {
            box-shadow:
              0 0 20px rgba(255, 215, 0, 0.6),
              0 0 40px rgba(255, 215, 0, 0.4),
              inset 0 2px 4px rgba(255, 255, 255, 0.3),
              inset 0 -2px 4px rgba(0, 0, 0, 0.2);
          }
        }
      `}</style>
    <div className="flex justify-center w-full gap-4 px-4">

      {/* حاوية الرصيد */}
      <div className="p-2 rounded-lg"
           style={{
             position: 'relative',
             zIndex: 20,
             minHeight: '32px',
             width: '160px',
             minWidth: '160px',
             maxWidth: '160px',
             background: 'linear-gradient(135deg, #808080 0%, #696969 25%, #808080 50%, #696969 75%, #808080 100%)',
             backgroundSize: '200% 200%',
             animation: 'grayShimmer 3s ease-in-out infinite',
             border: '3px solid #808080',
             boxShadow: `
             0 0 20px rgba(128, 128, 128, 0.6),
             0 0 40px rgba(128, 128, 128, 0.4),
             inset 0 2px 4px rgba(255, 255, 255, 0.3),
             inset 0 -2px 4px rgba(0, 0, 0, 0.2)
             `,
           }}>

        <div className="flex items-center justify-center gap-3">
          {/* كلمة رصيدك */}
          <span className="font-bold text-sm"
                style={{
                  color: '#333',
                  textShadow: '1px 1px 2px rgba(255,255,255,0.8), 0 0 8px rgba(128,128,128,0.6)',
                  fontSize: '14px',
                  fontWeight: '800'
                }}>رصيدك</span>
          {/* أيقونة العملة */}
          <div className="w-4 h-4 rounded-full flex items-center justify-center border-2"
               style={{
                 background: 'linear-gradient(135deg, #808080 0%, #696969 100%)',
                 border: '2px solid #555',
                 boxShadow: '0 0 8px rgba(128,128,128,0.6)'
               }}>
            <span style={{fontSize: '10px'}}>🪙</span>
          </div>
          {/* الرقم */}
          <span className="font-bold text-lg"
                style={{
                  color: '#333',
                  textShadow: '1px 1px 2px rgba(255,255,255,0.8), 0 0 8px rgba(128,128,128,0.6)',
                  fontSize: '16px',
                  fontWeight: '800'
                }}>{balance.toLocaleString()}</span>
        </div>
      </div>

      {/* حاوية الرهان */}
      <div className="p-2 rounded-lg"
           style={{
             position: 'relative',
             zIndex: 20,
             minHeight: '32px',
             width: '160px',
             minWidth: '160px',
             maxWidth: '160px',
             background: 'linear-gradient(135deg, #808080 0%, #696969 25%, #808080 50%, #696969 75%, #808080 100%)',
             backgroundSize: '200% 200%',
             animation: 'grayShimmer 3s ease-in-out infinite',
             border: '3px solid #808080',
             boxShadow: `
             0 0 20px rgba(128, 128, 128, 0.6),
             0 0 40px rgba(128, 128, 128, 0.4),
             inset 0 2px 4px rgba(255, 255, 255, 0.3),
             inset 0 -2px 4px rgba(0, 0, 0, 0.2)
             `,
             }}>
             <div className="flex items-center justify-center gap-3" style={{ marginLeft: '-16px' }}>
             {/* كلمة الرهان */}
             <span className="font-bold text-sm"
             style={{
             color: '#333',
             textShadow: '1px 1px 2px rgba(255,255,255,0.8), 0 0 8px rgba(128,128,128,0.6)',
             fontSize: '14px',
             fontWeight: '800'
             }}>الرهان</span>
             {/* أيقونة العملة */}
             <div className="w-4 h-4 rounded-full flex items-center justify-center border-2"
             style={{
             background: 'linear-gradient(135deg, #808080 0%, #696969 100%)',
             border: '2px solid #555',
             boxShadow: '0 0 8px rgba(128,128,128,0.6)'
             }}>
             <span style={{fontSize: '10px'}}>🪙</span>
             </div>
          {/* الرقم */}
          <span className="font-bold text-lg"
                style={{
                  color: '#333',
                  textShadow: '1px 1px 2px rgba(255,255,255,0.8), 0 0 8px rgba(128,128,128,0.6)',
                  fontSize: '16px',
                  fontWeight: '800'
                }}>{totalBet.toLocaleString()}</span>
        </div>
      </div>
    </div>
    </>
  );
};

export default GameInfo;