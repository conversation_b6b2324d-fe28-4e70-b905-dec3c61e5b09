# 📱 إعداد Samsung Galaxy (360x740)

## ✅ تم إضافة المقاس الجديد

### **تاريخ الإضافة:**
*${new Date().toLocaleDateString('ar-SA')}*

## 🎯 المقاس الجديد

### **المعلومات الأساسية:**
- **الجهاز**: Samsung Galaxy (360x740)
- **المقاس**: 360×740 بكسل
- **النسبة**: 18.5:9 (أكثر طولاً)
- **الخلفية المطلوبة**: `bg-360x740.webp`

## 📐 تفاصيل التخطيط

### **1. معلومات الجهاز:**
```javascript
device: {
  name: "Samsung Galaxy (360x740)",
  width: 360,
  height: 740
}
```

### **2. شب<PERSON>ة اللعبة:**
```javascript
gameBoard: {
  left: '50%',
  top: '40%',
  width: 'min(220px, 85vw)',
  height: 'min(200px, 60vh)',
  transform: 'translateX(-50%)',
  position: 'absolute',
  zIndex: 10,
}
```

### **3. مواقع الأزرار:**
- **BAR**: `left: '10%', top: '65%'`
- **WATERMELON**: `left: '30%', top: '65%'`
- **LEMON**: `left: '50%', top: '65%'`
- **BANANA**: `left: '70%', top: '65%'`
- **APPLE**: `left: '90%', top: '65%'`

### **4. أزرار المبالغ:**
- **5000**: `left: '8%', top: '75%'`
- **3000**: `left: '20%', top: '75%'`
- **1000**: `left: '32%', top: '75%'`
- **500**: `left: '44%', top: '75%'`
- **100**: `left: '56%', top: '75%'`

### **5. خانات الرهان والرصيد:**
```javascript
betNumbers: {
  "total_bets": { "left": "90%", "top": "12%", "info": "TOTAL_BETS" },
  "current_bet": { "left": "80%", "top": "12%", "info": "CURRENT_BET" },
  "last_win": { "left": "80%", "top": "80%", "info": "LAST_WIN" }
},

topDisplays: {
  "balanceDisplay": { "left": "31%", "top": "12%", "name": "BALANCE" },
  "totalBetDisplay": { "left": "89%", "top": "12%", "name": "TOTAL_BET" }
}
```

## 🖼️ الخلفية المطلوبة

### **الملف المطلوب:**
- **اسم الملف**: `bg-360x740.webp`
- **المقاس**: 360×740 بكسل
- **النوع**: WebP محسن
- **الموقع**: `public/images/bg-360x740.webp`

### **هل يجب إنشاء الخلفية؟**
**نعم!** يجب إنشاء خلفية بهذا المقاس لأن:
- ✅ **الكنسل يختار هذا المقاس** تلقائياً
- ✅ **هو مقاس شائع** لهواتف Samsung
- ✅ **سيحسن تجربة المستخدم** بشكل كبير

## 🔧 كيفية إنشاء الخلفية

### **الخيارات المتاحة:**

#### **1. إنشاء من الصورة الأصلية:**
```bash
# باستخدام ImageMagick
convert 222.jpg -resize 360x740^ -gravity center -extent 360x740 bg-360x740.webp

# أو باستخدام FFmpeg
ffmpeg -i 222.jpg -vf "scale=360:740:force_original_aspect_ratio=decrease,pad=360:740:(ow-iw)/2:(oh-ih)/2" bg-360x740.webp
```

#### **2. استخدام أداة تحرير الصور:**
- **Photoshop**: File → Export As → WebP
- **GIMP**: File → Export As → WebP
- **Online Tools**: مثل TinyPNG أو Squoosh

#### **3. استخدام أدوات الويب:**
- **Squoosh.app** (Google)
- **TinyPNG.com**
- **Convertio.co**

## 📱 مقارنة المقاسات

### **Galaxy S8 vs Galaxy (360x740):**
| الخاصية | Galaxy S8 | Galaxy (360x740) |
|---------|-----------|------------------|
| **العرض** | 412px | 360px |
| **الارتفاع** | 915px | 740px |
| **النسبة** | 18.5:9 | 18.5:9 |
| **شبكة اللعبة** | 242×237px | 220×200px |
| **الخلفية** | bg-412x915.webp | bg-360x740.webp |

## 🎯 النتيجة المتوقعة

### **بعد إنشاء الخلفية:**
- ✅ **الكنسل سيعرض** الخلفية الصحيحة
- ✅ **الأزرار في المواقع** المثالية
- ✅ **شبكة اللعبة** محسنة للمقاس
- ✅ **خانات الرهان والرصيد** مرفوعة (12%)

## 🔧 الملفات المحدثة

### **1. `src/utils/multiDeviceBackground.ts`:**
- ✅ إضافة تكوين Galaxy (360x740)
- ✅ تحديث مواقع الأزرار
- ✅ تحديد الخلفية المطلوبة

### **2. `src/utils/buttonPositions.ts`:**
- ✅ إضافة تكوين كامل لـ 360x740
- ✅ تحديث جميع العناصر
- ✅ إعدادات محسنة للمقاس

### **3. `GALAXY_360x740_SETUP.md`:**
- ✅ هذا الملف - توثيق الإعداد

## 📋 الخطوات التالية

### **1. إنشاء الخلفية:**
```bash
# انسخ الصورة الأصلية
cp public/images/222.jpg public/images/bg-360x740.jpg

# حولها لـ WebP
convert public/images/bg-360x740.jpg -resize 360x740^ -gravity center -extent 360x740 public/images/bg-360x740.webp
```

### **2. اختبار التخطيط:**
1. **افتح اللعبة** في المتصفح
2. **اضبط الحجم** لـ `360×740`
3. **تحقق من الخلفية** - يجب أن تكون `bg-360x740.webp`
4. **تحقق من الأزرار** - يجب أن تكون في المواقع الصحيحة

### **3. ضبط المواقع (إذا احتجت):**
- "حرك الأزرار للأسفل قليلاً"
- "كبر شبكة اللعبة"
- "ضبط خانات الرهان"

## 🎯 النتيجة النهائية

بعد إنشاء الخلفية، Galaxy (360x740) سيعمل بشكل مثالي:
- 🖼️ **الخلفية الصحيحة** - `bg-360x740.webp`
- 📐 **المقاس الصحيح** - 360×740 بكسل
- 🎮 **الأزرار في المواقع المثالية**
- 📊 **خانات الرهان والرصيد مرفوعة**
- 🎨 **تصميم متناسق** مع الخلفية

---
*جاهز لإنشاء الخلفية* 🎨 