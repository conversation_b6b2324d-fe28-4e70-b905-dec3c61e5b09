import React from 'react';
import { BUTTON_POSITIONS } from '../utils/buttonPositions';

interface iPhone12PreviewProps {
  isVisible: boolean;
  onToggle: () => void;
}

const iPhone12Preview: React.FC<iPhone12PreviewProps> = ({
  isVisible,
  onToggle
}) => {
  // البحث عن تكوين iPhone 12
  const iPhone12Config = BUTTON_POSITIONS.find(d => d.device.name === 'iPhone 12/13/14');

  if (!iPhone12Config) {
    return null;
  }

  if (!isVisible) {
    return (
      <button
        onClick={onToggle}
        style={{
          position: 'fixed',
          top: '90px',
          right: '10px',
          zIndex: 9999,
          background: 'rgba(100, 0, 200, 0.8)',
          color: '#fff',
          border: '1px solid #6600cc',
          borderRadius: '4px',
          padding: '8px 12px',
          cursor: 'pointer',
          fontSize: '12px',
        }}
      >
        👁️ iPhone 12 Preview
      </button>
    );
  }

  return (
    <div style={{
      position: 'fixed',
      top: '20px',
      left: '20px',
      width: '300px',
      height: '600px',
      background: 'rgba(0, 0, 0, 0.95)',
      border: '2px solid #333',
      borderRadius: '20px',
      zIndex: 9998,
      overflow: 'hidden',
    }}>
      {/* شريط العنوان */}
      <div style={{
        padding: '10px',
        background: 'rgba(255, 255, 255, 0.1)',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        borderBottom: '1px solid #333',
      }}>
        <span style={{ color: '#fff', fontSize: '14px', fontWeight: 'bold' }}>
          iPhone 12 Preview (390×844)
        </span>
        <button
          onClick={onToggle}
          style={{
            background: 'none',
            border: 'none',
            color: '#ff4444',
            cursor: 'pointer',
            fontSize: '16px',
          }}
        >
          ✕
        </button>
      </div>

      {/* محاكي الشاشة */}
      <div style={{
        width: '280px',
        height: '520px',
        margin: '10px auto',
        background: 'linear-gradient(135deg, #1a1a2e, #16213e)',
        borderRadius: '15px',
        position: 'relative',
        overflow: 'hidden',
        transform: 'scale(0.7)',
        transformOrigin: 'top center',
      }}>
        {/* الخلفية */}
        <div style={{
          width: '100%',
          height: '100%',
          background: 'linear-gradient(45deg, #0f3460, #0e4b99)',
          position: 'relative',
        }}>
          {/* عرض الرصيد */}
          <div style={{
            position: 'absolute',
            left: iPhone12Config.topDisplays.balanceDisplay.left,
            top: iPhone12Config.topDisplays.balanceDisplay.top,
            transform: 'translate(-50%, 0)',
            color: '#fff',
            fontSize: '12px',
            fontWeight: 'bold',
            background: 'rgba(0, 0, 0, 0.5)',
            padding: '4px 8px',
            borderRadius: '4px',
          }}>
            1,000,000
          </div>

          {/* عرض إجمالي الرهان */}
          <div style={{
            position: 'absolute',
            left: iPhone12Config.topDisplays.totalBetDisplay.left,
            top: iPhone12Config.topDisplays.totalBetDisplay.top,
            transform: 'translate(-50%, 0)',
            color: '#fff',
            fontSize: '12px',
            fontWeight: 'bold',
            background: 'rgba(0, 0, 0, 0.5)',
            padding: '4px 8px',
            borderRadius: '4px',
          }}>
            15,000
          </div>

          {/* لوحة اللعبة */}
          <div style={{
            position: 'absolute',
            left: '50%',
            top: '32%',
            width: '200px',
            height: '140px',
            transform: 'translateX(-50%)',
            background: 'rgba(255, 255, 255, 0.1)',
            border: '1px solid rgba(255, 255, 255, 0.3)',
            borderRadius: '8px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'rgba(255, 255, 255, 0.7)',
            fontSize: '10px',
          }}>
            Game Board
          </div>

          {/* أزرار الفواكه */}
          {Object.entries(iPhone12Config.symbolButtons).map(([key, pos]) => (
            <div
              key={key}
              style={{
                position: 'absolute',
                left: pos.left,
                top: pos.top,
                width: '30px',
                height: '30px',
                background: '#4ecdc4',
                border: '1px solid #fff',
                borderRadius: '6px',
                transform: 'translate(-50%, -50%)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '6px',
                fontWeight: 'bold',
                color: '#fff',
              }}
            >
              {key.substring(0, 3).toUpperCase()}
            </div>
          ))}

          {/* مربعات عرض الرهانات */}
          {Object.entries(iPhone12Config.betRectangles).map(([key, pos]) => (
            <div
              key={`bet-${key}`}
              style={{
                position: 'absolute',
                left: pos.left,
                top: pos.top,
                transform: 'translate(-50%, -50%)',
                background: 'rgba(255, 215, 0, 0.8)',
                color: '#000',
                fontSize: '6px',
                fontWeight: 'bold',
                padding: '2px 4px',
                borderRadius: '3px',
                minWidth: '20px',
                textAlign: 'center',
              }}
            >
              1.5k
            </div>
          ))}

          {/* أزرار المبالغ */}
          {Object.entries(iPhone12Config.amountButtons).map(([key, pos]) => (
            <div
              key={key}
              style={{
                position: 'absolute',
                left: pos.left,
                top: pos.top,
                width: '20px',
                height: '20px',
                background: '#66bb6a',
                border: '1px solid #fff',
                borderRadius: '50%',
                transform: 'translate(-50%, -50%)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '5px',
                fontWeight: 'bold',
                color: '#fff',
              }}
            >
              {pos.value ? (pos.value >= 1000 ? `${pos.value/1000}k` : pos.value) : key.slice(-1)}
            </div>
          ))}
        </div>
      </div>

      {/* معلومات إضافية */}
      <div style={{
        padding: '10px',
        color: '#ccc',
        fontSize: '10px',
        lineHeight: '1.4',
      }}>
        <div><strong>الأبعاد:</strong> 390×844 بكسل</div>
        <div><strong>الأزرار:</strong> {Object.keys(iPhone12Config.symbolButtons).length} فاكهة + {Object.keys(iPhone12Config.amountButtons).length} مبلغ</div>
        <div><strong>الحالة:</strong> معاينة مباشرة</div>
      </div>
    </div>
  );
};

export default iPhone12Preview;
